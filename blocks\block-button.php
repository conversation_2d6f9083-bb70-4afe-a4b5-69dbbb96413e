<?php
use Carbon_Fields\Block;
use Carbon_Fields\Field;

add_action( 'carbon_fields_register_fields', 'everest_register_button_block' );
function everest_register_button_block() {
    Block::make( __( 'Xbees Button' ) )
        ->add_fields( array(
            Field::make( 'text', 'btn_title', __( 'Button Title' ) )
                ->set_required( true ),
            Field::make( 'text', 'btn_url', __( 'Button URL' ) )
                ->set_required( true ),
            Field::make( 'text', 'btn_width', __( 'Button Width (px)' ) )
                ->set_attribute( 'type', 'number' )
                ->set_attribute( 'min', '0' )
                ->set_help_text( 'Leave empty for auto width.' ),
            Field::make( 'select', 'btn_align', __( 'Button Alignment' ) )
                ->set_options( array(
                    'left' => __( 'Left' ),
                    'center' => __( 'Center' ),
                    'right' => __( 'Right' ),
                ) )
                ->set_default_value( 'left' ),
            Field::make( 'text', 'btn_font_size', __( 'Button Font Size (px)' ) )
                ->set_attribute( 'type', 'number' )
                ->set_attribute( 'min', '1' )
                ->set_default_value( '15' ),
        ) )
        ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
        ->set_icon( 'no' )
        ->set_mode( 'preview' )
        ->set_render_callback( function ( $fields, $attributes, $inner_blocks ) {
            $btn_title = esc_html( $fields['btn_title'] );
            $btn_url = esc_url( $fields['btn_url'] );
            $btn_width = isset( $fields['btn_width'] ) && intval( $fields['btn_width'] ) > 10 ? intval( $fields['btn_width'] ) . 'px' : 'auto';
            $btn_align = isset( $fields['btn_align'] ) ? esc_attr( $fields['btn_align'] ) : 'left';
            $btn_font_size = isset( $fields['btn_font_size'] ) ? intval( $fields['btn_font_size'] ) . 'px' : '15px';
            ?>
            <div class="custom-button-block" style="text-align: <?php echo $btn_align; ?>;">
                <a class="btn-one" href="<?php echo $btn_url; ?>" style="width: <?php echo $btn_width; ?>; font-size: <?php echo $btn_font_size; ?>;">
                    <?php echo $btn_title; ?>
                </a>
            </div>
            <?php
        } );
}

