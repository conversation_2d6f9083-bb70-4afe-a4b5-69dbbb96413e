msgid ""
msgstr ""
"Project-Id-Version: Carbon Fields\n"
"POT-Creation-Date: 2019-01-02 15:20+0200\n"
"PO-Revision-Date: 2020-11-05 15:38+0200\n"
"Last-Translator: DieGOs <<EMAIL>>\n"
"Language-Team: Russian\n"
"Language: ru_RU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.4.1\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10 >= 2 && n"
"%10<=4 &&(n%100<10||n%100 >= 20)? 1 : 2);\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: languages\n"
"X-Poedit-SearchPathExcluded-2: node_modules\n"
"X-Poedit-SearchPathExcluded-3: tests\n"
"X-Poedit-SearchPathExcluded-4: tmp\n"
"X-Poedit-SearchPathExcluded-5: vendor\n"

#: core/Container/Block_Container.php:259
msgid "'render_callback' is required for the blocks."
msgstr "'render_callback' обязателен для блоков."

#: core/Container/Block_Container.php:263
msgid "'render_callback' must be a callable."
msgstr "'render_callback' должен быть вызываемым."

#: core/Container/Container.php:712
msgid "General"
msgstr "Основные"

#: core/Container/Theme_Options_Container.php:207
msgid "Settings saved."
msgstr "Настройки сохранены"

#: core/Field/Footer_Scripts_Field.php:20
msgid ""
"If you need to add scripts to your footer (like Google Analytics tracking "
"code), you should enter them in this box."
msgstr ""
"Если в футер необходимо добавить скрипты (код Google Analytics), их нужно "
"указать здесь."

#: core/Field/Gravity_Form_Field.php:47
msgid "No form"
msgstr "Нет формы"

#: core/Field/Header_Scripts_Field.php:20
msgid "If you need to add scripts to your header, you should enter them here."
msgstr "Если в заголовок необходимо добавить скрипты, их нужно указать здеcь."

#: core/Field/Sidebar_Field.php:74
msgctxt "sidebar"
msgid "Add New"
msgstr "Добавить новый"

#: core/Helper/Helper.php:612
msgid "F j, Y"
msgstr ""

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:69
msgid "Please enter a name for the sidebar."
msgstr "Пожалуйста, введите название для сайдбара."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:72
msgid "Unknown action attempted."
msgstr "Неизвестная операция."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:102
msgid "Sidebar with the same ID is already registered."
msgstr "Сайдбар с этим ID уже зарегистрирован."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:112
#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:142
msgid ""
"Failed to update option storing your custom sidebars. Please contact support."
msgstr ""
"Не удалось обновить параметры вашего сайдбара. Обратитесь в службу поддержки."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:137
msgid "Sidebar not found."
msgstr "Сайдбар не найден."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:181
msgid "Add Sidebar"
msgstr "Добавить сайдбар"

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:182
msgid "Please enter the name of the new sidebar:"
msgstr "Пожалуйста, введите название для сайдбара."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:183
msgid "Are you sure you wish to remove this sidebar?"
msgstr "Действительно удалить сайдбар?"

#: core/REST_API/Router.php:341
msgid "No option names provided"
msgstr "Не указаны имена опций"

#: core/REST_API/Router.php:352
msgid "Theme Options updated."
msgstr "Опции темы обновлены."

#: templates/Container/common/options-page.php:43
msgid "Actions"
msgstr "Действия"

#: templates/Container/common/options-page.php:52
msgid "Save Changes"
msgstr "Сохранить изменения"

#: templates/Container/widget.php:4
msgid "No options are available for this widget."
msgstr "Нет доступных настроек для этого виджета."
