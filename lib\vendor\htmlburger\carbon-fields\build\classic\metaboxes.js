this["cf"] = this["cf"] || {}; this["cf"]["metaboxes"] =
/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./packages/metaboxes/index.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./node_modules/@babel/runtime/helpers/arrayLikeToArray.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/arrayLikeToArray.js ***!
  \*****************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nmodule.exports = _arrayLikeToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/arrayLikeToArray.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/arrayWithHoles.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/arrayWithHoles.js ***!
  \***************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nmodule.exports = _arrayWithHoles, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/arrayWithHoles.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js":
/*!******************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js ***!
  \******************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var arrayLikeToArray = __webpack_require__(/*! ./arrayLikeToArray.js */ \"./node_modules/@babel/runtime/helpers/arrayLikeToArray.js\");\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return arrayLikeToArray(arr);\n}\nmodule.exports = _arrayWithoutHoles, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/assertThisInitialized.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/assertThisInitialized.js ***!
  \**********************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nmodule.exports = _assertThisInitialized, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/assertThisInitialized.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/classCallCheck.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/classCallCheck.js ***!
  \***************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nmodule.exports = _classCallCheck, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/classCallCheck.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/createClass.js":
/*!************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/createClass.js ***!
  \************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var toPropertyKey = __webpack_require__(/*! ./toPropertyKey.js */ \"./node_modules/@babel/runtime/helpers/toPropertyKey.js\");\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nmodule.exports = _createClass, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/createClass.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/defineProperty.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/defineProperty.js ***!
  \***************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var toPropertyKey = __webpack_require__(/*! ./toPropertyKey.js */ \"./node_modules/@babel/runtime/helpers/toPropertyKey.js\");\nfunction _defineProperty(obj, key, value) {\n  key = toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/defineProperty.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/extends.js":
/*!********************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/extends.js ***!
  \********************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _extends() {\n  module.exports = _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _extends.apply(this, arguments);\n}\nmodule.exports = _extends, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/extends.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/getPrototypeOf.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/getPrototypeOf.js ***!
  \***************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _getPrototypeOf(o) {\n  module.exports = _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _getPrototypeOf(o);\n}\nmodule.exports = _getPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/getPrototypeOf.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/inherits.js":
/*!*********************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/inherits.js ***!
  \*********************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var setPrototypeOf = __webpack_require__(/*! ./setPrototypeOf.js */ \"./node_modules/@babel/runtime/helpers/setPrototypeOf.js\");\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) setPrototypeOf(subClass, superClass);\n}\nmodule.exports = _inherits, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/inherits.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/iterableToArray.js":
/*!****************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/iterableToArray.js ***!
  \****************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nmodule.exports = _iterableToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/iterableToArray.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/iterableToArrayLimit.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/iterableToArrayLimit.js ***!
  \*********************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _iterableToArrayLimit(arr, i) {\n  var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (null != _i) {\n    var _s,\n      _e,\n      _x,\n      _r,\n      _arr = [],\n      _n = !0,\n      _d = !1;\n    try {\n      if (_x = (_i = _i.call(arr)).next, 0 === i) {\n        if (Object(_i) !== _i) return;\n        _n = !1;\n      } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0);\n    } catch (err) {\n      _d = !0, _e = err;\n    } finally {\n      try {\n        if (!_n && null != _i[\"return\"] && (_r = _i[\"return\"](), Object(_r) !== _r)) return;\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n    return _arr;\n  }\n}\nmodule.exports = _iterableToArrayLimit, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/iterableToArrayLimit.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/nonIterableRest.js":
/*!****************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/nonIterableRest.js ***!
  \****************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nmodule.exports = _nonIterableRest, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/nonIterableRest.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/nonIterableSpread.js":
/*!******************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/nonIterableSpread.js ***!
  \******************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nmodule.exports = _nonIterableSpread, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/nonIterableSpread.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js ***!
  \**************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var _typeof = __webpack_require__(/*! ./typeof.js */ \"./node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\nvar assertThisInitialized = __webpack_require__(/*! ./assertThisInitialized.js */ \"./node_modules/@babel/runtime/helpers/assertThisInitialized.js\");\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return assertThisInitialized(self);\n}\nmodule.exports = _possibleConstructorReturn, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/setPrototypeOf.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/setPrototypeOf.js ***!
  \***************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _setPrototypeOf(o, p) {\n  module.exports = _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _setPrototypeOf(o, p);\n}\nmodule.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/setPrototypeOf.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/slicedToArray.js":
/*!**************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/slicedToArray.js ***!
  \**************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var arrayWithHoles = __webpack_require__(/*! ./arrayWithHoles.js */ \"./node_modules/@babel/runtime/helpers/arrayWithHoles.js\");\nvar iterableToArrayLimit = __webpack_require__(/*! ./iterableToArrayLimit.js */ \"./node_modules/@babel/runtime/helpers/iterableToArrayLimit.js\");\nvar unsupportedIterableToArray = __webpack_require__(/*! ./unsupportedIterableToArray.js */ \"./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js\");\nvar nonIterableRest = __webpack_require__(/*! ./nonIterableRest.js */ \"./node_modules/@babel/runtime/helpers/nonIterableRest.js\");\nfunction _slicedToArray(arr, i) {\n  return arrayWithHoles(arr) || iterableToArrayLimit(arr, i) || unsupportedIterableToArray(arr, i) || nonIterableRest();\n}\nmodule.exports = _slicedToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/slicedToArray.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/toConsumableArray.js":
/*!******************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/toConsumableArray.js ***!
  \******************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var arrayWithoutHoles = __webpack_require__(/*! ./arrayWithoutHoles.js */ \"./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js\");\nvar iterableToArray = __webpack_require__(/*! ./iterableToArray.js */ \"./node_modules/@babel/runtime/helpers/iterableToArray.js\");\nvar unsupportedIterableToArray = __webpack_require__(/*! ./unsupportedIterableToArray.js */ \"./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js\");\nvar nonIterableSpread = __webpack_require__(/*! ./nonIterableSpread.js */ \"./node_modules/@babel/runtime/helpers/nonIterableSpread.js\");\nfunction _toConsumableArray(arr) {\n  return arrayWithoutHoles(arr) || iterableToArray(arr) || unsupportedIterableToArray(arr) || nonIterableSpread();\n}\nmodule.exports = _toConsumableArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/toConsumableArray.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/toPrimitive.js":
/*!************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/toPrimitive.js ***!
  \************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var _typeof = __webpack_require__(/*! ./typeof.js */ \"./node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nmodule.exports = _toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/toPrimitive.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/toPropertyKey.js":
/*!**************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/toPropertyKey.js ***!
  \**************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var _typeof = __webpack_require__(/*! ./typeof.js */ \"./node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\nvar toPrimitive = __webpack_require__(/*! ./toPrimitive.js */ \"./node_modules/@babel/runtime/helpers/toPrimitive.js\");\nfunction _toPropertyKey(arg) {\n  var key = toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nmodule.exports = _toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/toPropertyKey.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/typeof.js":
/*!*******************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/typeof.js ***!
  \*******************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return (module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports), _typeof(obj);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/typeof.js?");

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js ***!
  \***************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var arrayLikeToArray = __webpack_require__(/*! ./arrayLikeToArray.js */ \"./node_modules/@babel/runtime/helpers/arrayLikeToArray.js\");\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return arrayLikeToArray(o, minLen);\n}\nmodule.exports = _unsupportedIterableToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js?");

/***/ }),

/***/ "./node_modules/callbag-create/index.js":
/*!**********************************************!*\
  !*** ./node_modules/callbag-create/index.js ***!
  \**********************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("const create = prod => (start, sink) => {\n  if (start !== 0) return;\n  if (typeof prod !== 'function') {\n    sink(0, () => {});\n    sink(2);\n    return;\n  }\n  let end;\n  let unsub;\n  const maybeDispose = t => {\n    end = end || t === 2;\n    if (end && typeof unsub === 'function') unsub();\n  };\n  sink(0, maybeDispose);\n  unsub = prod((t, d) => {\n    if (end || t === 0) return;\n    sink(t, d);\n    maybeDispose(t);\n  });\n};\n\nmodule.exports = create;\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/callbag-create/index.js?");

/***/ }),

/***/ "./node_modules/callbag-distinct-until-changed/dist/callbag-distinct-until-changed.es.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/callbag-distinct-until-changed/dist/callbag-distinct-until-changed.es.js ***!
  \***********************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\nvar is = function is(previous, current) {\n  return previous === current;\n};\n\nfunction distinctUntilChanged(compare) {\n  if (compare === void 0) {\n    compare = is;\n  }\n\n  return function (source) {\n    return function (start, sink) {\n      if (start !== 0) return;\n      var inited = false;\n      var prev;\n      var talkback;\n      source(0, function (type, data) {\n        if (type === 0) {\n          talkback = data;\n        }\n\n        if (type !== 1) {\n          sink(type, data);\n          return;\n        }\n\n        if (inited && compare(prev, data)) {\n          talkback(1);\n          return;\n        }\n\n        inited = true;\n        prev = data;\n        sink(1, data);\n      });\n    };\n  };\n}\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (distinctUntilChanged);\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/callbag-distinct-until-changed/dist/callbag-distinct-until-changed.es.js?");

/***/ }),

/***/ "./node_modules/callbag-filter/readme.js":
/*!***********************************************!*\
  !*** ./node_modules/callbag-filter/readme.js ***!
  \***********************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("/**\n * callbag-filter\n * --------------\n *\n * Callbag operator that conditionally lets data pass through. Works on either\n * pullable or listenable sources.\n *\n * `npm install callbag-filter`\n *\n * Example:\n *\n *     const fromIter = require('callbag-from-iter');\n *     const iterate = require('callbag-iterate');\n *     const filter = require('callbag-filter');\n *\n *     const source = filter(x => x % 2)(fromIter([1,2,3,4,5]));\n *\n *     iterate(x => console.log(x))(source); // 1\n *                                           // 3\n *                                           // 5\n */\n\nconst filter = condition => source => (start, sink) => {\n  if (start !== 0) return;\n  let talkback;\n  source(0, (t, d) => {\n    if (t === 0) {\n      talkback = d;\n      sink(t, d);\n    } else if (t === 1) {\n      if (condition(d)) sink(t, d);\n      else talkback(1);\n    }\n    else sink(t, d);\n  });\n};\n\nmodule.exports = filter;\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/callbag-filter/readme.js?");

/***/ }),

/***/ "./node_modules/callbag-from-delegated-event/index.js":
/*!************************************************************!*\
  !*** ./node_modules/callbag-from-delegated-event/index.js ***!
  \************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var callbag_from_event__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! callbag-from-event */ \"./node_modules/callbag-from-event/index.js\");\n/* harmony import */ var callbag_filter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! callbag-filter */ \"./node_modules/callbag-filter/readme.js\");\n/* harmony import */ var callbag_filter__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(callbag_filter__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nconst fromDelegatedEvent = (root, sel, evt) => callbag_filter__WEBPACK_IMPORTED_MODULE_1___default()(e => {\n  let at = e.target;\n  while(at !== root){\n    if (at.matches(sel)) {\n      return true;\n    }\n    at = at.parentElement;\n  }\n  return false;\n})(Object(callbag_from_event__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(root, evt));\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (fromDelegatedEvent);\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/callbag-from-delegated-event/index.js?");

/***/ }),

/***/ "./node_modules/callbag-from-event/index.js":
/*!**************************************************!*\
  !*** ./node_modules/callbag-from-event/index.js ***!
  \**************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\nconst fromEvent = (node, name, options) => (start, sink) => {\n  if (start !== 0) return;\n  let disposed = false;\n  const handler = ev => {\n    sink(1, ev)\n  };\n\n  sink(0, t => {\n    if (t !== 2) {\n      return;\n    }\n    disposed = true;\n    if (node.removeEventListener) node.removeEventListener(name, handler, options);\n    else if (node.removeListener) node.removeListener(name, handler);\n    else throw new Error('cannot remove listener from node. No method found.');\n  });\n\n  if (disposed) {\n    return;\n  }\n\n  if (node.addEventListener) node.addEventListener(name, handler, options);\n  else if (node.addListener) node.addListener(name, handler);\n  else throw new Error('cannot add listener to node. No method found.');\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (fromEvent);\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/callbag-from-event/index.js?");

/***/ }),

/***/ "./node_modules/callbag-of/dist/callbag-of.esm.js":
/*!********************************************************!*\
  !*** ./node_modules/callbag-of/dist/callbag-of.esm.js ***!
  \********************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\nfunction of() {\n  for (var _len = arguments.length, values = new Array(_len), _key = 0; _key < _len; _key++) {\n    values[_key] = arguments[_key];\n  }\n\n  return function (start, sink) {\n    if (start !== 0) return;\n    var disposed = false;\n    sink(0, function (type) {\n      if (type !== 2) return;\n      disposed = true;\n      values.length = 0;\n    });\n\n    while (values.length !== 0) {\n      sink(1, values.shift());\n    }\n\n    if (disposed) return;\n    sink(2);\n  };\n}\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (of);\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/callbag-of/dist/callbag-of.esm.js?");

/***/ }),

/***/ "./node_modules/callbag-start-with/index.js":
/*!**************************************************!*\
  !*** ./node_modules/callbag-start-with/index.js ***!
  \**************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\nconst startWith = (...xs) => inputSource => (start, outputSink) => {\n  if (start !== 0) return;\n  let disposed = false;\n  let inputTalkback;\n  let trackPull = false;\n  let lastPull;\n\n  outputSink(0, (ot, od) => {\n    if (trackPull && ot === 1) {\n      lastPull = [1, od];\n    }\n\n    if (ot === 2) {\n      disposed = true;\n      xs.length = 0;\n    }\n\n    if (!inputTalkback) return;\n    inputTalkback(ot, od);\n  });\n\n  while (xs.length !== 0) {\n    if (xs.length === 1) {\n      trackPull = true;\n    }\n    outputSink(1, xs.shift());\n  }\n\n  if (disposed) return;\n\n  inputSource(0, (it, id) => {\n    if (it === 0) {\n      inputTalkback = id;\n      trackPull = false;\n\n      if (lastPull) {\n        inputTalkback(...lastPull);\n        lastPull = null;\n      }\n      return;\n    }\n    outputSink(it, id);\n  });\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (startWith);\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/callbag-start-with/index.js?");

/***/ }),

/***/ "./node_modules/callbag-take-until/index.js":
/*!**************************************************!*\
  !*** ./node_modules/callbag-take-until/index.js ***!
  \**************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\nconst UNIQUE = {};\n\nconst takeUntil = notifier => source => (start, sink) => {\n  if (start !== 0) return;\n  let sourceTalkback;\n  let notifierTalkback;\n  let inited = false;\n  let done = UNIQUE;\n\n  source(0, (type, data) => {\n    if (type === 0) {\n      sourceTalkback = data;\n\n      notifier(0, (t, d) => {\n        if (t === 0) {\n          notifierTalkback = d;\n          notifierTalkback(1);\n          return;\n        }\n        if (t === 1) {\n          done = void 0;\n          notifierTalkback(2);\n          sourceTalkback(2);\n          if (inited) sink(2);\n          return;\n        }\n        if (t === 2) {\n          notifierTalkback = null;\n          if (d != null) {\n            done = d;\n            sourceTalkback(2);\n            if (inited) sink(t, d);\n          }\n        }\n      });\n\n      inited = true;\n\n      sink(0, (t, d) => {\n        if (done !== UNIQUE) return;\n        if (t === 2 && notifierTalkback) notifierTalkback(2);\n        sourceTalkback(t, d);\n      });\n\n      if (done !== UNIQUE) sink(2, done);\n      return;\n    }\n\n    if (type === 2 && notifierTalkback) notifierTalkback(2);\n    sink(type, data);\n  });\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (takeUntil);\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./node_modules/callbag-take-until/index.js?");

/***/ }),

/***/ "./packages/metaboxes/components/container/index.js":
/*!**********************************************************!*\
  !*** ./packages/metaboxes/components/container/index.js ***!
  \**********************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(__webpack_provided_wp_dot_element) {/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ \"./node_modules/@babel/runtime/helpers/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/@babel/runtime/helpers/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/@babel/runtime/helpers/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/assertThisInitialized */ \"./node_modules/@babel/runtime/helpers/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"./node_modules/@babel/runtime/helpers/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js\");\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"./node_modules/@babel/runtime/helpers/getPrototypeOf.js\");\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\");\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./style.scss */ \"./packages/metaboxes/components/container/style.scss\");\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_style_scss__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _field__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../field */ \"./packages/metaboxes/components/field/index.js\");\n\n\n\n\n\n\n\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_6___default()(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_6___default()(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_5___default()(this, result); }; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\nvar Container = /*#__PURE__*/function (_Component) {\n  _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4___default()(Container, _Component);\n  var _super = _createSuper(Container);\n  function Container() {\n    var _this;\n    _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1___default()(this, Container);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    /**\r\n     * Local state.\r\n     *\r\n     * @type {Object}\r\n     */\n    _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_3___default()(_this), \"state\", {\n      currentTab: null\n    });\n    /**\r\n     * Renders the given field.\r\n     *\r\n     * @param  {Object} field\r\n     * @return {Object}\r\n     */\n    _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_3___default()(_this), \"renderField\", function (field) {\n      var FieldEdit = Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_11__[\"getFieldType\"])(field.type, 'metabox');\n      if (!FieldEdit) {\n        return null;\n      }\n      return __webpack_provided_wp_dot_element.createElement(_field__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        key: field.id,\n        id: field.id\n      }, __webpack_provided_wp_dot_element.createElement(FieldEdit, {\n        id: field.id,\n        containerId: _this.props.id\n      }));\n    });\n    /**\r\n     * Handles click on the tabs.\r\n     *\r\n     * @param  {string} tab\r\n     * @return {void}\r\n     */\n    _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_3___default()(_this), \"handleTabClick\", function (tab) {\n      _this.setState({\n        currentTab: tab\n      });\n    });\n    return _this;\n  }\n  _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2___default()(Container, [{\n    key: \"componentDidMount\",\n    value:\n    /**\r\n     * Lifecycle hook.\r\n     *\r\n     * @return {void}\r\n     */\n    function componentDidMount() {\n      var container = this.props.container;\n      if (this.isTabbed(container)) {\n        this.setState({\n          currentTab: Object.keys(container.settings.tabs)[0]\n        });\n      }\n    }\n\n    /**\r\n     * Returns whether the container uses tabs.\r\n     *\r\n     * @param  {Object} container\r\n     * @return {boolean}\r\n     */\n  }, {\n    key: \"isTabbed\",\n    value: function isTabbed(container) {\n      return Object(lodash__WEBPACK_IMPORTED_MODULE_10__[\"isPlainObject\"])(container.settings.tabs);\n    }\n  }, {\n    key: \"render\",\n    value:\n    /**\r\n     * Renders the component.\r\n     *\r\n     * @return {Object}\r\n     */\n    function render() {\n      var _this2 = this;\n      var currentTab = this.state.currentTab;\n      var container = this.props.container;\n      var hasTabs = this.isTabbed(container);\n      var classes = classnames__WEBPACK_IMPORTED_MODULE_8___default()(['cf-container', \"cf-container-\".concat(container.id), \"cf-container-\".concat(Object(lodash__WEBPACK_IMPORTED_MODULE_10__[\"kebabCase\"])(container.type))].concat(_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0___default()(container.classes), [_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_7___default()({\n        'cf-container--plain': !hasTabs\n      }, \"cf-container--tabbed cf-container--\".concat(container.layout), hasTabs)]));\n      return __webpack_provided_wp_dot_element.createElement(\"div\", {\n        className: classes\n      }, __webpack_provided_wp_dot_element.createElement(\"input\", {\n        type: \"hidden\",\n        name: container.nonce.name,\n        value: container.nonce.value\n      }), hasTabs && __webpack_provided_wp_dot_element.createElement(\"div\", {\n        className: \"cf-container__tabs cf-container__tabs--\".concat(container.layout)\n      }, __webpack_provided_wp_dot_element.createElement(\"ul\", {\n        className: \"cf-container__tabs-list\"\n      }, Object(lodash__WEBPACK_IMPORTED_MODULE_10__[\"map\"])(container.settings.tabs, function (fieldNames, tabName) {\n        // eslint-disable-next-line no-shadow\n        var classes = classnames__WEBPACK_IMPORTED_MODULE_8___default()('cf-container__tabs-item', {\n          'cf-container__tabs-item--current': tabName === currentTab\n        });\n        return __webpack_provided_wp_dot_element.createElement(\"li\", {\n          key: tabName,\n          className: classes,\n          tabIndex: -1,\n          role: \"tab\",\n          \"aria-selected\": currentTab === tabName\n        }, __webpack_provided_wp_dot_element.createElement(\"button\", {\n          type: \"button\",\n          onClick: function onClick() {\n            return _this2.handleTabClick(tabName);\n          },\n          dangerouslySetInnerHTML: {\n            __html: tabName\n          }\n        }));\n      }))), hasTabs && Object(lodash__WEBPACK_IMPORTED_MODULE_10__[\"map\"])(container.settings.tabs, function (fieldNames, tabName) {\n        return __webpack_provided_wp_dot_element.createElement(\"div\", {\n          className: \"cf-container__fields\",\n          key: tabName,\n          hidden: tabName !== currentTab\n        }, Object(lodash__WEBPACK_IMPORTED_MODULE_10__[\"map\"])(fieldNames, function (fieldName) {\n          var field = Object(lodash__WEBPACK_IMPORTED_MODULE_10__[\"find\"])(container.fields, ['name', fieldName]);\n          return _this2.renderField(field);\n        }));\n      }), !hasTabs && __webpack_provided_wp_dot_element.createElement(\"div\", {\n        className: \"cf-container__fields\"\n      }, Object(lodash__WEBPACK_IMPORTED_MODULE_10__[\"map\"])(container.fields, this.renderField)));\n    }\n  }]);\n  return Container;\n}(_wordpress_element__WEBPACK_IMPORTED_MODULE_9__[\"Component\"]);\n/* harmony default export */ __webpack_exports__[\"default\"] = (Container);\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\")))\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/components/container/index.js?");

/***/ }),

/***/ "./packages/metaboxes/components/container/style.scss":
/*!************************************************************!*\
  !*** ./packages/metaboxes/components/container/style.scss ***!
  \************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// extracted by mini-css-extract-plugin\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/components/container/style.scss?");

/***/ }),

/***/ "./packages/metaboxes/components/field/index.js":
/*!******************************************************!*\
  !*** ./packages/metaboxes/components/field/index.js ***!
  \******************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/compose */ \"@wordpress/compose\");\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_compose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./style.scss */ \"./packages/metaboxes/components/field/style.scss\");\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_style_scss__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hocs_with_field__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hocs/with-field */ \"./packages/metaboxes/hocs/with-field/index.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (Object(_wordpress_compose__WEBPACK_IMPORTED_MODULE_0__[\"compose\"])(_hocs_with_field__WEBPACK_IMPORTED_MODULE_3__[\"default\"], Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_1__[\"withFilters\"])('carbon-fields.field-wrapper.metabox'))(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_1__[\"Field\"]));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/components/field/index.js?");

/***/ }),

/***/ "./packages/metaboxes/components/field/style.scss":
/*!********************************************************!*\
  !*** ./packages/metaboxes/components/field/style.scss ***!
  \********************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// extracted by mini-css-extract-plugin\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/components/field/style.scss?");

/***/ }),

/***/ "./packages/metaboxes/containers/hooks.js":
/*!************************************************!*\
  !*** ./packages/metaboxes/containers/hooks.js ***!
  \************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/compose */ \"@wordpress/compose\");\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_compose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hocs_with_container__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hocs/with-container */ \"./packages/metaboxes/hocs/with-container/index.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n/**\r\n * Extends the containers with necessary hooks.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"addFilter\"])('carbon-fields.register-container-type', 'carbon-fields/metaboxes', function (type, context, component) {\n  return Object(_wordpress_compose__WEBPACK_IMPORTED_MODULE_0__[\"compose\"])(_hocs_with_container__WEBPACK_IMPORTED_MODULE_3__[\"default\"], Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_2__[\"withFilters\"])(\"carbon-fields.\".concat(type, \".\").concat(context)))(component);\n});\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/containers/hooks.js?");

/***/ }),

/***/ "./packages/metaboxes/containers/index.js":
/*!************************************************!*\
  !*** ./packages/metaboxes/containers/index.js ***!
  \************************************************/
/*! exports provided: renderContainer, default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(__webpack_provided_wp_dot_element) {/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"renderContainer\", function() { return renderContainer; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return initializeContainers; });\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\");\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/i18n */ \"@wordpress/i18n\");\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./hooks */ \"./packages/metaboxes/containers/hooks.js\");\n/* harmony import */ var _widget__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./widget */ \"./packages/metaboxes/containers/widget/index.js\");\n/* harmony import */ var _term_meta__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./term-meta */ \"./packages/metaboxes/containers/term-meta/index.js\");\n/* harmony import */ var _theme_options__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./theme-options */ \"./packages/metaboxes/containers/theme-options/index.js\");\n/* harmony import */ var _user_meta__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./user-meta */ \"./packages/metaboxes/containers/user-meta/index.js\");\n/* harmony import */ var _components_container__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/container */ \"./packages/metaboxes/components/container/index.js\");\n/* harmony import */ var _registry__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./registry */ \"./packages/metaboxes/containers/registry.js\");\n/* harmony import */ var _root_registry__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./root-registry */ \"./packages/metaboxes/containers/root-registry.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n\n\n\n\n\n\n/**\r\n * Registers the containers.\r\n */\n['post_meta', 'term_meta', 'user_meta', 'comment_meta', 'network', 'theme_options', 'nav_menu_item', 'widget'].forEach(function (type) {\n  return Object(_registry__WEBPACK_IMPORTED_MODULE_10__[\"registerContainerType\"])(type, _components_container__WEBPACK_IMPORTED_MODULE_9__[\"default\"]);\n});\n\n/**\r\n * Renders the given container.\r\n *\r\n * @param  {Object} container\r\n * @param  {string} context\r\n * @return {void}\r\n */\nfunction renderContainer(container, context) {\n  var node = document.querySelector(\".container-\".concat(container.id));\n  var Component = Object(_registry__WEBPACK_IMPORTED_MODULE_10__[\"getContainerType\"])(container.type, context);\n  if (node) {\n    var NodeComponent = __webpack_provided_wp_dot_element.createElement(Component, {\n      id: container.id\n    });\n    if (_wordpress_element__WEBPACK_IMPORTED_MODULE_0__[\"createRoot\"]) {\n      var nodeRoot = Object(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__[\"createRoot\"])(node);\n      nodeRoot.render(NodeComponent);\n      Object(_root_registry__WEBPACK_IMPORTED_MODULE_11__[\"registerContainerRoot\"])(container.id, nodeRoot);\n    } else {\n      Object(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__[\"render\"])(NodeComponent, node, function () {\n        node.dataset.mounted = true;\n      });\n    }\n  } else {\n    // eslint-disable-next-line no-console\n    console.error(Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"sprintf\"])(Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"__\"])('Could not find DOM element for container \"%1$s\".', 'carbon-fields-ui'), container.id));\n  }\n}\n\n/**\r\n * Initializes the containers.\r\n *\r\n * @param  {string} context\r\n * @return {void}\r\n */\nfunction initializeContainers(context) {\n  var containers = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__[\"select\"])('carbon-fields/metaboxes').getContainers();\n  Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"forEach\"])(containers, function (container) {\n    renderContainer(container, context);\n  });\n}\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\")))\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/containers/index.js?");

/***/ }),

/***/ "./packages/metaboxes/containers/registry.js":
/*!***************************************************!*\
  !*** ./packages/metaboxes/containers/registry.js ***!
  \***************************************************/
/*! exports provided: registerContainerType, getContainerType */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"registerContainerType\", function() { return registerContainerType; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"getContainerType\", function() { return getContainerType; });\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * External dependencies.\r\n */\n\nvar _createRegistry = Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_0__[\"createRegistry\"])('container', ['classic', 'gutenberg']),\n  registerContainerType = _createRegistry.registerContainerType,\n  getContainerType = _createRegistry.getContainerType;\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/containers/registry.js?");

/***/ }),

/***/ "./packages/metaboxes/containers/root-registry.js":
/*!********************************************************!*\
  !*** ./packages/metaboxes/containers/root-registry.js ***!
  \********************************************************/
/*! exports provided: registerContainerRoot, getContainerRoot */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"registerContainerRoot\", function() { return registerContainerRoot; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"getContainerRoot\", function() { return getContainerRoot; });\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nvar rootRegistry = {};\nfunction registerContainerRoot(containerId, root) {\n  rootRegistry[containerId] = _objectSpread(_objectSpread({\n    createdAt: Math.floor(Date.now() / 1000)\n  }, root), {}, {\n    unmount: function unmount() {\n      // Fix issues with race condition by delaying\n      // the onLoad unmounting of containers\n      // they would be unmounted later\n\n      if (parseFloat(window.cf.config.wp_version) >= 6.2) {\n        var currentTime = Math.floor(Date.now() / 1000);\n        if (currentTime - rootRegistry[containerId].createdAt >= 3) {\n          root.unmount();\n          delete rootRegistry[containerId];\n        }\n      } else {\n        root.unmount();\n        delete rootRegistry[containerId];\n      }\n    }\n  });\n}\nfunction getContainerRoot(containerId) {\n  return rootRegistry[containerId] || null;\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/containers/root-registry.js?");

/***/ }),

/***/ "./packages/metaboxes/containers/term-meta/index.js":
/*!**********************************************************!*\
  !*** ./packages/metaboxes/containers/term-meta/index.js ***!
  \**********************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! refract-callbag */ \"refract-callbag\");\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(refract_callbag__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! callbag-basics */ \"callbag-basics\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(callbag_basics__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_from_event_pattern__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/from-event-pattern */ \"./packages/metaboxes/utils/from-event-pattern.js\");\n/* harmony import */ var _store_helpers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../store/helpers */ \"./packages/metaboxes/store/helpers.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n/**\r\n * The function that controls the stream of side effects.\r\n *\r\n * @return {Object}\r\n */\nfunction aperture() {\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_4__[\"pipe\"])(Object(_utils_from_event_pattern__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(function (handler) {\n    return window.jQuery(document).on('ajaxSuccess', handler);\n  }, function (handler) {\n    return window.jQuery(document).off('ajaxSuccess', handler);\n  }, function (e, xhr, options, data) {\n    return {\n      options: options,\n      data: data\n    };\n  }), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_4__[\"filter\"])(function (_ref) {\n    var options = _ref.options,\n      data = _ref.data;\n    return options.data && options.data.indexOf('carbon_fields_container') > -1 && options.data.indexOf('add-tag') > -1 && !data.documentElement.querySelector('wp_error');\n  }));\n}\n\n/**\r\n * The function that causes the side effects.\r\n *\r\n * @param  {Object} props\r\n * @return {Function}\r\n */\nfunction handler(props) {\n  return function () {\n    // Collects identifiers of current fields so we can remove them later.\n    var oldFieldIds = lodash__WEBPACK_IMPORTED_MODULE_0___default.a.map(props.container.fields, 'id');\n\n    // Get a fresh copy of the container and fields.\n    var _normalizePreloadedSt = Object(_store_helpers__WEBPACK_IMPORTED_MODULE_6__[\"normalizePreloadedState\"])(lodash__WEBPACK_IMPORTED_MODULE_0___default.a.get(window.cf, 'preloaded.containers', [])),\n      containers = _normalizePreloadedSt.containers,\n      fields = _normalizePreloadedSt.fields;\n    var container = lodash__WEBPACK_IMPORTED_MODULE_0___default.a.find(containers, ['id', props.id]);\n    var containerFields = lodash__WEBPACK_IMPORTED_MODULE_0___default.a.filter(fields, ['container_id', props.id]);\n\n    // Replace the container and add the new fields.\n    var _dispatch = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_2__[\"dispatch\"])('carbon-fields/metaboxes'),\n      updateState = _dispatch.updateState,\n      removeFields = _dispatch.removeFields;\n    updateState(lodash__WEBPACK_IMPORTED_MODULE_0___default.a.keyBy([container], 'id'), lodash__WEBPACK_IMPORTED_MODULE_0___default.a.keyBy(containerFields, 'id'));\n    removeFields(oldFieldIds);\n  };\n}\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"addFilter\"])('carbon-fields.term_meta.classic', 'carbon-fields/metaboxes', Object(refract_callbag__WEBPACK_IMPORTED_MODULE_3__[\"withEffects\"])(aperture, {\n  handler: handler\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/containers/term-meta/index.js?");

/***/ }),

/***/ "./packages/metaboxes/containers/theme-options/index.js":
/*!**************************************************************!*\
  !*** ./packages/metaboxes/containers/theme-options/index.js ***!
  \**************************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! refract-callbag */ \"refract-callbag\");\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(refract_callbag__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! callbag-basics */ \"callbag-basics\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(callbag_basics__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var callbag_from_event__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! callbag-from-event */ \"./node_modules/callbag-from-event/index.js\");\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./style.scss */ \"./packages/metaboxes/containers/theme-options/style.scss\");\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_style_scss__WEBPACK_IMPORTED_MODULE_4__);\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n/**\r\n * The function that controls the stream of side effects.\r\n *\r\n * @return {Object}\r\n */\nfunction aperture() {\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_2__[\"pipe\"])(Object(callbag_from_event__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(window, 'scroll'), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_2__[\"map\"])(function () {\n    return window.jQuery(window).scrollTop();\n  }));\n}\n\n/**\r\n * The function that causes the side effects.\r\n *\r\n * @param  {Object} props\r\n * @return {Function}\r\n */\nfunction handler() {\n  return function (windowTopOffset) {\n    var $container = window.jQuery('.carbon-box:first');\n    var $panel = window.jQuery('#postbox-container-1');\n    var $bar = window.jQuery('#wpadminbar');\n    var offset = $bar.height() + 10;\n    var threshold = $container.offset().top - offset;\n\n    // In some situations the threshold is negative number because\n    // the container element isn't rendered yet.\n    if (threshold > 0) {\n      $panel.toggleClass('fixed', windowTopOffset >= threshold).css('top', offset);\n    }\n  };\n}\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_0__[\"addFilter\"])('carbon-fields.theme_options.classic', 'carbon-fields/metaboxes', Object(refract_callbag__WEBPACK_IMPORTED_MODULE_1__[\"withEffects\"])(aperture, {\n  handler: handler\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/containers/theme-options/index.js?");

/***/ }),

/***/ "./packages/metaboxes/containers/theme-options/style.scss":
/*!****************************************************************!*\
  !*** ./packages/metaboxes/containers/theme-options/style.scss ***!
  \****************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// extracted by mini-css-extract-plugin\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/containers/theme-options/style.scss?");

/***/ }),

/***/ "./packages/metaboxes/containers/user-meta/index.js":
/*!**********************************************************!*\
  !*** ./packages/metaboxes/containers/user-meta/index.js ***!
  \**********************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./style.scss */ \"./packages/metaboxes/containers/user-meta/style.scss\");\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_scss__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Internal dependencies.\r\n */\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/containers/user-meta/index.js?");

/***/ }),

/***/ "./packages/metaboxes/containers/user-meta/style.scss":
/*!************************************************************!*\
  !*** ./packages/metaboxes/containers/user-meta/style.scss ***!
  \************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// extracted by mini-css-extract-plugin\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/containers/user-meta/style.scss?");

/***/ }),

/***/ "./packages/metaboxes/containers/widget/index.js":
/*!*******************************************************!*\
  !*** ./packages/metaboxes/containers/widget/index.js ***!
  \*******************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! refract-callbag */ \"refract-callbag\");\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(refract_callbag__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./style.scss */ \"./packages/metaboxes/containers/widget/style.scss\");\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_style_scss__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_4__);\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * The function that controls the stream of side effects.\r\n *\r\n * @return {Object}\r\n */\nfunction aperture() {\n  return Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_4__[\"fromSelector\"])(Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__[\"select\"])('carbon-fields/metaboxes').isFieldUpdated);\n}\n\n/**\r\n * The function that causes the side effects.\r\n *\r\n * @param  {Object} props\r\n * @return {Function}\r\n */\nfunction handler(props) {\n  return function (_ref) {\n    var action = _ref.action;\n    if (!action) {\n      return;\n    }\n    var container = props.container;\n    var payload = action.payload;\n    if (container.fields.map(function (field) {\n      return field.id;\n    }).indexOf(payload.fieldId) >= 0) {\n      var $carbonContainer = window.jQuery(\".container-\".concat(container.id));\n      $carbonContainer.closest('.widget-inside').trigger('change');\n    }\n  };\n}\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"addFilter\"])('carbon-fields.widget.classic', 'carbon-fields/metaboxes', Object(refract_callbag__WEBPACK_IMPORTED_MODULE_2__[\"withEffects\"])(aperture, {\n  handler: handler\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/containers/widget/index.js?");

/***/ }),

/***/ "./packages/metaboxes/containers/widget/style.scss":
/*!*********************************************************!*\
  !*** ./packages/metaboxes/containers/widget/style.scss ***!
  \*********************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// extracted by mini-css-extract-plugin\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/containers/widget/style.scss?");

/***/ }),

/***/ "./packages/metaboxes/fields/association/index.js":
/*!********************************************************!*\
  !*** ./packages/metaboxes/fields/association/index.js ***!
  \********************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_strip_compact_input_prefix__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/strip-compact-input-prefix */ \"./packages/metaboxes/utils/strip-compact-input-prefix.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n/**\r\n * Returns a field with the given name.\r\n *\r\n * @param  {Object[]} fields\r\n * @param  {string}   name\r\n * @return {?Object}\r\n */\nfunction findFieldByName(fields, name) {\n  return Object(lodash__WEBPACK_IMPORTED_MODULE_2__[\"find\"])(fields, function (field) {\n    return field.name === name;\n  });\n}\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_0__[\"addFilter\"])('carbon-fields.association.metabox', 'carbon-fields/metaboxes', Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_3__[\"withProps\"])(function (props) {\n  return {\n    hierarchyResolver: function hierarchyResolver() {\n      // Get all fields.\n      var container = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__[\"select\"])('carbon-fields/metaboxes').getContainerById(props.containerId);\n      var fields = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__[\"select\"])('carbon-fields/metaboxes').getFieldsByContainerId(props.containerId);\n\n      // Get a clean version of field's name.\n      var fieldName = Object(_utils_strip_compact_input_prefix__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(props.name);\n\n      // Get the path.\n      var path = fieldName.split(/\\[|\\]/g);\n\n      // Remove chunks that are empty.\n      path = path.filter(function (chunk) {\n        return chunk !== '';\n      });\n      if (container.type === 'widget') {\n        return props.field.base_name;\n      }\n\n      // Get the root field.\n      var rootFieldName = path.shift();\n      var rootField = findFieldByName(fields, rootFieldName);\n\n      // Get the hierarchy.\n      var accessor = fields.indexOf(rootField);\n      var hierarchy = rootField.base_name;\n\n      // Visit every branch in the tree so we can get the full hierarchy.\n      while (path.length > 0) {\n        var chunk = path.shift();\n        var isGroup = !isNaN(chunk);\n        var isSameField = chunk === props.field.base_name;\n        var isNestedComplex = !isGroup && !isSameField;\n        if (isGroup) {\n          accessor = \"\".concat(accessor, \".value.\").concat(chunk, \".name\");\n          hierarchy = \"\".concat(hierarchy, \"[\").concat(chunk, \"]:\").concat(Object(lodash__WEBPACK_IMPORTED_MODULE_2__[\"get\"])(fields, accessor), \"/\");\n        }\n        if (isNestedComplex) {\n          var fieldReferences = Object(lodash__WEBPACK_IMPORTED_MODULE_2__[\"get\"])(fields, accessor.replace(/\\.name$/, '.fields'));\n          var fieldReference = findFieldByName(fieldReferences, chunk);\n          var field = Object(lodash__WEBPACK_IMPORTED_MODULE_2__[\"find\"])(fields, ['id', fieldReference.id]);\n          accessor = fields.indexOf(field);\n          hierarchy = \"\".concat(hierarchy).concat(field.base_name);\n        }\n        if (isSameField) {\n          hierarchy = \"\".concat(hierarchy).concat(chunk);\n        }\n      }\n      return hierarchy;\n    }\n  };\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/association/index.js?");

/***/ }),

/***/ "./packages/metaboxes/fields/complex/index.js":
/*!****************************************************!*\
  !*** ./packages/metaboxes/fields/complex/index.js ***!
  \****************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(__webpack_provided_wp_dot_element) {/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/@babel/runtime/helpers/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/@babel/runtime/helpers/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/assertThisInitialized */ \"./node_modules/@babel/runtime/helpers/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"./node_modules/@babel/runtime/helpers/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js\");\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"./node_modules/@babel/runtime/helpers/getPrototypeOf.js\");\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! immer */ \"immer\");\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(immer__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\");\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @wordpress/compose */ \"@wordpress/compose\");\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_wordpress_compose__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./style.scss */ \"./packages/metaboxes/fields/complex/style.scss\");\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_style_scss__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _components_field__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../components/field */ \"./packages/metaboxes/components/field/index.js\");\n/* harmony import */ var _utils_flatten_field__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/flatten-field */ \"./packages/metaboxes/utils/flatten-field.js\");\n\n\n\n\n\n\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_5___default()(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_5___default()(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_4___default()(this, result); }; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\nvar ComplexField = /*#__PURE__*/function (_Component) {\n  _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_3___default()(ComplexField, _Component);\n  var _super = _createSuper(ComplexField);\n  function ComplexField() {\n    var _this;\n    _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0___default()(this, ComplexField);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    /**\r\n     * Handles adding of group.\r\n     *\r\n     * @param  {Object}   group\r\n     * @param  {Function} callback\r\n     * @return {Object}\r\n     */\n    _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2___default()(_this), \"handleAddGroup\", function (group, callback) {\n      var _this$props = _this.props,\n        id = _this$props.id,\n        field = _this$props.field,\n        value = _this$props.value,\n        addFields = _this$props.addFields,\n        onChange = _this$props.onChange;\n\n      // Create a copy of the group to prevent\n      // incidentally modifications.\n      group = Object(lodash__WEBPACK_IMPORTED_MODULE_12__[\"cloneDeep\"])(group);\n\n      // Get a flat list of all fields for this group.\n      var fields = [];\n      group.id = Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_13__[\"uniqueId\"])();\n      group.container_id = field.container_id;\n      group.fields = group.fields.map(function (groupField) {\n        return Object(_utils_flatten_field__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(groupField, field.container_id, fields);\n      });\n\n      // Make sure that the group is expanded even\n      // `set_collapsed(true)` is used.\n      group.collapsed = false;\n\n      // Push the group to the field.\n      addFields(fields);\n      onChange(id, value.concat(group));\n      callback(group);\n    });\n    /**\r\n     * Handles cloning of group.\r\n     *\r\n     * @param  {Object}   group\r\n     * @param  {Function} callback\r\n     * @return {void}\r\n     */\n    _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2___default()(_this), \"handleCloneGroup\", function (group, callback) {\n      var _this$props2 = _this.props,\n        id = _this$props2.id,\n        value = _this$props2.value,\n        cloneFields = _this$props2.cloneFields,\n        onChange = _this$props2.onChange;\n      var originFieldIds = group.fields.map(function (groupField) {\n        return groupField.id;\n      });\n      var cloneFieldIds = originFieldIds.map(function () {\n        return Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_13__[\"uniqueId\"])();\n      });\n      var clonedGroup = Object(lodash__WEBPACK_IMPORTED_MODULE_12__[\"cloneDeep\"])(group);\n      clonedGroup.id = Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_13__[\"uniqueId\"])();\n      clonedGroup.fields.forEach(function (groupField, index) {\n        groupField.id = cloneFieldIds[index];\n      });\n      cloneFields(originFieldIds, cloneFieldIds);\n      onChange(id, immer__WEBPACK_IMPORTED_MODULE_7___default()(value, function (draft) {\n        draft.splice(value.indexOf(group) + 1, 0, clonedGroup);\n      }));\n      callback(clonedGroup);\n    });\n    /**\r\n     * Handles removing of group.\r\n     *\r\n     * @param  {Object} group\r\n     * @return {void}\r\n     */\n    _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2___default()(_this), \"handleRemoveGroup\", function (group) {\n      var _this$props3 = _this.props,\n        id = _this$props3.id,\n        value = _this$props3.value,\n        onChange = _this$props3.onChange;\n      onChange(id, Object(lodash__WEBPACK_IMPORTED_MODULE_12__[\"without\"])(value, group), group.fields.map(function (groupField) {\n        return groupField.id;\n      }));\n    });\n    /**\r\n     * Handles expanding/collapsing of group.\r\n     *\r\n     * @param  {string} groupId\r\n     * @return {void}\r\n     */\n    _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2___default()(_this), \"handleToggleGroup\", function (groupId) {\n      var _this$props4 = _this.props,\n        field = _this$props4.field,\n        value = _this$props4.value,\n        onChange = _this$props4.onChange;\n      onChange(field.id, immer__WEBPACK_IMPORTED_MODULE_7___default()(value, function (draft) {\n        var group = Object(lodash__WEBPACK_IMPORTED_MODULE_12__[\"find\"])(draft, ['id', groupId]);\n        group.collapsed = !group.collapsed;\n      }));\n    });\n    /**\r\n     * Handles expanding/collapsing of all groups.\r\n     *\r\n     * @param  {boolean} collapsed\r\n     * @return {void}\r\n     */\n    _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2___default()(_this), \"handleToggleAllGroups\", function (collapsed) {\n      var _this$props5 = _this.props,\n        field = _this$props5.field,\n        value = _this$props5.value,\n        onChange = _this$props5.onChange;\n      onChange(field.id, immer__WEBPACK_IMPORTED_MODULE_7___default()(value, function (draft) {\n        draft.forEach(function (group) {\n          group.collapsed = collapsed;\n        });\n      }));\n    });\n    /**\r\n     * Handles setuping of group.\r\n     *\r\n     * @param  {Object} group\r\n     * @param  {Object} props\r\n     * @return {Object}\r\n     */\n    _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2___default()(_this), \"handleGroupSetup\", function (group, props) {\n      return Object(lodash__WEBPACK_IMPORTED_MODULE_12__[\"assign\"])({}, props, {\n        id: group.id,\n        name: group.name,\n        prefix: \"\".concat(_this.props.name, \"[\").concat(props.index, \"]\"),\n        fields: group.fields,\n        collapsed: group.collapsed,\n        context: 'metabox'\n      });\n    });\n    /**\r\n     * Handles setuping of group's field.\r\n     *\r\n     * @param  {Object} field\r\n     * @param  {Object} props\r\n     * @param  {Object} groupProps\r\n     * @return {Array}\r\n     */\n    _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6___default()(_babel_runtime_helpers_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2___default()(_this), \"handleGroupFieldSetup\", function (field, props, groupProps) {\n      return [_components_field__WEBPACK_IMPORTED_MODULE_15__[\"default\"], Object(lodash__WEBPACK_IMPORTED_MODULE_12__[\"assign\"])({}, props, {\n        key: field.id,\n        id: field.id,\n        containerId: _this.props.containerId,\n        name: \"\".concat(groupProps.prefix, \"[\").concat(field.name, \"]\")\n      })];\n    });\n    return _this;\n  }\n  _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1___default()(ComplexField, [{\n    key: \"render\",\n    value:\n    /**\r\n     * Renders the component.\r\n     *\r\n     * @return {Object}\r\n     */\n    function render() {\n      var handleGroupSetup = this.handleGroupSetup,\n        handleGroupFieldSetup = this.handleGroupFieldSetup,\n        handleAddGroup = this.handleAddGroup,\n        handleCloneGroup = this.handleCloneGroup,\n        handleRemoveGroup = this.handleRemoveGroup,\n        handleToggleGroup = this.handleToggleGroup,\n        handleToggleAllGroups = this.handleToggleAllGroups;\n      var _this$props6 = this.props,\n        value = _this$props6.value,\n        children = _this$props6.children;\n      var allGroupsAreCollapsed = value.every(function (_ref) {\n        var collapsed = _ref.collapsed;\n        return collapsed;\n      });\n      return children({\n        allGroupsAreCollapsed: allGroupsAreCollapsed,\n        handleGroupSetup: handleGroupSetup,\n        handleGroupFieldSetup: handleGroupFieldSetup,\n        handleAddGroup: handleAddGroup,\n        handleCloneGroup: handleCloneGroup,\n        handleRemoveGroup: handleRemoveGroup,\n        handleToggleGroup: handleToggleGroup,\n        handleToggleAllGroups: handleToggleAllGroups\n      });\n    }\n  }]);\n  return ComplexField;\n}(_wordpress_element__WEBPACK_IMPORTED_MODULE_8__[\"Component\"]);\nvar applyWithSelect = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_11__[\"withSelect\"])(function (select, props) {\n  var _select = select('carbon-fields/metaboxes'),\n    getComplexGroupValues = _select.getComplexGroupValues;\n  var groupValues = props.value.map(function (group) {\n    var fieldIds = group.fields.map(function (field) {\n      return field.id;\n    });\n    return [group.name, getComplexGroupValues(fieldIds)];\n  });\n  return {\n    groupValues: groupValues\n  };\n});\nvar applyWithDispatch = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_11__[\"withDispatch\"])(function (dispatch) {\n  var _dispatch = dispatch('carbon-fields/metaboxes'),\n    addFields = _dispatch.addFields,\n    cloneFields = _dispatch.cloneFields;\n  return {\n    addFields: addFields,\n    cloneFields: cloneFields\n  };\n});\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_9__[\"addFilter\"])('carbon-fields.complex.metabox', 'carbon-fields/metaboxes', function (OriginalComplexField) {\n  return Object(_wordpress_compose__WEBPACK_IMPORTED_MODULE_10__[\"compose\"])(applyWithSelect, applyWithDispatch)(function (props) {\n    var id = props.id,\n      field = props.field,\n      name = props.name,\n      value = props.value,\n      groupValues = props.groupValues;\n    return __webpack_provided_wp_dot_element.createElement(ComplexField, props, function (_ref2) {\n      var allGroupsAreCollapsed = _ref2.allGroupsAreCollapsed,\n        handleGroupSetup = _ref2.handleGroupSetup,\n        handleGroupFieldSetup = _ref2.handleGroupFieldSetup,\n        handleAddGroup = _ref2.handleAddGroup,\n        handleCloneGroup = _ref2.handleCloneGroup,\n        handleRemoveGroup = _ref2.handleRemoveGroup,\n        handleToggleGroup = _ref2.handleToggleGroup,\n        handleToggleAllGroups = _ref2.handleToggleAllGroups;\n      return __webpack_provided_wp_dot_element.createElement(OriginalComplexField, {\n        groupIdKey: \"id\",\n        groupFilterKey: \"name\",\n        id: id,\n        field: field,\n        name: name,\n        value: value,\n        groupValues: groupValues,\n        allGroupsAreCollapsed: allGroupsAreCollapsed,\n        onGroupSetup: handleGroupSetup,\n        onGroupFieldSetup: handleGroupFieldSetup,\n        onAddGroup: handleAddGroup,\n        onCloneGroup: handleCloneGroup,\n        onRemoveGroup: handleRemoveGroup,\n        onToggleGroup: handleToggleGroup,\n        onToggleAllGroups: handleToggleAllGroups,\n        onChange: props.onChange\n      });\n    });\n  });\n});\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\")))\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/complex/index.js?");

/***/ }),

/***/ "./packages/metaboxes/fields/complex/style.scss":
/*!******************************************************!*\
  !*** ./packages/metaboxes/fields/complex/style.scss ***!
  \******************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// extracted by mini-css-extract-plugin\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/complex/style.scss?");

/***/ }),

/***/ "./packages/metaboxes/fields/datetime/index.js":
/*!*****************************************************!*\
  !*** ./packages/metaboxes/fields/datetime/index.js ***!
  \*****************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(__webpack_provided_wp_dot_element) {/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/i18n */ \"@wordpress/i18n\");\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__);\n\n/**\r\n * External dependencies.\r\n */\n\n\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"addFilter\"])('carbon-fields.date_time.metabox', 'carbon-fields/metaboxes', function (OriginalDatetimeField) {\n  return function (props) {\n    return __webpack_provided_wp_dot_element.createElement(OriginalDatetimeField, _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n      buttonText: Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"__\"])('Select Date', 'carbon-fields-ui')\n    }, props));\n  };\n});\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\")))\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/datetime/index.js?");

/***/ }),

/***/ "./packages/metaboxes/fields/file/index.js":
/*!*************************************************!*\
  !*** ./packages/metaboxes/fields/file/index.js ***!
  \*************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(__webpack_provided_wp_dot_element) {/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/i18n */ \"@wordpress/i18n\");\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__);\n\n/**\r\n * External dependencies.\r\n */\n\n\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"addFilter\"])('carbon-fields.file.metabox', 'carbon-fields/metaboxes', function (OriginalFileField) {\n  return function (props) {\n    return __webpack_provided_wp_dot_element.createElement(OriginalFileField, _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n      buttonLabel: Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"__\"])('Select File', 'carbon-fields-ui'),\n      mediaLibraryButtonLabel: Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"__\"])('Use File', 'carbon-fields-ui'),\n      mediaLibraryTitle: Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"__\"])('Select File', 'carbon-fields-ui')\n    }, props));\n  };\n});\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\")))\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/file/index.js?");

/***/ }),

/***/ "./packages/metaboxes/fields/image/index.js":
/*!**************************************************!*\
  !*** ./packages/metaboxes/fields/image/index.js ***!
  \**************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(__webpack_provided_wp_dot_element) {/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/i18n */ \"@wordpress/i18n\");\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__);\n\n/**\r\n * External dependencies.\r\n */\n\n\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"addFilter\"])('carbon-fields.image.metabox', 'carbon-fields/metaboxes', function (OriginalImageField) {\n  return function (props) {\n    return __webpack_provided_wp_dot_element.createElement(OriginalImageField, _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n      buttonLabel: Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"__\"])('Select Image', 'carbon-fields-ui'),\n      mediaLibraryButtonLabel: Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"__\"])('Use Image', 'carbon-fields-ui'),\n      mediaLibraryTitle: Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"__\"])('Select Image', 'carbon-fields-ui')\n    }, props));\n  };\n});\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\")))\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/image/index.js?");

/***/ }),

/***/ "./packages/metaboxes/fields/index.js":
/*!********************************************!*\
  !*** ./packages/metaboxes/fields/index.js ***!
  \********************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/compose */ \"@wordpress/compose\");\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_compose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _hocs_with_field__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hocs/with-field */ \"./packages/metaboxes/hocs/with-field/index.js\");\n/* harmony import */ var _hocs_with_conditional_logic__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hocs/with-conditional-logic */ \"./packages/metaboxes/hocs/with-conditional-logic/index.js\");\n/* harmony import */ var _utils_is_gutenberg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/is-gutenberg */ \"./packages/metaboxes/utils/is-gutenberg.js\");\n/* harmony import */ var _association__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./association */ \"./packages/metaboxes/fields/association/index.js\");\n/* harmony import */ var _complex__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./complex */ \"./packages/metaboxes/fields/complex/index.js\");\n/* harmony import */ var _datetime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./datetime */ \"./packages/metaboxes/fields/datetime/index.js\");\n/* harmony import */ var _file__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./file */ \"./packages/metaboxes/fields/file/index.js\");\n/* harmony import */ var _image__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./image */ \"./packages/metaboxes/fields/image/index.js\");\n/* harmony import */ var _multiselect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./multiselect */ \"./packages/metaboxes/fields/multiselect/index.js\");\n/* harmony import */ var _media_gallery__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./media-gallery */ \"./packages/metaboxes/fields/media-gallery/index.js\");\n/* harmony import */ var _radio__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./radio */ \"./packages/metaboxes/fields/radio/index.js\");\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./sidebar */ \"./packages/metaboxes/fields/sidebar/index.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n\n/**\r\n * Connects every field to the store.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"addFilter\"])('carbon-fields.field-edit.metabox', 'carbon-fields/metaboxes', Object(_wordpress_compose__WEBPACK_IMPORTED_MODULE_0__[\"compose\"])(_hocs_with_field__WEBPACK_IMPORTED_MODULE_4__[\"default\"], _hocs_with_conditional_logic__WEBPACK_IMPORTED_MODULE_5__[\"default\"], Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_2__[\"withDispatch\"])(function (dispatch) {\n  if (Object(_utils_is_gutenberg__WEBPACK_IMPORTED_MODULE_6__[\"default\"])()) {\n    var _dispatch = dispatch('core/editor'),\n      lockPostSaving = _dispatch.lockPostSaving,\n      unlockPostSaving = _dispatch.unlockPostSaving;\n    return {\n      lockSaving: lockPostSaving,\n      unlockSaving: unlockPostSaving\n    };\n  }\n  var _dispatch2 = dispatch('carbon-fields/metaboxes'),\n    lockSaving = _dispatch2.lockSaving,\n    unlockSaving = _dispatch2.unlockSaving;\n  return {\n    lockSaving: lockSaving,\n    unlockSaving: unlockSaving\n  };\n}), _carbon_fields_core__WEBPACK_IMPORTED_MODULE_3__[\"withValidation\"]));\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/index.js?");

/***/ }),

/***/ "./packages/metaboxes/fields/media-gallery/index.js":
/*!**********************************************************!*\
  !*** ./packages/metaboxes/fields/media-gallery/index.js ***!
  \**********************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(__webpack_provided_wp_dot_element) {/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/i18n */ \"@wordpress/i18n\");\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./style.scss */ \"./packages/metaboxes/fields/media-gallery/style.scss\");\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_style_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n/**\r\n * External dependencies.\r\n */\n\n\n\n/**\r\n * The internal dependencies.\r\n */\n\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"addFilter\"])('carbon-fields.media_gallery.metabox', 'carbon-fields/metaboxes', function (OriginalMediaGalleryField) {\n  return function (props) {\n    return __webpack_provided_wp_dot_element.createElement(OriginalMediaGalleryField, _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n      buttonLabel: Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"__\"])('Select Attachments', 'carbon-fields-ui'),\n      mediaLibraryButtonLabel: Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"__\"])('Use Attachments', 'carbon-fields-ui'),\n      mediaLibraryTitle: Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__[\"__\"])('Select Attachments', 'carbon-fields-ui')\n    }, props));\n  };\n});\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\")))\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/media-gallery/index.js?");

/***/ }),

/***/ "./packages/metaboxes/fields/media-gallery/style.scss":
/*!************************************************************!*\
  !*** ./packages/metaboxes/fields/media-gallery/style.scss ***!
  \************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// extracted by mini-css-extract-plugin\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/media-gallery/style.scss?");

/***/ }),

/***/ "./packages/metaboxes/fields/multiselect/index.js":
/*!********************************************************!*\
  !*** ./packages/metaboxes/fields/multiselect/index.js ***!
  \********************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./style.scss */ \"./packages/metaboxes/fields/multiselect/style.scss\");\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_scss__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Internal dependencies.\r\n */\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/multiselect/index.js?");

/***/ }),

/***/ "./packages/metaboxes/fields/multiselect/style.scss":
/*!**********************************************************!*\
  !*** ./packages/metaboxes/fields/multiselect/style.scss ***!
  \**********************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// extracted by mini-css-extract-plugin\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/multiselect/style.scss?");

/***/ }),

/***/ "./packages/metaboxes/fields/radio/index.js":
/*!**************************************************!*\
  !*** ./packages/metaboxes/fields/radio/index.js ***!
  \**************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./style.scss */ \"./packages/metaboxes/fields/radio/style.scss\");\n/* harmony import */ var _style_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_scss__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * The internal dependencies.\r\n */\n\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/radio/index.js?");

/***/ }),

/***/ "./packages/metaboxes/fields/radio/style.scss":
/*!****************************************************!*\
  !*** ./packages/metaboxes/fields/radio/style.scss ***!
  \****************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// extracted by mini-css-extract-plugin\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/radio/style.scss?");

/***/ }),

/***/ "./packages/metaboxes/fields/sidebar/index.js":
/*!****************************************************!*\
  !*** ./packages/metaboxes/fields/sidebar/index.js ***!
  \****************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/**\r\n * External dependencies.\r\n */\n\n\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"addFilter\"])('carbon-fields.sidebar.metabox', 'carbon-fields/metaboxes', Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__[\"withDispatch\"])(function (dispatch) {\n  var _dispatch = dispatch('carbon-fields/metaboxes'),\n    receiveSidebar = _dispatch.receiveSidebar;\n  return {\n    onAdded: receiveSidebar\n  };\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/fields/sidebar/index.js?");

/***/ }),

/***/ "./packages/metaboxes/hocs/with-conditional-logic/index.js":
/*!*****************************************************************!*\
  !*** ./packages/metaboxes/hocs/with-conditional-logic/index.js ***!
  \*****************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ \"./node_modules/@babel/runtime/helpers/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var callbag_of__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! callbag-of */ \"./node_modules/callbag-of/dist/callbag-of.esm.js\");\n/* harmony import */ var callbag_take_until__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! callbag-take-until */ \"./node_modules/callbag-take-until/index.js\");\n/* harmony import */ var callbag_distinct_until_changed__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! callbag-distinct-until-changed */ \"./node_modules/callbag-distinct-until-changed/dist/callbag-distinct-until-changed.es.js\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! callbag-basics */ \"callbag-basics\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(callbag_basics__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_7__);\n\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * Returns all root fields from the given holder\r\n * while excluding some of them.\r\n *\r\n * @param  {Object}   fieldsHolder\r\n * @param  {Object}   allFields\r\n * @param  {string[]} excludedIds\r\n * @return {Object[]}\r\n */\nfunction getFieldsFromFieldsHolder(fieldsHolder, allFields) {\n  var excludedIds = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  if (typeof fieldsHolder === 'undefined') {\n    return [];\n  }\n  return Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"pick\"])(allFields, Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"difference\"])(Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"map\"])(fieldsHolder.fields, 'id'), excludedIds));\n}\n\n/**\r\n * Adds the `parent.` parent prefix to field's name.\r\n *\r\n * @param  {Object[]} fields\r\n * @param  {number}   depth\r\n * @return {Array[]}\r\n */\nfunction mapParentPrefix(fields) {\n  var depth = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  return Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"map\"])(fields, function (field) {\n    return [field.id, \"\".concat(Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"repeat\"])('parent.', depth)).concat(field.base_name)];\n  });\n}\n\n/**\r\n * The function used to track dependencies required\r\n * by conditional logic.\r\n *\r\n * @param  {Object} props\r\n * @param  {Object} component\r\n * @return {Object}\r\n */\nfunction input(props, component) {\n  var _select = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__[\"select\"])('carbon-fields/metaboxes'),\n    getFieldsByContainerId = _select.getFieldsByContainerId;\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_4__[\"pipe\"])(Object(callbag_basics__WEBPACK_IMPORTED_MODULE_4__[\"merge\"])(Object(callbag_of__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(getFieldsByContainerId(props.containerId)), Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_7__[\"fromSelector\"])(getFieldsByContainerId, props.containerId)), Object(callbag_take_until__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(component.unmount), Object(callbag_distinct_until_changed__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(lodash__WEBPACK_IMPORTED_MODULE_6__[\"isEqual\"]));\n}\n\n/**\r\n * The function that provides the data that needs to be\r\n * evaluated by conditional logic.\r\n *\r\n * @param  {Object} props\r\n * @param  {Object} fields\r\n * @return {Object}\r\n */\nfunction output(props, fields) {\n  fields = Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"keyBy\"])(fields, 'id');\n  var container = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__[\"select\"])('carbon-fields/metaboxes').getContainerById(props.containerId);\n  var isTopLevelField = Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"some\"])(container.fields, ['id', props.id]);\n  var siblingFields = [];\n  if (isTopLevelField) {\n    siblingFields = getFieldsFromFieldsHolder(container, fields, [props.id]);\n    siblingFields = mapParentPrefix(siblingFields);\n  } else {\n    var fieldName = props.name.replace(new RegExp(\"^\".concat(window.cf.config.compactInputKey, \"\\\\[(.+?)\\\\]\")), '$1');\n\n    // Get the root field.\n    var rootField = Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"find\"])(fields, function (field) {\n      return field.container_id === props.containerId && Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"startsWith\"])(fieldName, field.name);\n    });\n\n    // Get the hierarchy.\n    var path = fieldName.split(/\\[|\\]/g);\n\n    // Remove the chunk with name of root field\n    // because we already have it.\n    path.shift();\n\n    // Remove any chunks that don't have a value.\n    path = path.filter(function (chunk) {\n      return chunk !== '';\n    });\n\n    // Remove the chunk with name of field\n    // because we don't needed it.\n    path.pop();\n\n    // Keep reference to the depth\n    // so we can add the `parent.` prefix.\n    var depth = path.reduce(function (accumulator, chunk) {\n      return isNaN(chunk) ? accumulator + 1 : accumulator;\n    }, 0);\n\n    // Collect fields that are siblings of root field.\n    siblingFields = getFieldsFromFieldsHolder(container, fields, [rootField.id]);\n    siblingFields = mapParentPrefix(siblingFields, depth + 1);\n\n    // Keep reference to the full path of the field.\n    var pathPrefix = \"\".concat(rootField.id, \".value\");\n    while (path.length > 0) {\n      var chunk = path.shift();\n      var isGroup = !isNaN(chunk);\n      var isNestedComplex = !isGroup;\n      if (isGroup) {\n        pathPrefix = \"\".concat(pathPrefix, \"[\").concat(chunk, \"]\");\n        var group = Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"get\"])(fields, pathPrefix);\n        var groupFields = getFieldsFromFieldsHolder(group, fields, [props.id]);\n        siblingFields = siblingFields.concat(mapParentPrefix(groupFields, depth));\n        pathPrefix = \"\".concat(pathPrefix, \".fields\");\n      }\n      if (isNestedComplex) {\n        var groupField = Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"find\"])(Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"get\"])(fields, pathPrefix), ['name', chunk]);\n        if (groupField) {\n          pathPrefix = \"\".concat(groupField.id, \".value\");\n        }\n        depth--;\n      }\n    }\n  }\n  siblingFields = siblingFields.map(function (_ref) {\n    var _ref2 = _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0___default()(_ref, 2),\n      id = _ref2[0],\n      name = _ref2[1];\n    return [name, Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"get\"])(fields, \"\".concat(id, \".value\"))];\n  });\n  return Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"fromPairs\"])(siblingFields);\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_7__[\"withConditionalLogic\"])(input, output));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/hocs/with-conditional-logic/index.js?");

/***/ }),

/***/ "./packages/metaboxes/hocs/with-container/index.js":
/*!*********************************************************!*\
  !*** ./packages/metaboxes/hocs/with-container/index.js ***!
  \*********************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/compose */ \"@wordpress/compose\");\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_compose__WEBPACK_IMPORTED_MODULE_1__);\n/**\r\n * External dependencies.\r\n */\n\n\n\n/**\r\n * Creates a high-order component which adds connection\r\n * to the store.\r\n *\r\n * @param  {Function} Component\r\n * @return {Function}\r\n */\n/* harmony default export */ __webpack_exports__[\"default\"] = (Object(_wordpress_compose__WEBPACK_IMPORTED_MODULE_1__[\"createHigherOrderComponent\"])(function (Component) {\n  var applyWithSelect = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__[\"withSelect\"])(function (select, _ref) {\n    var id = _ref.id;\n    var container = select('carbon-fields/metaboxes').getContainerById(id);\n    return {\n      container: container\n    };\n  });\n  return applyWithSelect(Component);\n}, 'withContainer'));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/hocs/with-container/index.js?");

/***/ }),

/***/ "./packages/metaboxes/hocs/with-field/index.js":
/*!*****************************************************!*\
  !*** ./packages/metaboxes/hocs/with-field/index.js ***!
  \*****************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/compose */ \"@wordpress/compose\");\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_compose__WEBPACK_IMPORTED_MODULE_1__);\n/**\r\n * External dependencies.\r\n */\n\n\n\n/**\r\n * Creates a high-order component which adds connection\r\n * to the store.\r\n *\r\n * @param  {Function} Component\r\n * @return {Function}\r\n */\n/* harmony default export */ __webpack_exports__[\"default\"] = (Object(_wordpress_compose__WEBPACK_IMPORTED_MODULE_1__[\"createHigherOrderComponent\"])(function (Component) {\n  var applyWithSelect = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__[\"withSelect\"])(function (select, props) {\n    var _window$cf$config = window.cf.config,\n      compactInput = _window$cf$config.compactInput,\n      compactInputKey = _window$cf$config.compactInputKey;\n    var field = select('carbon-fields/metaboxes').getFieldById(props.id);\n    var value = field && field.value;\n    var name = props.name || field.name;\n\n    /**\r\n     * Wrap top-level field names in compact input key.\r\n     *\r\n     * The fields in widgets don't need this prefix because\r\n     * their input is already compacted by the `widget` namespace.\r\n     */\n    if (compactInput && !props.name && name.indexOf('widget-carbon_fields') === -1) {\n      name = \"\".concat(compactInputKey, \"[\").concat(name, \"]\");\n    }\n    return {\n      field: field,\n      name: name,\n      value: value\n    };\n  });\n  var applyWithDispatch = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__[\"withDispatch\"])(function (dispatch) {\n    var _dispatch = dispatch('carbon-fields/metaboxes'),\n      updateFieldValue = _dispatch.updateFieldValue;\n    return {\n      onChange: updateFieldValue\n    };\n  });\n  return Object(_wordpress_compose__WEBPACK_IMPORTED_MODULE_1__[\"compose\"])(applyWithSelect, applyWithDispatch)(Component);\n}, 'withField'));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/hocs/with-field/index.js?");

/***/ }),

/***/ "./packages/metaboxes/index.js":
/*!*************************************!*\
  !*** ./packages/metaboxes/index.js ***!
  \*************************************/
/*! exports provided: registerContainerType, getContainerType */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/i18n */ \"@wordpress/i18n\");\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./store */ \"./packages/metaboxes/store/index.js\");\n/* harmony import */ var _fields__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./fields */ \"./packages/metaboxes/fields/index.js\");\n/* harmony import */ var _monitors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./monitors */ \"./packages/metaboxes/monitors/index.js\");\n/* harmony import */ var _containers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./containers */ \"./packages/metaboxes/containers/index.js\");\n/* harmony import */ var _utils_is_gutenberg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/is-gutenberg */ \"./packages/metaboxes/utils/is-gutenberg.js\");\n/* harmony import */ var _containers_registry__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./containers/registry */ \"./packages/metaboxes/containers/registry.js\");\n/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, \"registerContainerType\", function() { return _containers_registry__WEBPACK_IMPORTED_MODULE_7__[\"registerContainerType\"]; });\n\n/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, \"getContainerType\", function() { return _containers_registry__WEBPACK_IMPORTED_MODULE_7__[\"getContainerType\"]; });\n\n/**\r\n * External dependencies.\r\n */\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n\n\n\n/**\r\n * Public API.\r\n */\n\n\n/**\r\n * Sets the locale data for the package type\r\n */\nObject(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__[\"setLocaleData\"])(window.cf.config.locale, 'carbon-fields-ui');\n\n/**\r\n * Determines the rendering context.\r\n *\r\n * @type {string}\r\n */\nvar context = Object(_utils_is_gutenberg__WEBPACK_IMPORTED_MODULE_6__[\"default\"])() ? 'gutenberg' : 'classic';\n\n/**\r\n * Abracadabra! Poof! Containers everywhere ...\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"addAction\"])('carbon-fields.init', 'carbon-fields/metaboxes', function () {\n  Object(_containers__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(context);\n  Object(_monitors__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(context);\n});\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/index.js?");

/***/ }),

/***/ "./packages/metaboxes/lib/constants.js":
/*!*********************************************!*\
  !*** ./packages/metaboxes/lib/constants.js ***!
  \*********************************************/
/*! exports provided: PAGE_NOW_WIDGETS, PAGE_NOW_CUSTOMIZE, CARBON_FIELDS_CONTAINER_ID_PREFIX, CARBON_FIELDS_CONTAINER_WIDGET_ID_PREFIX */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"PAGE_NOW_WIDGETS\", function() { return PAGE_NOW_WIDGETS; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"PAGE_NOW_CUSTOMIZE\", function() { return PAGE_NOW_CUSTOMIZE; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"CARBON_FIELDS_CONTAINER_ID_PREFIX\", function() { return CARBON_FIELDS_CONTAINER_ID_PREFIX; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"CARBON_FIELDS_CONTAINER_WIDGET_ID_PREFIX\", function() { return CARBON_FIELDS_CONTAINER_WIDGET_ID_PREFIX; });\nvar PAGE_NOW_WIDGETS = 'widgets.php';\nvar PAGE_NOW_CUSTOMIZE = 'customize.php';\nvar CARBON_FIELDS_CONTAINER_ID_PREFIX = 'carbon_fields_container_';\nvar CARBON_FIELDS_CONTAINER_WIDGET_ID_PREFIX = 'carbon_fields_';\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/lib/constants.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/aperture/index.js":
/*!***************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/aperture/index.js ***!
  \***************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return aperture; });\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! immer */ \"immer\");\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(immer__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! callbag-basics */ \"callbag-basics\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(callbag_basics__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _post_parent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./post-parent */ \"./packages/metaboxes/monitors/conditional-display/aperture/post-parent.js\");\n/* harmony import */ var _post_format__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./post-format */ \"./packages/metaboxes/monitors/conditional-display/aperture/post-format.js\");\n/* harmony import */ var _post_template__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./post-template */ \"./packages/metaboxes/monitors/conditional-display/aperture/post-template.js\");\n/* harmony import */ var _post_term__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./post-term */ \"./packages/metaboxes/monitors/conditional-display/aperture/post-term.js\");\n/* harmony import */ var _term_parent__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./term-parent */ \"./packages/metaboxes/monitors/conditional-display/aperture/term-parent.js\");\n/* harmony import */ var _user_role__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./user-role */ \"./packages/metaboxes/monitors/conditional-display/aperture/user-role.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n\n\n\n\n/**\r\n * The function that controls the stream of side effects.\r\n *\r\n * @param  {Object} props\r\n * @param  {string} props.context\r\n * @return {Object}\r\n */\n// eslint-disable-next-line no-unused-vars\nfunction aperture(component, _ref) {\n  var context = _ref.context;\n  var postParent$ = Object(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"applyFilters\"])(\"carbon-fields.conditional-display-post-parent.\".concat(context));\n  var postFormat$ = Object(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"applyFilters\"])(\"carbon-fields.conditional-display-post-format.\".concat(context));\n  var postTemplate$ = Object(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"applyFilters\"])(\"carbon-fields.conditional-display-post-template.\".concat(context));\n  var postTerm$ = Object(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"applyFilters\"])(\"carbon-fields.conditional-display-post-term.\".concat(context));\n  var termParent$ = Object(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"applyFilters\"])(\"carbon-fields.conditional-display-term-parent.\".concat(context));\n  var userRole$ = Object(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_1__[\"applyFilters\"])(\"carbon-fields.conditional-display-user-role.\".concat(context));\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_3__[\"pipe\"])(Object(callbag_basics__WEBPACK_IMPORTED_MODULE_3__[\"merge\"])(postParent$, postFormat$, postTemplate$, postTerm$, termParent$, userRole$), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_3__[\"scan\"])(function (previous, current) {\n    return immer__WEBPACK_IMPORTED_MODULE_0___default()(previous, function (draft) {\n      Object(lodash__WEBPACK_IMPORTED_MODULE_2__[\"assign\"])(draft, current);\n    });\n  }));\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/aperture/index.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/aperture/post-format.js":
/*!*********************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/aperture/post-format.js ***!
  \*********************************************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var callbag_of__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! callbag-of */ \"./node_modules/callbag-of/dist/callbag-of.esm.js\");\n/* harmony import */ var callbag_start_with__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! callbag-start-with */ \"./node_modules/callbag-start-with/index.js\");\n/* harmony import */ var callbag_from_delegated_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! callbag-from-delegated-event */ \"./node_modules/callbag-from-delegated-event/index.js\");\n/* harmony import */ var callbag_distinct_until_changed__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! callbag-distinct-until-changed */ \"./node_modules/callbag-distinct-until-changed/dist/callbag-distinct-until-changed.es.js\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! callbag-basics */ \"callbag-basics\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(callbag_basics__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_7__);\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * The default state.\r\n *\r\n * @type {Object}\r\n */\nvar INITIAL_STATE = {\n  post_format: 'standard'\n};\n\n/**\r\n * Extracts `post_format` from the input.\r\n *\r\n * @param  {Object} input\r\n * @return {Object}\r\n */\nfunction getPostFormatFromRadioInput(input) {\n  var value = input.value;\n\n  // Normalize the value of \"Standard\" input.\n  if (value === '0') {\n    value = 'standard';\n  }\n  return {\n    post_format: value\n  };\n}\n\n/**\r\n * Defines the side effects for Classic Editor.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__[\"addFilter\"])('carbon-fields.conditional-display-post-format.classic', 'carbon-fields/metaboxes', function () {\n  var node = document.querySelector('div#post-formats-select');\n  if (!node) {\n    return Object(callbag_of__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(INITIAL_STATE);\n  }\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_6__[\"pipe\"])(Object(callbag_from_delegated_event__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node, 'input.post-format', 'change'), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_6__[\"map\"])(function (_ref) {\n    var target = _ref.target;\n    return getPostFormatFromRadioInput(target);\n  }), Object(callbag_start_with__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(getPostFormatFromRadioInput(node.querySelector('input.post-format:checked'))));\n});\n\n/**\r\n * Defines the side effects for Gutenberg.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__[\"addFilter\"])('carbon-fields.conditional-display-post-format.gutenberg', 'carbon-fields/metaboxes', function () {\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_6__[\"pipe\"])(Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_7__[\"fromSelector\"])(Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__[\"select\"])('core/editor').getEditedPostAttribute, 'format'), Object(callbag_distinct_until_changed__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_6__[\"filter\"])(Boolean), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_6__[\"map\"])(function (postFormat) {\n    return {\n      post_format: postFormat\n    };\n  }), Object(callbag_start_with__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(INITIAL_STATE));\n});\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/aperture/post-format.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/aperture/post-parent.js":
/*!*********************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/aperture/post-parent.js ***!
  \*********************************************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ \"./node_modules/@babel/runtime/helpers/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var callbag_of__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! callbag-of */ \"./node_modules/callbag-of/dist/callbag-of.esm.js\");\n/* harmony import */ var callbag_start_with__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! callbag-start-with */ \"./node_modules/callbag-start-with/index.js\");\n/* harmony import */ var callbag_distinct_until_changed__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! callbag-distinct-until-changed */ \"./node_modules/callbag-distinct-until-changed/dist/callbag-distinct-until-changed.es.js\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! callbag-basics */ \"callbag-basics\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(callbag_basics__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _utils_get_parent_id_from_option__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../utils/get-parent-id-from-option */ \"./packages/metaboxes/monitors/conditional-display/utils/get-parent-id-from-option.js\");\n/* harmony import */ var _utils_get_level_from_option__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../utils/get-level-from-option */ \"./packages/metaboxes/monitors/conditional-display/utils/get-level-from-option.js\");\n/* harmony import */ var _utils_get_ancestors_from_option__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../utils/get-ancestors-from-option */ \"./packages/metaboxes/monitors/conditional-display/utils/get-ancestors-from-option.js\");\n\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n\n/**\r\n * The default state.\r\n *\r\n * @type {Object}\r\n */\nvar INITIAL_STATE = {\n  post_ancestors: [],\n  post_parent_id: 0,\n  post_level: 1\n};\n\n/**\r\n * Extracts the `post_ancestors`, `post_parent_id` & `post_level` from the select.\r\n *\r\n * @param  {Object} node\r\n * @return {Object}\r\n */\nfunction getParentIdAncestorsAndLevelFromSelect(node) {\n  var option = node.options[node.selectedIndex];\n  var ancestors = Object(_utils_get_ancestors_from_option__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(option);\n  var parentId = Object(_utils_get_parent_id_from_option__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(option);\n  var level = Object(_utils_get_level_from_option__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(option) + 1;\n  return {\n    post_ancestors: ancestors,\n    post_parent_id: parentId,\n    post_level: level\n  };\n}\n\n/**\r\n * Extracts `post_ancestors` from the list.\r\n *\r\n * @param  {number}   parentId\r\n * @param  {Object[]} posts\r\n * @param  {Array}    ancestors\r\n * @return {number[]}\r\n */\nfunction getAncestorsFromPostsList(parentId, posts) {\n  var ancestors = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  var parent = Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"find\"])(posts, ['id', parentId]);\n  if (!parent) {\n    return ancestors;\n  }\n  ancestors.unshift(parent.id);\n  if (parent.parent) {\n    return getAncestorsFromPostsList(parent.parent, posts, ancestors);\n  }\n  return ancestors;\n}\n\n/**\r\n * Defines the side effects for Classic Editor.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__[\"addFilter\"])('carbon-fields.conditional-display-post-parent.classic', 'carbon-fields/metaboxes', function () {\n  var node = document.querySelector('select#parent_id');\n  if (!node) {\n    return Object(callbag_of__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(INITIAL_STATE);\n  }\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_7__[\"pipe\"])(callbag_basics__WEBPACK_IMPORTED_MODULE_7__[\"fromEvent\"][\"default\"](node, 'change'), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_7__[\"map\"])(function (_ref) {\n    var target = _ref.target;\n    return getParentIdAncestorsAndLevelFromSelect(target);\n  }), Object(callbag_start_with__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(getParentIdAncestorsAndLevelFromSelect(node)));\n});\n\n/**\r\n * Defines the side effects for Gutenberg.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__[\"addFilter\"])('carbon-fields.conditional-display-post-parent.gutenberg', 'carbon-fields/metaboxes', function () {\n  var _select = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__[\"select\"])('core'),\n    getPostType = _select.getPostType,\n    getEntityRecords = _select.getEntityRecords;\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_7__[\"pipe\"])(Object(callbag_basics__WEBPACK_IMPORTED_MODULE_7__[\"combine\"])(Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_8__[\"fromSelector\"])(Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__[\"select\"])('core/editor').getCurrentPostId), Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_8__[\"fromSelector\"])(Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__[\"select\"])('core/editor').getEditedPostAttribute, 'type'), Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_8__[\"fromSelector\"])(Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__[\"select\"])('core/editor').getEditedPostAttribute, 'parent')), Object(callbag_distinct_until_changed__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(lodash__WEBPACK_IMPORTED_MODULE_6__[\"isEqual\"]), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_7__[\"map\"])(function (_ref2) {\n    var _ref3 = _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0___default()(_ref2, 3),\n      postId = _ref3[0],\n      postTypeSlug = _ref3[1],\n      parentId = _ref3[2];\n    parentId = parseInt(parentId, 10);\n    if (isNaN(parentId)) {\n      return INITIAL_STATE;\n    }\n    var postType = getPostType(postTypeSlug);\n    var isHierarchical = Object(lodash__WEBPACK_IMPORTED_MODULE_6__[\"get\"])(postType, ['hierarchical'], false);\n    if (!isHierarchical) {\n      return INITIAL_STATE;\n    }\n\n    // Borrowed from https://github.com/WordPress/gutenberg/blob/master/packages/editor/src/components/page-attributes/parent.js\n    var items = getEntityRecords('postType', postTypeSlug, {\n      per_page: -1,\n      exclude: postId,\n      parent_exclude: postId,\n      orderby: 'menu_order',\n      order: 'asc'\n    }) || [];\n    var ancestors = getAncestorsFromPostsList(parentId, items);\n    var level = ancestors.length + 1;\n    return {\n      post_ancestors: ancestors,\n      post_parent_id: parentId,\n      post_level: level\n    };\n  }));\n});\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/aperture/post-parent.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/aperture/post-template.js":
/*!***********************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/aperture/post-template.js ***!
  \***********************************************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var callbag_of__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! callbag-of */ \"./node_modules/callbag-of/dist/callbag-of.esm.js\");\n/* harmony import */ var callbag_start_with__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! callbag-start-with */ \"./node_modules/callbag-start-with/index.js\");\n/* harmony import */ var callbag_distinct_until_changed__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! callbag-distinct-until-changed */ \"./node_modules/callbag-distinct-until-changed/dist/callbag-distinct-until-changed.es.js\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! callbag-basics */ \"callbag-basics\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(callbag_basics__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_7__);\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * The default state.\r\n *\r\n * @type {Object}\r\n */\nvar INITIAL_STATE = {\n  post_template: ''\n};\n\n/**\r\n * Extracts `post_template` from the select.\r\n *\r\n * @param  {Object} node\r\n * @return {Object}\r\n */\nfunction getPostTemplateFromSelect(node) {\n  var value = node.value;\n\n  // In Gutenberg for the \"Default\" template is used an empty string.\n  if (value === 'default') {\n    value = '';\n  }\n  return {\n    post_template: value\n  };\n}\n\n/**\r\n * Defines the side effects for Classic Editor.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_3__[\"addFilter\"])('carbon-fields.conditional-display-post-template.classic', 'carbon-fields/metaboxes', function () {\n  var node = document.querySelector('select#page_template');\n  if (!node) {\n    return Object(callbag_of__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(INITIAL_STATE);\n  }\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_6__[\"pipe\"])(callbag_basics__WEBPACK_IMPORTED_MODULE_6__[\"fromEvent\"][\"default\"](node, 'change'), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_6__[\"map\"])(function (_ref) {\n    var target = _ref.target;\n    return getPostTemplateFromSelect(target);\n  }), Object(callbag_start_with__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(getPostTemplateFromSelect(node)));\n});\n\n/**\r\n * Defines the side effects for Gutenberg.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_3__[\"addFilter\"])('carbon-fields.conditional-display-post-template.gutenberg', 'carbon-fields/metaboxes', function () {\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_6__[\"pipe\"])(Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_7__[\"fromSelector\"])(Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_4__[\"select\"])('core/editor').getEditedPostAttribute, 'template'), Object(callbag_distinct_until_changed__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_6__[\"filter\"])(lodash__WEBPACK_IMPORTED_MODULE_5__[\"isString\"]), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_6__[\"map\"])(function (postTemplate) {\n    return {\n      post_template: postTemplate\n    };\n  }), Object(callbag_start_with__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(INITIAL_STATE));\n});\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/aperture/post-template.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/aperture/post-term.js":
/*!*******************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/aperture/post-term.js ***!
  \*******************************************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ \"./node_modules/@babel/runtime/helpers/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! immer */ \"immer\");\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(immer__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var callbag_start_with__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! callbag-start-with */ \"./node_modules/callbag-start-with/index.js\");\n/* harmony import */ var callbag_from_delegated_event__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! callbag-from-delegated-event */ \"./node_modules/callbag-from-delegated-event/index.js\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! callbag-basics */ \"callbag-basics\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(callbag_basics__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_9__);\n\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\nvar TAGS_DELIMITER = ',';\n\n/**\r\n * Applies a monkey patch to the specified method of `window.tagBox` API\r\n * so we can detect changes of the non-hierarchical taxonomies.\r\n *\r\n * @param  {Object} tagBox\r\n * @param  {string} method\r\n * @return {void}\r\n */\nfunction patchWordPressTagBoxAPI(tagBox, method) {\n  tagBox[\"original_\".concat(method)] = tagBox[method];\n  tagBox[method] = function () {\n    var event = new Event('change');\n    var textarea = window.jQuery(arguments.length <= 0 ? undefined : arguments[0]).closest('.postbox').find('textarea.the-tags').get(0);\n    var result = tagBox[\"original_\".concat(method)].apply(tagBox, arguments);\n    textarea.dispatchEvent(event);\n    return result;\n  };\n}\nif (window.tagBox) {\n  patchWordPressTagBoxAPI(window.tagBox, 'parseTags');\n  patchWordPressTagBoxAPI(window.tagBox, 'flushTags');\n}\n\n/**\r\n * Extracts the terms of a hierarchical taxonomy.\r\n *\r\n * @param  {string} taxonomy\r\n * @return {Object}\r\n */\nfunction getTermsFromChecklist(taxonomy) {\n  var inputs = document.querySelectorAll(\"#\".concat(taxonomy, \"checklist input[type=\\\"checkbox\\\"]:checked\"));\n  return _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1___default()(inputs).reduce(function (memo, input) {\n    var value = parseInt(input.value, 10);\n    memo[taxonomy].push(value);\n    return memo;\n  }, _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()({}, taxonomy, []));\n}\n\n/**\r\n * Extracts the terms of a non-hierarchical taxonomy.\r\n *\r\n * @param  {string} taxonomy\r\n * @return {Object}\r\n */\nfunction getTermsFromText(taxonomy) {\n  var node = document.querySelector(\"#tagsdiv-\".concat(taxonomy, \" textarea.the-tags\"));\n  var terms = node.value ? node.value.split(TAGS_DELIMITER) : [];\n  return _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()({}, taxonomy, terms);\n}\n\n/**\r\n * Keeps track of the hierarchical taxonomies like `categories`.\r\n *\r\n * @return {Function}\r\n */\nfunction trackHierarchicalTaxonomies() {\n  var nodes = document.querySelectorAll('div[id^=\"taxonomy-\"]');\n  return _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1___default()(nodes).map(function (node) {\n    var taxonomy = node.id.replace('taxonomy-', '');\n    return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"pipe\"])(Object(callbag_from_delegated_event__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(node.querySelector(\"#\".concat(taxonomy, \"checklist\")), 'input[type=\"checkbox\"]', 'change'), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"scan\"])(function (stack, _ref2) {\n      var target = _ref2.target;\n      return immer__WEBPACK_IMPORTED_MODULE_2___default()(stack, function (draft) {\n        var value = parseInt(target.value, 10);\n        if (target.checked) {\n          draft[taxonomy].push(value);\n        } else {\n          Object(lodash__WEBPACK_IMPORTED_MODULE_8__[\"pull\"])(draft[taxonomy], value);\n        }\n      });\n    }, _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()({}, taxonomy, [])), Object(callbag_start_with__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(getTermsFromChecklist(taxonomy)));\n  });\n}\n\n/**\r\n * Keeps track of the non-hierarchical taxonomies like `tags`.\r\n *\r\n * @return {Function}\r\n */\nfunction trackNonHierarchicalTaxonomies() {\n  var nodes = document.querySelectorAll('div[id^=\"tagsdiv-\"]');\n  return _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1___default()(nodes).map(function (node) {\n    var taxonomy = node.id.replace('tagsdiv-', '');\n    return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"pipe\"])(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"fromEvent\"][\"default\"](node.querySelector('textarea.the-tags'), 'change'), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"map\"])(function (_ref3) {\n      var target = _ref3.target;\n      return _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()({}, taxonomy, target.value ? target.value.split(TAGS_DELIMITER) : []);\n    }), Object(callbag_start_with__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(getTermsFromText(taxonomy)));\n  });\n}\n\n/**\r\n * Defines the side effects for Classic Editor.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_6__[\"addFilter\"])('carbon-fields.conditional-display-post-term.classic', 'carbon-fields/metaboxes', function () {\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"pipe\"])(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"merge\"].apply(void 0, _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1___default()(trackHierarchicalTaxonomies()).concat(_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1___default()(trackNonHierarchicalTaxonomies()))), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"scan\"])(function (previous, current) {\n    return {\n      post_term: _objectSpread(_objectSpread({}, previous.post_term), current)\n    };\n  }, {\n    post_term: {}\n  }));\n});\n\n/**\r\n * Defines the side effects for Gutenberg.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_6__[\"addFilter\"])('carbon-fields.conditional-display-post-term.gutenberg', 'carbon-fields/metaboxes', function () {\n  var _select = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_7__[\"select\"])('core'),\n    getTaxonomies = _select.getTaxonomies;\n  var _select2 = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_7__[\"select\"])('core/editor'),\n    getEditedPostAttribute = _select2.getEditedPostAttribute;\n\n  // Borrowed from https://github.com/WordPress/gutenberg/blob/master/packages/editor/src/components/post-taxonomies/index.js\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"pipe\"])(Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_9__[\"fromSelector\"])(getTaxonomies, {\n    per_page: -1\n  }), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"filter\"])(Boolean), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"map\"])(function (taxonomies) {\n    var pairs = taxonomies.map(function (taxonomy) {\n      return [taxonomy.slug, getEditedPostAttribute(taxonomy.rest_base) || []];\n    });\n    return {\n      post_term: Object(lodash__WEBPACK_IMPORTED_MODULE_8__[\"fromPairs\"])(pairs)\n    };\n  }));\n});\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/aperture/post-term.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/aperture/term-parent.js":
/*!*********************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/aperture/term-parent.js ***!
  \*********************************************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var callbag_of__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! callbag-of */ \"./node_modules/callbag-of/dist/callbag-of.esm.js\");\n/* harmony import */ var callbag_start_with__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! callbag-start-with */ \"./node_modules/callbag-start-with/index.js\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! callbag-basics */ \"callbag-basics\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(callbag_basics__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_get_parent_id_from_option__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/get-parent-id-from-option */ \"./packages/metaboxes/monitors/conditional-display/utils/get-parent-id-from-option.js\");\n/* harmony import */ var _utils_get_level_from_option__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/get-level-from-option */ \"./packages/metaboxes/monitors/conditional-display/utils/get-level-from-option.js\");\n/* harmony import */ var _utils_get_ancestors_from_option__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/get-ancestors-from-option */ \"./packages/metaboxes/monitors/conditional-display/utils/get-ancestors-from-option.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n\n/**\r\n * The default state.\r\n *\r\n * @type {Object}\r\n */\nvar INITIAL_STATE = {\n  term_ancestors: [],\n  term_parent: 0,\n  term_level: 1\n};\n\n/**\r\n * Extracts the `term_ancestors`, `term_parent` & `term_level` from the select.\r\n *\r\n * @param  {Object} node\r\n * @return {Object}\r\n */\nfunction getParentIdAncestorsAndLevelFromSelect(node) {\n  var option = node.options[node.selectedIndex];\n  var ancestors = Object(_utils_get_ancestors_from_option__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(option);\n  var parentId = Object(_utils_get_parent_id_from_option__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(option);\n  var level = Object(_utils_get_level_from_option__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(option) + 1;\n  return {\n    term_ancestors: ancestors,\n    term_parent: parentId,\n    term_level: level\n  };\n}\n\n/**\r\n * Defines the side effects for Classic Editor.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_2__[\"addFilter\"])('carbon-fields.conditional-display-term-parent.classic', 'carbon-fields/metaboxes', function () {\n  var node = document.querySelector('select#parent');\n  if (!node) {\n    return Object(callbag_of__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(INITIAL_STATE);\n  }\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_3__[\"pipe\"])(callbag_basics__WEBPACK_IMPORTED_MODULE_3__[\"fromEvent\"][\"default\"](node, 'change'), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_3__[\"map\"])(function (_ref) {\n    var target = _ref.target;\n    return getParentIdAncestorsAndLevelFromSelect(target);\n  }), Object(callbag_start_with__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(getParentIdAncestorsAndLevelFromSelect(node)));\n});\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/aperture/term-parent.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/aperture/user-role.js":
/*!*******************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/aperture/user-role.js ***!
  \*******************************************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var callbag_of__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! callbag-of */ \"./node_modules/callbag-of/dist/callbag-of.esm.js\");\n/* harmony import */ var callbag_start_with__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! callbag-start-with */ \"./node_modules/callbag-start-with/index.js\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/hooks */ \"@wordpress/hooks\");\n/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! callbag-basics */ \"callbag-basics\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(callbag_basics__WEBPACK_IMPORTED_MODULE_3__);\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n/**\r\n * The default state.\r\n *\r\n * @type {Object}\r\n */\nvar INITIAL_STATE = {\n  user_role: ''\n};\n\n/**\r\n * Extracts `user_role` from a select.\r\n *\r\n * @param  {Object} node\r\n * @return {Object}\r\n */\nfunction getRoleFromSelect(node) {\n  return {\n    user_role: node.value\n  };\n}\n\n/**\r\n * Defines the side effects for Classic Editor.\r\n */\nObject(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_2__[\"addFilter\"])('carbon-fields.conditional-display-user-role.classic', 'carbon-fields/metaboxes', function () {\n  var node = document.querySelector('select#role');\n  if (!node) {\n    var fieldset = document.querySelector('fieldset[data-profile-role]');\n\n    // The selectbox doesn't exist on the \"Profile\" page.\n    // So we need to read the role from the container in DOM.\n    if (fieldset) {\n      return Object(callbag_of__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        user_role: fieldset.dataset.profileRole\n      });\n    }\n    return Object(callbag_of__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(INITIAL_STATE);\n  }\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_3__[\"pipe\"])(callbag_basics__WEBPACK_IMPORTED_MODULE_3__[\"fromEvent\"][\"default\"](node, 'change'), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_3__[\"map\"])(function (_ref) {\n    var target = _ref.target;\n    return getRoleFromSelect(target);\n  }), Object(callbag_start_with__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(getRoleFromSelect(node)));\n});\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/aperture/user-role.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/comparers/any-contain.js":
/*!**********************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/comparers/any-contain.js ***!
  \**********************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./base */ \"./packages/metaboxes/monitors/conditional-display/comparers/base.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_objectSpread(_objectSpread({}, _base__WEBPACK_IMPORTED_MODULE_2__[\"default\"]), {}, {\n  /**\r\n   * @inheritdoc\r\n   */\n  operators: ['IN', 'NOT IN'],\n  /**\r\n   * @inheritdoc\r\n   */\n  evaluate: function evaluate(a, operator, b) {\n    switch (operator) {\n      case 'IN':\n        return Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"intersection\"])(a, b).length > 0;\n      case 'NOT IN':\n        return Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"intersection\"])(a, b).length === 0;\n      default:\n        return false;\n    }\n  }\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/comparers/any-contain.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/comparers/any-equality.js":
/*!***********************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/comparers/any-equality.js ***!
  \***********************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./base */ \"./packages/metaboxes/monitors/conditional-display/comparers/base.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_objectSpread(_objectSpread({}, _base__WEBPACK_IMPORTED_MODULE_2__[\"default\"]), {}, {\n  /**\r\n   * @inheritdoc\r\n   */\n  operators: ['=', '!='],\n  /**\r\n   * @inheritdoc\r\n   */\n  evaluate: function evaluate(a, operator, b) {\n    switch (operator) {\n      case '=':\n        return Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"includes\"])(a, b);\n      case '!=':\n        return !Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"includes\"])(a, b);\n      default:\n        return false;\n    }\n  }\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/comparers/any-equality.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/comparers/base.js":
/*!***************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/comparers/base.js ***!
  \***************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  /**\r\n   * The supported operators.\r\n   *\r\n   * @type {string[]}\r\n   */\n  operators: [],\n  /**\r\n   * Checks if the operator is supported.\r\n   *\r\n   * @param  {string} operator\r\n   * @return {boolean}\r\n   */\n  isOperatorSupported: function isOperatorSupported(operator) {\n    return this.operators.indexOf(operator) > -1;\n  },\n  /**\r\n   * Performs the comparison.\r\n   *\r\n   * @param  {mixed}  a\r\n   * @param  {string} operator\r\n   * @param  {mixed}  b\r\n   * @return {boolean}\r\n   */\n  evaluate: function evaluate() {\n    return false;\n  }\n});\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/comparers/base.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/comparers/contain.js":
/*!******************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/comparers/contain.js ***!
  \******************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base */ \"./packages/metaboxes/monitors/conditional-display/comparers/base.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * Internal dependencies.\r\n */\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_objectSpread(_objectSpread({}, _base__WEBPACK_IMPORTED_MODULE_1__[\"default\"]), {}, {\n  /**\r\n   * @inheritdoc\r\n   */\n  operators: ['IN', 'NOT IN'],\n  /**\r\n   * @inheritdoc\r\n   */\n  evaluate: function evaluate(a, operator, b) {\n    switch (operator) {\n      case 'IN':\n        return b.indexOf(a) > -1;\n      case 'NOT IN':\n        return b.indexOf(a) === -1;\n      default:\n        return false;\n    }\n  }\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/comparers/contain.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/comparers/equality.js":
/*!*******************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/comparers/equality.js ***!
  \*******************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base */ \"./packages/metaboxes/monitors/conditional-display/comparers/base.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/* eslint eqeqeq: \"off\" */\n\n/**\r\n * Internal dependencies.\r\n */\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_objectSpread(_objectSpread({}, _base__WEBPACK_IMPORTED_MODULE_1__[\"default\"]), {}, {\n  /**\r\n   * @inheritdoc\r\n   */\n  operators: ['=', '!='],\n  /**\r\n   * @inheritdoc\r\n   */\n  evaluate: function evaluate(a, operator, b) {\n    switch (operator) {\n      case '=':\n        return a == b;\n      case '!=':\n        return a != b;\n      default:\n        return false;\n    }\n  }\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/comparers/equality.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/comparers/scalar.js":
/*!*****************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/comparers/scalar.js ***!
  \*****************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base */ \"./packages/metaboxes/monitors/conditional-display/comparers/base.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * Internal dependencies.\r\n */\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_objectSpread(_objectSpread({}, _base__WEBPACK_IMPORTED_MODULE_1__[\"default\"]), {}, {\n  /**\r\n   * @inheritdoc\r\n   */\n  operators: ['>', '>=', '<', '<='],\n  /**\r\n   * @inheritdoc\r\n   */\n  evaluate: function evaluate(a, operator, b) {\n    switch (operator) {\n      case '>':\n        return a > b;\n      case '>=':\n        return a >= b;\n      case '<':\n        return a < b;\n      case '<=':\n        return a <= b;\n      default:\n        return false;\n    }\n  }\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/comparers/scalar.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/conditions/base.js":
/*!****************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/conditions/base.js ***!
  \****************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/i18n */ \"@wordpress/i18n\");\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _comparers_equality__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../comparers/equality */ \"./packages/metaboxes/monitors/conditional-display/comparers/equality.js\");\n/* harmony import */ var _comparers_contain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../comparers/contain */ \"./packages/metaboxes/monitors/conditional-display/comparers/contain.js\");\n/* harmony import */ var _comparers_scalar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../comparers/scalar */ \"./packages/metaboxes/monitors/conditional-display/comparers/scalar.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  /**\r\n   * The supported comparers.\r\n   *\r\n   * @type {Function[]}\r\n   */\n  comparers: [_comparers_equality__WEBPACK_IMPORTED_MODULE_2__[\"default\"], _comparers_contain__WEBPACK_IMPORTED_MODULE_3__[\"default\"], _comparers_scalar__WEBPACK_IMPORTED_MODULE_4__[\"default\"]],\n  /**\r\n   * Checks if the condition is fulfiled.\r\n   *\r\n   * @param  {Object} definition\r\n   * @param  {Object} values\r\n   * @return {boolean}\r\n   */\n  isFulfiled: function isFulfiled(definition, values) {\n    var compare = definition.compare,\n      value = definition.value;\n    return this.firstComparerIsCorrect(this.getEnvironmentValue(definition, values), compare, value);\n  },\n  /**\r\n   * Checks if any comparer is correct for `a` and `b`.\r\n   *\r\n   * @param  {mixed}  a\r\n   * @param  {string} operator\r\n   * @param  {mixed}  b\r\n   * @return {boolean}\r\n   */\n  firstComparerIsCorrect: function firstComparerIsCorrect(a, operator, b) {\n    var comparer = Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"find\"])(this.comparers, function (item) {\n      return item.isOperatorSupported(operator);\n    });\n    if (!comparer) {\n      // eslint-disable-next-line no-console\n      console.error(Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__[\"sprintf\"])(Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__[\"__\"])('Unsupported container condition comparison operator used - \"%1$s\".', 'carbon-fields-ui'), operator));\n      return false;\n    }\n    return comparer.evaluate(a, operator, b);\n  },\n  /**\r\n   * Returns the value from the environment.\r\n   *\r\n   * @param  {Object} definition\r\n   * @param  {Object} values\r\n   * @return {Object}\r\n   */\n  getEnvironmentValue: function getEnvironmentValue(definition, values) {\n    return values[definition.type];\n  }\n});\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/conditions/base.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/conditions/boolean.js":
/*!*******************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/conditions/boolean.js ***!
  \*******************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base */ \"./packages/metaboxes/monitors/conditional-display/conditions/base.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * Internal dependencies.\r\n */\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_objectSpread(_objectSpread({}, _base__WEBPACK_IMPORTED_MODULE_1__[\"default\"]), {}, {\n  /**\r\n   * @inheritdoc\r\n   */\n  getEnvironmentValue: function getEnvironmentValue() {\n    return true;\n  }\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/conditions/boolean.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/conditions/post-ancestor-id.js":
/*!****************************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/conditions/post-ancestor-id.js ***!
  \****************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _comparers_any_equality__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../comparers/any-equality */ \"./packages/metaboxes/monitors/conditional-display/comparers/any-equality.js\");\n/* harmony import */ var _comparers_any_contain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../comparers/any-contain */ \"./packages/metaboxes/monitors/conditional-display/comparers/any-contain.js\");\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./base */ \"./packages/metaboxes/monitors/conditional-display/conditions/base.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_objectSpread(_objectSpread({}, _base__WEBPACK_IMPORTED_MODULE_4__[\"default\"]), {}, {\n  /**\r\n   * @inheritdoc\r\n   */\n  comparers: [_comparers_any_equality__WEBPACK_IMPORTED_MODULE_2__[\"default\"], _comparers_any_contain__WEBPACK_IMPORTED_MODULE_3__[\"default\"]],\n  /**\r\n   * @inheritdoc\r\n   */\n  getEnvironmentValue: function getEnvironmentValue(definition, values) {\n    return Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"get\"])(values, 'post_ancestors', []);\n  }\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/conditions/post-ancestor-id.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/conditions/post-template.js":
/*!*************************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/conditions/post-template.js ***!
  \*************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./base */ \"./packages/metaboxes/monitors/conditional-display/conditions/base.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_objectSpread(_objectSpread({}, _base__WEBPACK_IMPORTED_MODULE_2__[\"default\"]), {}, {\n  /**\r\n   * @inheritdoc\r\n   */\n  isFulfiled: function isFulfiled(definition, values) {\n    definition = _objectSpread({}, definition);\n\n    // In Gutenberg for the \"Default\" template is used an empty string.\n    if (definition.value === 'default') {\n      definition.value = '';\n    } else if (Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"isArray\"])(definition.value)) {\n      var defaultIndex = definition.value.indexOf('default');\n      if (defaultIndex !== -1) {\n        definition.value[defaultIndex] = '';\n      }\n    }\n    return _base__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isFulfiled(definition, values);\n  }\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/conditions/post-template.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/conditions/post-term.js":
/*!*********************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/conditions/post-term.js ***!
  \*********************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _comparers_any_equality__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../comparers/any-equality */ \"./packages/metaboxes/monitors/conditional-display/comparers/any-equality.js\");\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base */ \"./packages/metaboxes/monitors/conditional-display/conditions/base.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_objectSpread(_objectSpread({}, _base__WEBPACK_IMPORTED_MODULE_3__[\"default\"]), {}, {\n  /**\r\n   * @inheritdoc\r\n   */\n  comparers: [_comparers_any_equality__WEBPACK_IMPORTED_MODULE_2__[\"default\"]],\n  /**\r\n   * @inheritdoc\r\n   */\n  isFulfiled: function isFulfiled(definition, values) {\n    var _this = this;\n    var compare = definition.compare,\n      value = definition.value;\n    if (Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"isArray\"])(value)) {\n      var method;\n      switch (compare) {\n        case 'IN':\n          compare = '=';\n          method = 'some';\n          break;\n        case 'NOT IN':\n          compare = '!=';\n          method = 'every';\n          break;\n        default:\n          return false;\n      }\n      var results = value.map(function (descriptor) {\n        return _this.isFulfiled(_objectSpread(_objectSpread({}, definition), {}, {\n          compare: compare,\n          value: descriptor\n        }), values);\n      });\n      return results[method](Boolean);\n    }\n\n    // TODO: Improve value resolution in context of Gutenberg\n    value = value.taxonomy_object.hierarchical ? value.term_object.term_id : value.term_object.name;\n    return this.firstComparerIsCorrect(this.getEnvironmentValue(definition, values), compare, value);\n  },\n  /**\r\n   * @inheritdoc\r\n   */\n  getEnvironmentValue: function getEnvironmentValue(definition, values) {\n    return Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"get\"])(values, \"post_term.\".concat(definition.value.taxonomy), []);\n  }\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/conditions/post-term.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/conditions/term-ancestor-id.js":
/*!****************************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/conditions/term-ancestor-id.js ***!
  \****************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _comparers_any_equality__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../comparers/any-equality */ \"./packages/metaboxes/monitors/conditional-display/comparers/any-equality.js\");\n/* harmony import */ var _comparers_any_contain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../comparers/any-contain */ \"./packages/metaboxes/monitors/conditional-display/comparers/any-contain.js\");\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./base */ \"./packages/metaboxes/monitors/conditional-display/conditions/base.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_objectSpread(_objectSpread({}, _base__WEBPACK_IMPORTED_MODULE_4__[\"default\"]), {}, {\n  /**\r\n   * @inheritdoc\r\n   */\n  comparers: [_comparers_any_equality__WEBPACK_IMPORTED_MODULE_2__[\"default\"], _comparers_any_contain__WEBPACK_IMPORTED_MODULE_3__[\"default\"]],\n  /**\r\n   * @inheritdoc\r\n   */\n  isFulfiled: function isFulfiled(definition, values) {\n    var compare = definition.compare;\n    var value = definition.value;\n    if (Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"isArray\"])(value)) {\n      value = value.map(function (item) {\n        return item.term_object.term_id;\n      });\n    } else if (Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"isPlainObject\"])(value)) {\n      value = value.term_object.term_id;\n    }\n    return this.firstComparerIsCorrect(this.getEnvironmentValue(definition, values), compare, value);\n  },\n  /**\r\n   * @inheritdoc\r\n   */\n  getEnvironmentValue: function getEnvironmentValue(definition, values) {\n    return Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"get\"])(values, 'term_ancestors', []);\n  }\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/conditions/term-ancestor-id.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/conditions/term-parent-id.js":
/*!**************************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/conditions/term-parent-id.js ***!
  \**************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./base */ \"./packages/metaboxes/monitors/conditional-display/conditions/base.js\");\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_objectSpread(_objectSpread({}, _base__WEBPACK_IMPORTED_MODULE_2__[\"default\"]), {}, {\n  /**\r\n   * @inheritdoc\r\n   */\n  isFulfiled: function isFulfiled(definition, values) {\n    var compare = definition.compare;\n    var value = definition.value;\n    if (Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"isArray\"])(value)) {\n      value = value.map(function (item) {\n        return item.term_object.term_id;\n      });\n    } else if (Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"isPlainObject\"])(value)) {\n      value = value.term_object.term_id;\n    }\n    return this.firstComparerIsCorrect(this.getEnvironmentValue(definition, values), compare, value);\n  }\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/conditions/term-parent-id.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/handler/index.js":
/*!**************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/handler/index.js ***!
  \**************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return handler; });\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ \"./node_modules/@babel/runtime/helpers/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/i18n */ \"@wordpress/i18n\");\n/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\");\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _containers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../containers */ \"./packages/metaboxes/containers/index.js\");\n/* harmony import */ var _conditions_base__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../conditions/base */ \"./packages/metaboxes/monitors/conditional-display/conditions/base.js\");\n/* harmony import */ var _conditions_boolean__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../conditions/boolean */ \"./packages/metaboxes/monitors/conditional-display/conditions/boolean.js\");\n/* harmony import */ var _conditions_post_term__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../conditions/post-term */ \"./packages/metaboxes/monitors/conditional-display/conditions/post-term.js\");\n/* harmony import */ var _conditions_post_template__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../conditions/post-template */ \"./packages/metaboxes/monitors/conditional-display/conditions/post-template.js\");\n/* harmony import */ var _conditions_post_ancestor_id__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../conditions/post-ancestor-id */ \"./packages/metaboxes/monitors/conditional-display/conditions/post-ancestor-id.js\");\n/* harmony import */ var _conditions_term_parent_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../conditions/term-parent-id */ \"./packages/metaboxes/monitors/conditional-display/conditions/term-parent-id.js\");\n/* harmony import */ var _conditions_term_ancestor_id__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../conditions/term-ancestor-id */ \"./packages/metaboxes/monitors/conditional-display/conditions/term-ancestor-id.js\");\n/* harmony import */ var _containers_root_registry__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../../containers/root-registry */ \"./packages/metaboxes/containers/root-registry.js\");\n\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n\n\n\n\n\n\n\n/**\r\n * Keeps track of supported conditions.\r\n *\r\n * @type {Object}\r\n */\nvar conditions = {\n  \"boolean\": _conditions_boolean__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n  post_term: _conditions_post_term__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n  post_ancestor_id: _conditions_post_ancestor_id__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n  post_parent_id: _conditions_base__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n  post_level: _conditions_base__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n  post_format: _conditions_base__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n  post_template: _conditions_post_template__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n  term_level: _conditions_base__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n  term_parent: _conditions_term_parent_id__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n  term_ancestor: _conditions_term_ancestor_id__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n  user_role: _conditions_base__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n};\n\n/**\r\n * Walks through the definitions and evaluates the conditions.\r\n *\r\n * @param  {Object[]} definitions\r\n * @param  {Object}   values\r\n * @param  {string}   relation\r\n * @return {boolean}\r\n */\nfunction evaluate(definitions, values, relation) {\n  var results = definitions.map(function (definition) {\n    if (!definition.relation) {\n      var condition = Object(lodash__WEBPACK_IMPORTED_MODULE_2__[\"get\"])(conditions, definition.type);\n      if (condition) {\n        return condition.isFulfiled(definition, values);\n      } else {\n        // eslint-disable-line no-else-return\n        // eslint-disable-next-line no-console\n        console.error(Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__[\"sprintf\"])(Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__[\"__\"])('Unsupported container condition - \"%1$s\".', 'carbon-fields-ui'), definition.type));\n        return false;\n      }\n    } else {\n      // eslint-disable-line no-else-return\n      return evaluate(definition.conditions, values, definition.relation);\n    }\n  });\n  switch (relation) {\n    case 'AND':\n      return results.every(Boolean);\n    case 'OR':\n      return results.some(Boolean);\n    default:\n      // eslint-disable-next-line no-console\n      console.error(Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__[\"sprintf\"])(Object(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__[\"__\"])('Unsupported container condition relation used - \"%1$s\".', 'carbon-fields-ui'), relation));\n      return false;\n  }\n}\n\n/**\r\n * The function that causes the side effects.\r\n *\r\n * @param  {Object} props\r\n * @param  {Object} props.containers\r\n * @param  {string} props.context\r\n * @return {Function}\r\n */\nfunction handler(_ref) {\n  var containers = _ref.containers,\n    context = _ref.context;\n  return function (effect) {\n    var results = Object(lodash__WEBPACK_IMPORTED_MODULE_2__[\"map\"])(containers, function (container, id) {\n      return [id, evaluate(container.conditions.conditions, effect, container.conditions.relation)];\n    });\n    results.forEach(function (_ref2) {\n      var _ref3 = _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0___default()(_ref2, 2),\n        id = _ref3[0],\n        result = _ref3[1];\n      var postboxNode = document.getElementById(id);\n      var containerNode = document.querySelector(\".container-\".concat(id));\n      var isMounted = !!containerNode.dataset.mounted;\n      if (postboxNode) {\n        postboxNode.hidden = !result;\n      }\n      if (containerNode) {\n        if (_wordpress_element__WEBPACK_IMPORTED_MODULE_3__[\"createRoot\"]) {\n          var containerRoot = Object(_containers_root_registry__WEBPACK_IMPORTED_MODULE_12__[\"getContainerRoot\"])(id);\n          if (result && !containerRoot) {\n            Object(_containers__WEBPACK_IMPORTED_MODULE_4__[\"renderContainer\"])(containers[id], context);\n          }\n          if (!result && containerRoot) {\n            containerRoot.unmount();\n          }\n        } else {\n          if (result && !isMounted) {\n            Object(_containers__WEBPACK_IMPORTED_MODULE_4__[\"renderContainer\"])(containers[id], context);\n          }\n          if (!result && isMounted) {\n            var _containerNode$datase, _containerNode$_react;\n            containerNode === null || containerNode === void 0 ? true : (_containerNode$datase = containerNode.dataset) === null || _containerNode$datase === void 0 ? true : delete _containerNode$datase.mounted;\n\n            // Rely on React's internals instead of `unmountComponentAtNode`\n            // due to https://github.com/facebook/react/issues/13690.\n            // TODO: Conditionally render the fields in the container, this way\n            // we can move away from mount/unmount cycles.\n            containerNode === null || containerNode === void 0 ? void 0 : (_containerNode$_react = containerNode._reactRootContainer) === null || _containerNode$_react === void 0 ? void 0 : _containerNode$_react.unmount();\n          }\n        }\n      }\n    });\n  };\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/handler/index.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/index.js":
/*!******************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/index.js ***!
  \******************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/compose */ \"@wordpress/compose\");\n/* harmony import */ var _wordpress_compose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_compose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! refract-callbag */ \"refract-callbag\");\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(refract_callbag__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _aperture__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./aperture */ \"./packages/metaboxes/monitors/conditional-display/aperture/index.js\");\n/* harmony import */ var _handler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./handler */ \"./packages/metaboxes/monitors/conditional-display/handler/index.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n/**\r\n * Performs the evaluation of conditions.\r\n *\r\n * @return {null}\r\n */\nfunction ConditionalDisplay() {\n  return null;\n}\nvar applyWithSelect = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__[\"withSelect\"])(function (select) {\n  var containers = select('carbon-fields/metaboxes').getContainers();\n  return {\n    containers: containers\n  };\n});\nvar applyWithEffects = Object(refract_callbag__WEBPACK_IMPORTED_MODULE_2__[\"withEffects\"])(_aperture__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n  handler: _handler__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (Object(_wordpress_compose__WEBPACK_IMPORTED_MODULE_0__[\"compose\"])(applyWithSelect, applyWithEffects)(ConditionalDisplay));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/index.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/utils/get-ancestors-from-option.js":
/*!********************************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/utils/get-ancestors-from-option.js ***!
  \********************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return getAncestorsFromOption; });\n/* harmony import */ var _get_level_from_option__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./get-level-from-option */ \"./packages/metaboxes/monitors/conditional-display/utils/get-level-from-option.js\");\n/**\r\n * Internal dependencies.\r\n */\n\n\n/**\r\n * Extracts the ancestors of the post/term from an option.\r\n *\r\n * @param  {Object} option\r\n * @return {number[]}\r\n */\nfunction getAncestorsFromOption(option) {\n  var ancestors = [];\n  var previousOption = option;\n  var level = Object(_get_level_from_option__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(option);\n  while (level > 0 && previousOption) {\n    if (Object(_get_level_from_option__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(previousOption) !== level) {\n      previousOption = previousOption.previousSibling;\n\n      // Skip this iteration because the option isn't an ancestor.\n      continue;\n    }\n    var id = parseInt(previousOption.value, 10);\n    if (id > 0) {\n      ancestors.unshift(id);\n    }\n    previousOption = previousOption.previousSibling;\n    level--;\n  }\n  return ancestors;\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/utils/get-ancestors-from-option.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/utils/get-level-from-option.js":
/*!****************************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/utils/get-level-from-option.js ***!
  \****************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return getLevelFromOption; });\n/**\r\n * Extracts the level from an option.\r\n *\r\n * @param  {Object} option\r\n * @return {number}\r\n */\nfunction getLevelFromOption(option) {\n  var level = 0;\n  if (option.className) {\n    var matches = option.className.match(/^level-(\\d+)/);\n    if (matches) {\n      level = parseInt(matches[1], 10) + 1;\n    }\n  }\n  return level;\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/utils/get-level-from-option.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/conditional-display/utils/get-parent-id-from-option.js":
/*!********************************************************************************************!*\
  !*** ./packages/metaboxes/monitors/conditional-display/utils/get-parent-id-from-option.js ***!
  \********************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return getParentIdFromOption; });\n/**\r\n * Extracts the id of the post/term parent from an option.\r\n *\r\n * @param  {Object} option\r\n * @return {number}\r\n */\nfunction getParentIdFromOption(option) {\n  var value = parseInt(option.value, 10);\n  return !isNaN(value) && value >= 0 ? value : 0;\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/conditional-display/utils/get-parent-id-from-option.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/index.js":
/*!**********************************************!*\
  !*** ./packages/metaboxes/monitors/index.js ***!
  \**********************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(__webpack_provided_wp_dot_element) {/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return initializeMonitors; });\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\");\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _save_lock__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./save-lock */ \"./packages/metaboxes/monitors/save-lock/index.js\");\n/* harmony import */ var _conditional_display__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conditional-display */ \"./packages/metaboxes/monitors/conditional-display/index.js\");\n/* harmony import */ var _widget_handler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./widget-handler */ \"./packages/metaboxes/monitors/widget-handler/index.js\");\n/* harmony import */ var _revisions_flag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./revisions-flag */ \"./packages/metaboxes/monitors/revisions-flag/index.js\");\n/* harmony import */ var _utils_is_gutenberg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/is-gutenberg */ \"./packages/metaboxes/utils/is-gutenberg.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/constants */ \"./packages/metaboxes/lib/constants.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n\n\n\n\n/**\r\n * Initializes the monitors.\r\n *\r\n * @param  {string} context\r\n * @return {void}\r\n */\nfunction initializeMonitors(context) {\n  var pagenow = window.cf.config.pagenow;\n  var MonitorElement = document.createElement('div');\n  var MonitorComponent = __webpack_provided_wp_dot_element.createElement(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__[\"Fragment\"], null, !Object(_utils_is_gutenberg__WEBPACK_IMPORTED_MODULE_5__[\"default\"])() && __webpack_provided_wp_dot_element.createElement(_save_lock__WEBPACK_IMPORTED_MODULE_1__[\"default\"], null), (pagenow === _lib_constants__WEBPACK_IMPORTED_MODULE_6__[\"PAGE_NOW_WIDGETS\"] || pagenow === _lib_constants__WEBPACK_IMPORTED_MODULE_6__[\"PAGE_NOW_CUSTOMIZE\"]) && __webpack_provided_wp_dot_element.createElement(_widget_handler__WEBPACK_IMPORTED_MODULE_3__[\"default\"], null), __webpack_provided_wp_dot_element.createElement(_conditional_display__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n    context: context\n  }));\n  if (_wordpress_element__WEBPACK_IMPORTED_MODULE_0__[\"createRoot\"]) {\n    Object(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__[\"createRoot\"])(MonitorElement).render(MonitorComponent);\n  } else {\n    Object(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__[\"render\"])(MonitorComponent, MonitorElement);\n  }\n  var postStuffNode = document.querySelector('#poststuff');\n  if (postStuffNode) {\n    var postStuffElement = document.createElement('div');\n    var postStuffComponenet = __webpack_provided_wp_dot_element.createElement(_revisions_flag__WEBPACK_IMPORTED_MODULE_4__[\"default\"], null);\n    var postStuffChildElement = postStuffNode.appendChild(postStuffElement);\n    if (_wordpress_element__WEBPACK_IMPORTED_MODULE_0__[\"createRoot\"]) {\n      Object(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__[\"createRoot\"])(postStuffChildElement).render(postStuffComponenet);\n    } else {\n      Object(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__[\"render\"])(postStuffComponenet, postStuffChildElement);\n    }\n  }\n}\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\")))\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/index.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/revisions-flag/index.js":
/*!*************************************************************!*\
  !*** ./packages/metaboxes/monitors/revisions-flag/index.js ***!
  \*************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(__webpack_provided_wp_dot_element) {/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Renders the input used to notify the backend about the changes.\r\n *\r\n * @param  {Object}  props\r\n * @param  {boolean} props.isDirty\r\n * @return {mixed}\r\n */\nfunction RevisionsFlag(props) {\n  return __webpack_provided_wp_dot_element.createElement(\"input\", {\n    type: \"hidden\",\n    name: window.cf.config.revisionsInputKey,\n    disabled: !props.isDirty,\n    value: \"1\"\n  });\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__[\"withSelect\"])(function (select) {\n  return {\n    isDirty: select('carbon-fields/metaboxes').isDirty()\n  };\n})(RevisionsFlag));\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\")))\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/revisions-flag/index.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/save-lock/index.js":
/*!********************************************************!*\
  !*** ./packages/metaboxes/monitors/save-lock/index.js ***!
  \********************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! refract-callbag */ \"refract-callbag\");\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(refract_callbag__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_2__);\n/**\r\n * External dependencies.\r\n */\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * Toggles the ability to save the page.\r\n *\r\n * @return {null}\r\n */\nfunction SaveLock() {\n  return null;\n}\n\n/**\r\n * The function that controls the stream of side effects.\r\n *\r\n * @return {Object}\r\n */\nfunction aperture() {\n  return Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_2__[\"fromSelector\"])(Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__[\"select\"])('carbon-fields/metaboxes').isSavingLocked);\n}\n\n/**\r\n * The function that causes the side effects.\r\n *\r\n * @return {Function}\r\n */\nfunction handler() {\n  return function (isLocked) {\n    var nodes = document.querySelectorAll(\"\\n\\t\\t\\t#publishing-action input#publish,\\n\\t\\t\\t#publishing-action input#save,\\n\\t\\t\\t#addtag input#submit,\\n\\t\\t\\t#edittag input[type=\\\"submit\\\"],\\n\\t\\t\\t#your-profile input#submit\\n\\t\\t\");\n    nodes.forEach(function (node) {\n      node.disabled = isLocked;\n    });\n  };\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (Object(refract_callbag__WEBPACK_IMPORTED_MODULE_0__[\"withEffects\"])(aperture, {\n  handler: handler\n})(SaveLock));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/save-lock/index.js?");

/***/ }),

/***/ "./packages/metaboxes/monitors/widget-handler/index.js":
/*!*************************************************************!*\
  !*** ./packages/metaboxes/monitors/widget-handler/index.js ***!
  \*************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ \"./node_modules/@babel/runtime/helpers/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/element */ \"@wordpress/element\");\n/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! refract-callbag */ \"refract-callbag\");\n/* harmony import */ var refract_callbag__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(refract_callbag__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! callbag-basics */ \"callbag-basics\");\n/* harmony import */ var callbag_basics__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(callbag_basics__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _utils_urldecode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/urldecode */ \"./packages/metaboxes/utils/urldecode.js\");\n/* harmony import */ var _utils_flatten_field__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/flatten-field */ \"./packages/metaboxes/utils/flatten-field.js\");\n/* harmony import */ var _utils_from_event_pattern__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../utils/from-event-pattern */ \"./packages/metaboxes/utils/from-event-pattern.js\");\n/* harmony import */ var _containers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../containers */ \"./packages/metaboxes/containers/index.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../lib/constants */ \"./packages/metaboxes/lib/constants.js\");\n\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n\n\n\n/**\r\n * Performs the re-initialization of widgets.\r\n *\r\n * @return {null}\r\n */\nfunction WidgetHandler() {\n  return null;\n}\n\n/**\r\n * Returns whether the widget is created by Carbon Fields.\r\n *\r\n * @param  {string} identifier\r\n * @return {boolean}\r\n */\nfunction isCarbonFieldsWidget(identifier) {\n  return identifier.indexOf(_lib_constants__WEBPACK_IMPORTED_MODULE_10__[\"CARBON_FIELDS_CONTAINER_WIDGET_ID_PREFIX\"]) > -1;\n}\n\n/**\r\n * The function that controls the stream of side effects.\r\n *\r\n * @return {Object}\r\n */\nfunction aperture() {\n  return Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"merge\"])(Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"pipe\"])(Object(_utils_from_event_pattern__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function (handler) {\n    return window.jQuery(document).on('widget-added widget-updated', handler);\n  }, function (handler) {\n    return window.jQuery(document).off('widget-added widget-updated', handler);\n  }, function (event, $widget) {\n    return {\n      event: event,\n      $widget: $widget\n    };\n  }), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"filter\"])(function (_ref) {\n    var $widget = _ref.$widget;\n    return isCarbonFieldsWidget($widget[0].id);\n  }), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"map\"])(function (payload) {\n    return {\n      type: 'WIDGET_CREATED_OR_UPDATED',\n      payload: payload\n    };\n  })), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"pipe\"])(Object(_utils_from_event_pattern__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function (handler) {\n    return window.jQuery(document).on('ajaxSend', handler);\n  }, function (handler) {\n    return window.jQuery(document).off('ajaxSend', handler);\n  }, function (event, xhr, options, data) {\n    return {\n      event: event,\n      xhr: xhr,\n      options: options,\n      data: data\n    };\n  }), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"filter\"])(function (_ref2) {\n    var options = _ref2.options;\n    return Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"startsWith\"])(options.data, _lib_constants__WEBPACK_IMPORTED_MODULE_10__[\"CARBON_FIELDS_CONTAINER_ID_PREFIX\"]);\n  }), Object(callbag_basics__WEBPACK_IMPORTED_MODULE_5__[\"map\"])(function (payload) {\n    return {\n      type: 'WIDGET_BEIGN_UPDATED_OR_DELETED',\n      payload: payload\n    };\n  })));\n}\n\n/**\r\n * The function that causes the side effects.\r\n *\r\n * @return {Function}\r\n */\nfunction handler() {\n  return function (effect) {\n    var _select = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__[\"select\"])('carbon-fields/metaboxes'),\n      getContainerById = _select.getContainerById;\n    var _dispatch = Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__[\"dispatch\"])('carbon-fields/metaboxes'),\n      addContainer = _dispatch.addContainer,\n      removeContainer = _dispatch.removeContainer,\n      addFields = _dispatch.addFields,\n      removeFields = _dispatch.removeFields;\n    switch (effect.type) {\n      case 'WIDGET_CREATED_OR_UPDATED':\n        {\n          var _effect$payload = effect.payload,\n            event = _effect$payload.event,\n            $widget = _effect$payload.$widget;\n          var container = Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"flow\"])(_utils_urldecode__WEBPACK_IMPORTED_MODULE_6__[\"default\"], JSON.parse)($widget.find('[data-json]').data('json'));\n          var fields = [];\n          container.fields = container.fields.map(function (field) {\n            return Object(_utils_flatten_field__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(field, container, fields);\n          });\n          addFields(fields);\n          addContainer(container);\n          Object(_containers__WEBPACK_IMPORTED_MODULE_9__[\"renderContainer\"])(container, 'classic');\n\n          // WARNING: This piece of code manipulates the core behavior of WordPress Widgets.\n          // Some day this code will stop to work and we'll need to find another workaround.\n          //\n          // * Disable the submit { handler } since it breaks our validation logic.\n          // * Disable live preview mode because we can't detect when the widget is updated/synced.\n          // * Show the \"Apply\" button because it's hidden by the live mode.\n          if (window.cf.config.pagenow === _lib_constants__WEBPACK_IMPORTED_MODULE_10__[\"PAGE_NOW_CUSTOMIZE\"] && event.type === 'widget-added') {\n            var widgetId = $widget.find('[name=\"widget-id\"]').val();\n            $widget.find('[name=\"savewidget\"]').show().end().find('.widget-content:first').off('keydown', 'input').off('change input propertychange', ':input');\n            var instance = wp.customize.Widgets.getWidgetFormControlForWidget(widgetId);\n\n            // Change the flag for 'live mode' so we can receive proper `widget-updated` events.\n            instance.liveUpdateMode = false;\n          }\n          break;\n        }\n      case 'WIDGET_BEIGN_UPDATED_OR_DELETED':\n        {\n          var _effect$payload$optio = effect.payload.options.data.match(/widget-id=(.+?)&/),\n            _effect$payload$optio2 = _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0___default()(_effect$payload$optio, 2),\n            _widgetId = _effect$payload$optio2[1];\n          var containerId = \"\".concat(_lib_constants__WEBPACK_IMPORTED_MODULE_10__[\"CARBON_FIELDS_CONTAINER_ID_PREFIX\"]).concat(_widgetId);\n\n          // Get the container from the store.\n          var _container = getContainerById(containerId);\n\n          // Remove the current instance from DOM.\n          Object(_wordpress_element__WEBPACK_IMPORTED_MODULE_2__[\"unmountComponentAtNode\"])(document.querySelector(\".container-\".concat(containerId)));\n\n          // Get the fields that belongs to the container.\n          var fieldsIds = _.map(_container.fields, 'id');\n\n          // Remove everything from the store.\n          removeContainer(containerId);\n          removeFields(fieldsIds);\n          break;\n        }\n    }\n  };\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (Object(refract_callbag__WEBPACK_IMPORTED_MODULE_4__[\"withEffects\"])(aperture, {\n  handler: handler\n})(WidgetHandler));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/monitors/widget-handler/index.js?");

/***/ }),

/***/ "./packages/metaboxes/store/actions.js":
/*!*********************************************!*\
  !*** ./packages/metaboxes/store/actions.js ***!
  \*********************************************/
/*! exports provided: setupState, updateState, updateFieldValue, addFields, cloneFields, removeFields, addContainer, removeContainer, receiveSidebar, lockSaving, unlockSaving */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"setupState\", function() { return setupState; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"updateState\", function() { return updateState; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"updateFieldValue\", function() { return updateFieldValue; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"addFields\", function() { return addFields; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"cloneFields\", function() { return cloneFields; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"removeFields\", function() { return removeFields; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"addContainer\", function() { return addContainer; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"removeContainer\", function() { return removeContainer; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"receiveSidebar\", function() { return receiveSidebar; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"lockSaving\", function() { return lockSaving; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"unlockSaving\", function() { return unlockSaving; });\n/**\r\n * Returns an action object used to setup the state when first opening an editor.\r\n *\r\n * @param  {Object[]} containers\r\n * @param  {Object}   fields\r\n * @return {Object}\r\n */\nfunction setupState(containers, fields) {\n  return {\n    type: 'SETUP_STATE',\n    payload: {\n      containers: containers,\n      fields: fields\n    }\n  };\n}\n\n/**\r\n * Returns an action object used to update the state.\r\n *\r\n * @param  {Object[]} containers\r\n * @param  {Object}   fields\r\n * @return {Object}\r\n */\nfunction updateState(containers, fields) {\n  return {\n    type: 'UPDATE_STATE',\n    payload: {\n      containers: containers,\n      fields: fields\n    }\n  };\n}\n\n/**\r\n * Returns an action object used to update the field's value.\r\n *\r\n * @param  {string}   fieldId\r\n * @param  {mixed}    value\r\n * @param  {string[]} fieldsToRemove It's used by the complex fields to remove the nested\r\n *                                 fields within a single action.\r\n * @return {Object}\r\n */\nfunction updateFieldValue(fieldId, value) {\n  var fieldsToRemove = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  return {\n    type: 'UPDATE_FIELD_VALUE',\n    payload: {\n      fieldId: fieldId,\n      value: value,\n      fieldsToRemove: fieldsToRemove\n    }\n  };\n}\n\n/**\r\n * Returns an action object used to add the fields.\r\n *\r\n * @param  {Object[]} fields\r\n * @return {Object}\r\n */\nfunction addFields(fields) {\n  return {\n    type: 'ADD_FIELDS',\n    payload: {\n      fields: fields\n    }\n  };\n}\n\n/**\r\n * Returns an action object used to clone the fields.\r\n *\r\n * @param  {string[]} originFieldIds\r\n * @param  {string[]} cloneFieldIds\r\n * @return {Object}\r\n */\nfunction cloneFields(originFieldIds, cloneFieldIds) {\n  return {\n    type: 'CLONE_FIELDS',\n    payload: {\n      originFieldIds: originFieldIds,\n      cloneFieldIds: cloneFieldIds\n    }\n  };\n}\n\n/**\r\n * Returns an action object used to remove the fields.\r\n *\r\n * @param  {string[]} fieldIds\r\n * @return {Object}\r\n */\nfunction removeFields(fieldIds) {\n  return {\n    type: 'REMOVE_FIELDS',\n    payload: {\n      fieldIds: fieldIds\n    }\n  };\n}\n\n/**\r\n * Returns an action object used to add a container to all containers.\r\n *\r\n * @param  {Object} container\r\n * @return {Object}\r\n */\nfunction addContainer(container) {\n  return {\n    type: 'ADD_CONTAINER',\n    payload: container\n  };\n}\n\n/**\r\n * Returns an action object used to remove a container from all containers.\r\n *\r\n * @param  {Object} container\r\n * @return {Object}\r\n */\nfunction removeContainer(container) {\n  return {\n    type: 'REMOVE_CONTAINER',\n    payload: container\n  };\n}\n\n/**\r\n * Returns an action object used to add the created sidebar to all fields.\r\n *\r\n * @param  {Object} sidebar\r\n * @return {Object}\r\n */\nfunction receiveSidebar(sidebar) {\n  return {\n    type: 'RECEIVE_SIDEBAR',\n    payload: sidebar\n  };\n}\n\n/**\r\n * Returns an action object used to signal that saving is locked.\r\n *\r\n * @param  {string} lockName\r\n * @return {Object}\r\n */\nfunction lockSaving(lockName) {\n  return {\n    type: 'LOCK_SAVING',\n    payload: {\n      lockName: lockName\n    }\n  };\n}\n\n/**\r\n * Returns an action object used to signal that saving is unlocked.\r\n *\r\n * @param  {string} lockName\r\n * @return {Object}\r\n */\nfunction unlockSaving(lockName) {\n  return {\n    type: 'UNLOCK_SAVING',\n    payload: {\n      lockName: lockName\n    }\n  };\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/store/actions.js?");

/***/ }),

/***/ "./packages/metaboxes/store/helpers.js":
/*!*********************************************!*\
  !*** ./packages/metaboxes/store/helpers.js ***!
  \*********************************************/
/*! exports provided: normalizePreloadedState */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"normalizePreloadedState\", function() { return normalizePreloadedState; });\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_flatten_field__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/flatten-field */ \"./packages/metaboxes/utils/flatten-field.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n/**\r\n * Transform the shape of the given state to be more Redux friendly.\r\n *\r\n * @param  {Object} state\r\n * @return {Object}\r\n */\nfunction normalizePreloadedState(state) {\n  var fields = [];\n  var containers = state.filter(function (_ref) {\n    var id = _ref.id;\n    return !Object(lodash__WEBPACK_IMPORTED_MODULE_0__[\"endsWith\"])(id, '__i__');\n  }).map(function (container) {\n    return Object(lodash__WEBPACK_IMPORTED_MODULE_0__[\"assign\"])({}, container, {\n      fields: container.fields.map(function (field) {\n        return Object(_utils_flatten_field__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(field, container.id, fields);\n      })\n    });\n  });\n  return {\n    containers: containers,\n    fields: fields\n  };\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/store/helpers.js?");

/***/ }),

/***/ "./packages/metaboxes/store/index.js":
/*!*******************************************!*\
  !*** ./packages/metaboxes/store/index.js ***!
  \*******************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _reducer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./reducer */ \"./packages/metaboxes/store/reducer.js\");\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./actions */ \"./packages/metaboxes/store/actions.js\");\n/* harmony import */ var _selectors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./selectors */ \"./packages/metaboxes/store/selectors.js\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./helpers */ \"./packages/metaboxes/store/helpers.js\");\n/**\r\n * External dependencies.\r\n */\n\n\n\n/**\r\n * Internal dependencies.\r\n */\n\n\n\n\n\n/**\r\n * Register the store.\r\n */\nObject(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__[\"registerStore\"])('carbon-fields/metaboxes', {\n  reducer: _reducer__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n  actions: _actions__WEBPACK_IMPORTED_MODULE_3__,\n  selectors: _selectors__WEBPACK_IMPORTED_MODULE_4__\n});\n\n/**\r\n * Hydrate the store's state.\r\n */\nvar _normalizePreloadedSt = Object(_helpers__WEBPACK_IMPORTED_MODULE_5__[\"normalizePreloadedState\"])(Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"get\"])(window.cf, 'preloaded.containers', [])),\n  containers = _normalizePreloadedSt.containers,\n  fields = _normalizePreloadedSt.fields;\nObject(_wordpress_data__WEBPACK_IMPORTED_MODULE_0__[\"dispatch\"])('carbon-fields/metaboxes').setupState(Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"keyBy\"])(containers, 'id'), Object(lodash__WEBPACK_IMPORTED_MODULE_1__[\"keyBy\"])(fields, 'id'));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/store/index.js?");

/***/ }),

/***/ "./packages/metaboxes/store/reducer.js":
/*!*********************************************!*\
  !*** ./packages/metaboxes/store/reducer.js ***!
  \*********************************************/
/*! exports provided: containers, fields, savingLock, isDirty, isFieldUpdated, default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"containers\", function() { return containers; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"fields\", function() { return fields; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"savingLock\", function() { return savingLock; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"isDirty\", function() { return isDirty; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"isFieldUpdated\", function() { return isFieldUpdated; });\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! immer */ \"immer\");\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(immer__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/data */ \"@wordpress/data\");\n/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_4__);\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n/**\r\n * External dependencies.\r\n */\n\n\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * The reducer that keeps track of the containers.\r\n *\r\n * @param  {Object} state\r\n * @param  {Object} action\r\n * @return {Object}\r\n */\nfunction containers() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  switch (action.type) {\n    case 'SETUP_STATE':\n      return action.payload.containers;\n    case 'UPDATE_STATE':\n      return immer__WEBPACK_IMPORTED_MODULE_1___default()(state, function (draft) {\n        Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"values\"])(action.payload.containers).forEach(function (container) {\n          draft[container.id] = container;\n        });\n      });\n    case 'ADD_CONTAINER':\n      return immer__WEBPACK_IMPORTED_MODULE_1___default()(state, function (draft) {\n        draft[action.payload.id] = action.payload;\n      });\n    case 'REMOVE_CONTAINER':\n      return Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"omit\"])(state, action.payload);\n    default:\n      return state;\n  }\n}\n\n/**\r\n * Clones a field.\r\n *\r\n * @param  {string}   originId\r\n * @param  {string}   cloneId\r\n * @param  {Object}   fields\r\n * @param  {Object[]} accumulator\r\n * @return {Object[]}\r\n */\nfunction cloneField(originId, cloneId, fields, accumulator) {\n  var field = Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"cloneDeep\"])(fields[originId]);\n  field.id = cloneId;\n  if (field.type === 'complex') {\n    field.value.forEach(function (group) {\n      group.id = Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_4__[\"uniqueId\"])();\n      accumulator = group.fields.reduce(function (groupAccumulator, groupField) {\n        var originGroupFieldId = groupField.id;\n        var cloneGroupFieldId = Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_4__[\"uniqueId\"])();\n        groupField.id = cloneGroupFieldId;\n        return cloneField(originGroupFieldId, cloneGroupFieldId, fields, groupAccumulator);\n      }, accumulator);\n    });\n  }\n  return accumulator.concat(field);\n}\n\n/**\r\n * Returns a list of field ids by a given root id.\r\n *\r\n * @param  {string}   fieldId\r\n * @param  {Object}   fields\r\n * @param  {string[]} accumulator\r\n * @return {string[]}\r\n */\nfunction getFieldIdsByRootId(fieldId, fields, accumulator) {\n  var field = fields[fieldId];\n  if (field.type === 'complex') {\n    field.value.forEach(function (group) {\n      accumulator = group.fields.reduce(function (groupAccumulator, groupField) {\n        return getFieldIdsByRootId(groupField.id, fields, groupAccumulator);\n      }, accumulator);\n    });\n  }\n  return accumulator.concat(fieldId);\n}\n\n/**\r\n * The reducer that keeps track of the fields.\r\n *\r\n * @param  {Object} state\r\n * @param  {Object} action\r\n * @return {Object}\r\n */\nfunction fields() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  switch (action.type) {\n    case 'SETUP_STATE':\n      return action.payload.fields;\n    case 'UPDATE_STATE':\n      return immer__WEBPACK_IMPORTED_MODULE_1___default()(state, function (draft) {\n        Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"values\"])(action.payload.fields).forEach(function (field) {\n          draft[field.id] = field;\n        });\n      });\n    case 'UPDATE_FIELD_VALUE':\n      return immer__WEBPACK_IMPORTED_MODULE_1___default()(state, function (draft) {\n        var _action$payload = action.payload,\n          fieldId = _action$payload.fieldId,\n          value = _action$payload.value,\n          fieldsToRemove = _action$payload.fieldsToRemove;\n        draft[fieldId].value = value;\n        var fieldIdsToRemove = fieldsToRemove.reduce(function (accumulator, fieldIdToRemove) {\n          return getFieldIdsByRootId(fieldIdToRemove, state, accumulator);\n        }, []);\n        fieldIdsToRemove.forEach(function (fieldIdToRemove) {\n          Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"unset\"])(draft, fieldIdToRemove);\n        });\n      });\n    case 'ADD_FIELDS':\n      return immer__WEBPACK_IMPORTED_MODULE_1___default()(state, function (draft) {\n        action.payload.fields.forEach(function (field) {\n          draft[field.id] = field;\n        });\n      });\n    case 'CLONE_FIELDS':\n      return immer__WEBPACK_IMPORTED_MODULE_1___default()(state, function (draft) {\n        var _action$payload2 = action.payload,\n          originFieldIds = _action$payload2.originFieldIds,\n          cloneFieldIds = _action$payload2.cloneFieldIds;\n        var clonedFields = originFieldIds.reduce(function (accumulator, originFieldId, index) {\n          return cloneField(originFieldId, cloneFieldIds[index], draft, accumulator);\n        }, []);\n        Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"assign\"])(draft, Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"keyBy\"])(clonedFields, 'id'));\n      });\n    case 'REMOVE_FIELDS':\n      var fieldIds = action.payload.fieldIds.reduce(function (accumulator, fieldId) {\n        return getFieldIdsByRootId(fieldId, state, accumulator);\n      }, []);\n      return Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"omit\"])(state, fieldIds);\n    case 'RECEIVE_SIDEBAR':\n      return immer__WEBPACK_IMPORTED_MODULE_1___default()(state, function (draft) {\n        Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"forEach\"])(draft, function (field) {\n          if (field.type === 'sidebar') {\n            field.options.unshift(action.payload);\n          }\n        });\n      });\n    default:\n      return state;\n  }\n}\n\n/**\r\n * The reducer that keeps track of the save locks.\r\n *\r\n * @param  {Object} state\r\n * @param  {Object} action\r\n * @return {Object}\r\n */\nfunction savingLock() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  switch (action.type) {\n    case 'LOCK_SAVING':\n      return _objectSpread(_objectSpread({}, state), {}, _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()({}, action.payload.lockName, true));\n    case 'UNLOCK_SAVING':\n      return Object(lodash__WEBPACK_IMPORTED_MODULE_3__[\"omit\"])(state, [action.payload.lockName]);\n    default:\n      return state;\n  }\n}\n\n/**\r\n * The reducer that keeps track if there is dirty fields.\r\n *\r\n * @param  {boolean} state\r\n * @param  {Object}  action\r\n * @return {Object}\r\n */\nfunction isDirty() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  switch (action.type) {\n    case 'UPDATE_FIELD_VALUE':\n      return true;\n    default:\n      return state;\n  }\n}\n\n/**\r\n * The reducer that keeps track if an update is being made.\r\n *\r\n * @param  {boolean} state\r\n * @param  {Object}  action\r\n * @return {Object}\r\n */\nfunction isFieldUpdated(state, action) {\n  switch (action.type) {\n    case 'UPDATE_FIELD_VALUE':\n      return {\n        action: action\n      };\n    default:\n      return false;\n  }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (Object(_wordpress_data__WEBPACK_IMPORTED_MODULE_2__[\"combineReducers\"])({\n  containers: containers,\n  fields: fields,\n  savingLock: savingLock,\n  isDirty: isDirty,\n  isFieldUpdated: isFieldUpdated\n}));\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/store/reducer.js?");

/***/ }),

/***/ "./packages/metaboxes/store/selectors.js":
/*!***********************************************!*\
  !*** ./packages/metaboxes/store/selectors.js ***!
  \***********************************************/
/*! exports provided: getContainers, getContainerById, getFields, getFieldsByContainerId, getFieldById, isSavingLocked, isDirty, isFieldUpdated, getComplexGroupValues */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"getContainers\", function() { return getContainers; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"getContainerById\", function() { return getContainerById; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"getFields\", function() { return getFields; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"getFieldsByContainerId\", function() { return getFieldsByContainerId; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"getFieldById\", function() { return getFieldById; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"isSavingLocked\", function() { return isSavingLocked; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"isDirty\", function() { return isDirty; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"isFieldUpdated\", function() { return isFieldUpdated; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"getComplexGroupValues\", function() { return getComplexGroupValues; });\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Returns the containers.\r\n *\r\n * @param  {Object} state\r\n * @return {Object[]}\r\n */\nfunction getContainers(state) {\n  return state.containers;\n}\n\n/**\r\n * Returns a container by an id.\r\n *\r\n * @param  {Object} state\r\n * @param  {string} containerId\r\n * @return {?Object}\r\n */\nfunction getContainerById(state, containerId) {\n  return state.containers[containerId];\n}\n\n/**\r\n * Returns the fields.\r\n *\r\n * @param  {Object} state\r\n * @return {Object}\r\n */\nfunction getFields(state) {\n  return state.fields;\n}\n\n/**\r\n * Returns the fields that belong to the specified container.\r\n *\r\n * @param  {Object} state\r\n * @param  {string} containerId\r\n * @return {Object[]}\r\n */\nfunction getFieldsByContainerId(state, containerId) {\n  return Object(lodash__WEBPACK_IMPORTED_MODULE_0__[\"filter\"])(state.fields, ['container_id', containerId]);\n}\n\n/**\r\n * Returns a field by an id.\r\n *\r\n * @param  {Object} state\r\n * @param  {string} fieldId\r\n * @return {?Object}\r\n */\nfunction getFieldById(state, fieldId) {\n  return state.fields[fieldId];\n}\n\n/**\r\n * Returns whether saving is locked.\r\n *\r\n * @param  {Object} state\r\n * @return {boolean}\r\n */\nfunction isSavingLocked(state) {\n  return Object.keys(state.savingLock).length > 0;\n}\n\n/**\r\n * Returns whether the metaboxes fields contain unsaved changed.\r\n *\r\n * @param  {Object} state\r\n * @return {boolean}\r\n */\nfunction isDirty(state) {\n  return state.isDirty;\n}\n\n/**\r\n * Returns whether the metaboxes fields contain unsaved changed.\r\n *\r\n * @param  {Object} state\r\n * @return {boolean}\r\n */\nfunction isFieldUpdated(state) {\n  return state.isFieldUpdated;\n}\n\n/**\r\n * Returns a map of field values for a given group.\r\n *\r\n * @param  {Object}   state\r\n * @param  {string[]} fieldIds\r\n * @return {Object}\r\n */\nfunction getComplexGroupValues(state, fieldIds) {\n  var fields = Object(lodash__WEBPACK_IMPORTED_MODULE_0__[\"pick\"])(getFields(state), fieldIds);\n  fields = Object(lodash__WEBPACK_IMPORTED_MODULE_0__[\"mapKeys\"])(fields, function (field) {\n    return field.base_name.replace(/\\-/g, '_');\n  });\n  fields = Object(lodash__WEBPACK_IMPORTED_MODULE_0__[\"mapValues\"])(fields, function (field) {\n    return field.value;\n  });\n  return fields;\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/store/selectors.js?");

/***/ }),

/***/ "./packages/metaboxes/utils/flatten-field.js":
/*!***************************************************!*\
  !*** ./packages/metaboxes/utils/flatten-field.js ***!
  \***************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return flattenField; });\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @carbon-fields/core */ \"@carbon-fields/core\");\n/* harmony import */ var _carbon_fields_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_1__);\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Carbon Fields dependencies.\r\n */\n\n\n/**\r\n * Flattens a field.\r\n *\r\n * @param  {Object}   field\r\n * @param  {string}   containerId\r\n * @param  {Object[]} accumulator\r\n * @return {Object}\r\n */\nfunction flattenField(field, containerId, accumulator) {\n  field = Object(lodash__WEBPACK_IMPORTED_MODULE_0__[\"cloneDeep\"])(field);\n\n  // Replace the id of the field.\n  field.id = Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_1__[\"uniqueId\"])();\n\n  // Keep reference to the container.\n  field.container_id = containerId;\n\n  // The complex fields represent a nested structure of fields.\n  // So we need to flat them as well.\n  if (field.type === 'complex') {\n    field.value.forEach(function (group) {\n      group.id = Object(_carbon_fields_core__WEBPACK_IMPORTED_MODULE_1__[\"uniqueId\"])();\n      group.container_id = containerId;\n      group.fields = group.fields.map(function (groupField) {\n        return flattenField(groupField, containerId, accumulator);\n      });\n    });\n  }\n  accumulator.push(field);\n  return Object(lodash__WEBPACK_IMPORTED_MODULE_0__[\"pick\"])(field, ['id', 'type', 'name', 'base_name']);\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/utils/flatten-field.js?");

/***/ }),

/***/ "./packages/metaboxes/utils/from-event-pattern.js":
/*!********************************************************!*\
  !*** ./packages/metaboxes/utils/from-event-pattern.js ***!
  \********************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return fromEventPattern; });\n/* harmony import */ var callbag_create__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! callbag-create */ \"./node_modules/callbag-create/index.js\");\n/* harmony import */ var callbag_create__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(callbag_create__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Callbag source factory from `addHandler` and `removeHandler` pair.\r\n *\r\n * @see https://github.com/Andarist/callbag-from-event-pattern\r\n * @param  {Function} addHandler\r\n * @param  {Function} removeHandler\r\n * @param  {Function} argsTransformer\r\n * @return {Function}\r\n */\nfunction fromEventPattern(addHandler, removeHandler) {\n  var argsTransformer = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return args;\n  };\n  return callbag_create__WEBPACK_IMPORTED_MODULE_0___default()(function (sink) {\n    var handler = function handler() {\n      return sink(1, argsTransformer.apply(void 0, arguments));\n    };\n    addHandler(handler);\n    return function () {\n      return removeHandler(handler);\n    };\n  });\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/utils/from-event-pattern.js?");

/***/ }),

/***/ "./packages/metaboxes/utils/is-gutenberg.js":
/*!**************************************************!*\
  !*** ./packages/metaboxes/utils/is-gutenberg.js ***!
  \**************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return isGutenberg; });\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * External dependencies.\r\n */\n\n\n/**\r\n * Returns true if Gutenberg is presented.\r\n *\r\n * @return {boolean}\r\n */\nfunction isGutenberg() {\n  return !Object(lodash__WEBPACK_IMPORTED_MODULE_0__[\"isUndefined\"])(window._wpLoadBlockEditor);\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/utils/is-gutenberg.js?");

/***/ }),

/***/ "./packages/metaboxes/utils/strip-compact-input-prefix.js":
/*!****************************************************************!*\
  !*** ./packages/metaboxes/utils/strip-compact-input-prefix.js ***!
  \****************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return stripCompactInputPrefix; });\n/**\r\n * Removes the prefix used to compact the input of Carbon Fields.\r\n *\r\n * @param  {string} str\r\n * @return {string}\r\n */\nfunction stripCompactInputPrefix(str) {\n  var _window$cf$config = window.cf.config,\n    compactInput = _window$cf$config.compactInput,\n    compactInputKey = _window$cf$config.compactInputKey;\n  if (!compactInput || str.indexOf(compactInputKey) !== 0) {\n    return str;\n  }\n  return str.replace(new RegExp(\"^\".concat(compactInputKey, \"\\\\[(.+?)\\\\]\")), '$1');\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/utils/strip-compact-input-prefix.js?");

/***/ }),

/***/ "./packages/metaboxes/utils/urldecode.js":
/*!***********************************************!*\
  !*** ./packages/metaboxes/utils/urldecode.js ***!
  \***********************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return urldecode; });\n/**\r\n * Source: https://github.com/kvz/locutus/blob/master/src/php/url/urldecode.js\r\n *\r\n * @param  {string} str\r\n * @return {string}\r\n */\nfunction urldecode(str) {\n  return decodeURIComponent((str + '').replace(/%(?![\\da-f]{2})/gi, function () {\n    // PHP tolerates poorly formed escape sequences\n    return '%25';\n  }).replace(/\\+/g, '%20'));\n}\n\n//# sourceURL=webpack://cf.%5Bname%5D/./packages/metaboxes/utils/urldecode.js?");

/***/ }),

/***/ "@carbon-fields/core":
/*!******************************!*\
  !*** external ["cf","core"] ***!
  \******************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("(function() { module.exports = this[\"cf\"][\"core\"]; }());\n\n//# sourceURL=webpack://cf.%5Bname%5D/external_%5B%22cf%22,%22core%22%5D?");

/***/ }),

/***/ "@wordpress/compose":
/*!*****************************************************!*\
  !*** external ["cf","vendor","@wordpress/compose"] ***!
  \*****************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("(function() { module.exports = this[\"cf\"][\"vendor\"][\"@wordpress/compose\"]; }());\n\n//# sourceURL=webpack://cf.%5Bname%5D/external_%5B%22cf%22,%22vendor%22,%22@wordpress/compose%22%5D?");

/***/ }),

/***/ "@wordpress/data":
/*!**************************************************!*\
  !*** external ["cf","vendor","@wordpress/data"] ***!
  \**************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("(function() { module.exports = this[\"cf\"][\"vendor\"][\"@wordpress/data\"]; }());\n\n//# sourceURL=webpack://cf.%5Bname%5D/external_%5B%22cf%22,%22vendor%22,%22@wordpress/data%22%5D?");

/***/ }),

/***/ "@wordpress/element":
/*!*****************************************************!*\
  !*** external ["cf","vendor","@wordpress/element"] ***!
  \*****************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("(function() { module.exports = this[\"cf\"][\"vendor\"][\"@wordpress/element\"]; }());\n\n//# sourceURL=webpack://cf.%5Bname%5D/external_%5B%22cf%22,%22vendor%22,%22@wordpress/element%22%5D?");

/***/ }),

/***/ "@wordpress/hooks":
/*!***************************************************!*\
  !*** external ["cf","vendor","@wordpress/hooks"] ***!
  \***************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("(function() { module.exports = this[\"cf\"][\"vendor\"][\"@wordpress/hooks\"]; }());\n\n//# sourceURL=webpack://cf.%5Bname%5D/external_%5B%22cf%22,%22vendor%22,%22@wordpress/hooks%22%5D?");

/***/ }),

/***/ "@wordpress/i18n":
/*!**************************************************!*\
  !*** external ["cf","vendor","@wordpress/i18n"] ***!
  \**************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("(function() { module.exports = this[\"cf\"][\"vendor\"][\"@wordpress/i18n\"]; }());\n\n//# sourceURL=webpack://cf.%5Bname%5D/external_%5B%22cf%22,%22vendor%22,%22@wordpress/i18n%22%5D?");

/***/ }),

/***/ "callbag-basics":
/*!*************************************************!*\
  !*** external ["cf","vendor","callbag-basics"] ***!
  \*************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("(function() { module.exports = this[\"cf\"][\"vendor\"][\"callbag-basics\"]; }());\n\n//# sourceURL=webpack://cf.%5Bname%5D/external_%5B%22cf%22,%22vendor%22,%22callbag-basics%22%5D?");

/***/ }),

/***/ "classnames":
/*!*********************************************!*\
  !*** external ["cf","vendor","classnames"] ***!
  \*********************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("(function() { module.exports = this[\"cf\"][\"vendor\"][\"classnames\"]; }());\n\n//# sourceURL=webpack://cf.%5Bname%5D/external_%5B%22cf%22,%22vendor%22,%22classnames%22%5D?");

/***/ }),

/***/ "immer":
/*!****************************************!*\
  !*** external ["cf","vendor","immer"] ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("(function() { module.exports = this[\"cf\"][\"vendor\"][\"immer\"]; }());\n\n//# sourceURL=webpack://cf.%5Bname%5D/external_%5B%22cf%22,%22vendor%22,%22immer%22%5D?");

/***/ }),

/***/ "lodash":
/*!*****************************************!*\
  !*** external ["cf","vendor","lodash"] ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("(function() { module.exports = this[\"cf\"][\"vendor\"][\"lodash\"]; }());\n\n//# sourceURL=webpack://cf.%5Bname%5D/external_%5B%22cf%22,%22vendor%22,%22lodash%22%5D?");

/***/ }),

/***/ "refract-callbag":
/*!**************************************************!*\
  !*** external ["cf","vendor","refract-callbag"] ***!
  \**************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("(function() { module.exports = this[\"cf\"][\"vendor\"][\"refract-callbag\"]; }());\n\n//# sourceURL=webpack://cf.%5Bname%5D/external_%5B%22cf%22,%22vendor%22,%22refract-callbag%22%5D?");

/***/ })

/******/ });