<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_testimonial_2' );
function everest_testimonial_2() {
Block::make( __( 'Xbees Testimonial 2' ) )
	->add_fields( array(
        Field::make( 'text', 'title', __( 'Title' ) ),
        Field::make( 'text', 'subtext', __( 'Subtitle' ) ),
        Field::make( 'image', 'image', __( 'Testimonial Image' ) )
        ->set_value_type( 'url' ),
        ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $title = !empty($fields['title']) ? $fields['title'] : '<span>Review from</span> Elearing Clients';
        $subtext = !empty($fields['subtext']) ? $fields['subtext'] : 'Testimonials';
        // WP_Query arguments
        $args = array(
            'post_type' => 'everest-testimonial',
            'posts_per_page' => 6, // -1 to retrieve all posts, you can adjust this number as needed
            'post_status' => 'publish', // Retrieve only published posts
            // You can add more parameters as needed, like 'orderby', 'order', 'category', 'tag', etc.
        );
        // The Query
        $query = new WP_Query( $args );
      ?>
<!--Start Testimonial Style1 Area-->
<section class="testimonial-style3-area">
    <div class="container">
        <div class="row">
            <div class="col-xl-6">
                <div class="testimonial-style3__img-box">
                    <div class="inner">
                        <img src="<?php echo $fields['image'] ?>" alt="">
                    </div>
                </div>
            </div>
            <div class="col-xl-6">
                <div class="testimonial-style3__text-box">
                    <div class="sec-title-style3">
                        <div class="sub-title">
                            <h5><?php echo $subtext; ?></h5>
                        </div>
                        <h2><?php echo $title; ?></h2>
                    </div>
                    <div class="testimonial-style3__text-box__inner">
                        <div class="testimonial-style3__text-box__inner-bg"></div>
                        <div class="testimonial-style3-carousel owl-theme owl-carousel owl-nav-style-one">
                            <?php 
                      // The Loop
                        if ( $query->have_posts() ) {
                            while ( $query->have_posts() ) {
                                $query->the_post(); 
                                $client_name = carbon_get_post_meta(get_the_ID(), 'client-name');
                                
                    ?>
                            <!--Start Single Testimonial Style3-->
                            <div class="single-testimonial-style3">
                                <div class="top">
                                    <div class="left">
                                        <div class="quote-icon">
                                            <span class="icon-quote"></span>
                                        </div>
                                        <div class="name">
                                            <h3><?php the_title(); ?></h3>
                                        </div>
                                    </div>
                                    <div class="right">
                                        <div class="social-links">
                                            <ul>
                                                <li>
                                                    <a href="#">
                                                        <span class="icon-user"></span>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="inner-text">
                                    <p><?php the_content(); ?></p>
                                </div>
                                <div class="bottom">
                                    <div class="left">
                                        <div class="review-box">
                                            <ul>
                                                <li><i class="fa fa-star"></i></li>
                                                <li><i class="fa fa-star"></i></li>
                                                <li><i class="fa fa-star"></i></li>
                                                <li><i class="fa fa-star"></i></li>
                                                <li><i class="fa fa-star"></i></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="right">
                                        <div class="date-box">
                                            <p><?php the_date(); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--End Single Testimonial Style3-->

                            <?php } ?>
                            <?php
                    } else {
                        // no posts found
                        echo 'No testimonial found.';
                    }
                    
                    // Restore original post data
                    wp_reset_postdata(); 
                    ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</section>
<!--End Testimonial Style1 Area-->
<?php
	} );
}