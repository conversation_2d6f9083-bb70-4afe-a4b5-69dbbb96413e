msgid ""
msgstr ""
"Content-Type: text/plain; charset=UTF-8\n"
"X-Generator: Poedit 2.4.1\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Transfer-Encoding: 8bit\n"
"Last-Translator: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Language: en\n"

#: packages/blocks/components/block-edit/index.js:152
msgid "Show preview"
msgstr "Show preview"

#: packages/blocks/components/block-edit/index.js:153
msgid "Hide preview"
msgstr "Hide preview"

#: packages/blocks/components/block-edit/index.js:169
msgid "Fields"
msgstr "Fields"

#: packages/blocks/components/not-supported-field/index.js:14
msgid "Field of type '%s' is not supported in Gutenberg."
msgstr "Field of type ‘%s’ is not supported in Gutenberg."

#: packages/blocks/fields/datetime/index.js:14
#: packages/core/fields/date/index.js:15
#: packages/metaboxes/fields/datetime/index.js:9
msgid "Select Date"
msgstr "Select Date"

#: packages/blocks/fields/file/index.js:16
#: packages/metaboxes/fields/file/index.js:11
msgid "Use File"
msgstr "Use File"

#: packages/blocks/fields/file/index.js:17
#: packages/metaboxes/fields/file/index.js:12
msgid "Select File"
msgstr "Select File"

#: packages/blocks/fields/image/index.js:11
#: packages/metaboxes/fields/image/index.js:11
msgid "Use Image"
msgstr "Use Image"

#: packages/blocks/fields/image/index.js:12
#: packages/metaboxes/fields/image/index.js:12
msgid "Select Image"
msgstr "Select Image"

#: packages/blocks/fields/media-gallery/index.js:16
#: packages/metaboxes/fields/media-gallery/index.js:16
msgid "Use Attachments"
msgstr "Use Attachments"

#: packages/blocks/fields/media-gallery/index.js:17
#: packages/metaboxes/fields/media-gallery/index.js:17
msgid "Select Attachments"
msgstr "Select Attachments"

#: packages/core/components/no-options/index.js:14
msgid "No options."
msgstr "No options."

#: packages/core/components/search-input/index.js:30
msgid "Search..."
msgstr "Search…"

#: packages/core/fields/association/index.js:113
msgid "Maximum number of items reached (%s items)"
msgstr "Maximum number of items reached (%s items)"

#: packages/core/fields/association/index.js:204
msgid "Showing %1$d of %2$d results"
msgstr "Showing %1$d of %2$d results"

#: packages/core/fields/association/index.js:380
msgid "An error occurred while trying to fetch association options."
msgstr "An error occurred while trying to fetch association options."

#: packages/core/fields/association/index.js:430
#: packages/core/fields/complex/index.js:394
#: packages/core/hocs/with-validation/required.js:20
msgid "This field is required."
msgstr "This field is required."

#: packages/core/fields/association/index.js:434
msgid "Minimum number of items not reached (%s items)"
msgstr "Minimum number of items not reached (%s items)"

#: packages/core/fields/color/index.js:86
msgid "Select a color"
msgstr "Select a color"

#: packages/core/fields/complex/group.js:150
msgid "Duplicate"
msgstr "Duplicate"

#: packages/core/fields/complex/group.js:159
msgid "Remove"
msgstr "Remove"

#: packages/core/fields/complex/group.js:168
msgid "Collapse"
msgstr "Collapse"

#: packages/core/fields/complex/index.js:145
msgid "Couldn't create the label of group - %s"
msgstr "Couldn’t create the label of group - %s"

#: packages/core/fields/complex/index.js:367
msgid "Expand All"
msgstr "Expand All"

#: packages/core/fields/complex/index.js:367
msgid "Collapse All"
msgstr "Collapse All"

#: packages/core/fields/complex/index.js:401
msgid "Minimum number of rows not reached (%1$d %2$s)"
msgstr "Minimum number of rows not reached (%1$d %2$s)"

#: packages/core/fields/complex/index.js:81
msgid "Add %s"
msgstr "Add %s"

#: packages/core/fields/map/index.js:165
msgid "The address could not be found."
msgstr "The address could not be found."

#: packages/core/fields/map/index.js:167
msgid "Geocode was not successful for the following reason: "
msgstr "Geocode was not successful for the following reason: "

#: packages/core/fields/map/index.js:185
msgid "Error alert"
msgstr ""

#: packages/core/fields/oembed/index.js:188
msgid "An error occurred while trying to fetch oembed preview."
msgstr "An error occurred while trying to fetch oembed preview."

#: packages/core/fields/oembed/index.js:203
msgid "Not Found"
msgstr "Not Found"

#: packages/core/fields/rich-text/index.js:100
msgid "Text"
msgstr "Text"

#: packages/core/fields/rich-text/index.js:96
msgid "Visual"
msgstr "Visual"

#: packages/core/fields/sidebar/index.js:110
msgid "Please enter the name of the new sidebar:"
msgstr "Please enter the name of the new sidebar:"

#: packages/core/fields/sidebar/index.js:128
msgid "An error occurred while trying to create the sidebar."
msgstr "An error occurred while trying to create the sidebar."

#: packages/core/fields/sidebar/index.js:58
msgid "Please choose"
msgstr "Please choose"

#: packages/core/fields/time/index.js:16
msgid "Select Time"
msgstr "Select Time"

#: packages/core/hocs/with-conditional-logic/index.js:62
msgid "An unknown field is used in condition - \"%s\""
msgstr ""

#: packages/core/registry/index.js:40
msgid "%1$s type must be a string."
msgstr ""

#: packages/core/registry/index.js:46
msgid "%1$s %2$s is already registered."
msgstr ""

#: packages/core/registry/index.js:54
msgid "The \"component\" param must be a function."
msgstr ""

#: packages/core/registry/index.js:79
msgid "The provided context isn't a valid one. Must be one of - %s ."
msgstr ""

#: packages/core/registry/index.js:89
msgid "%s %s isn't registered."
msgstr "%s %s isn’t registered."

#: packages/core/utils/api-fetch.js:19
msgid "An error occured."
msgstr "An error occured."

#: packages/core/utils/fetch-attachments-data.js:23
msgid "An error occurred while trying to fetch files data."
msgstr "An error occurred while trying to fetch files data."

#: packages/metaboxes/containers/index.js:52
msgid "Could not find DOM element for container \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/conditions/base.js:52
msgid "Unsupported container condition comparison operator used - \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/handler/index.js:55
msgid "Unsupported container condition - \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/handler/index.js:73
msgid "Unsupported container condition relation used - \"%1$s\"."
msgstr ""
