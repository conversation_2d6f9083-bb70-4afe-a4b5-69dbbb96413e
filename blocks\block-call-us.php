<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_call_us' );
function everest_block_call_us() {
Block::make( __( 'Xbees Call Us' ) )
	->add_fields( array(
        Field::make( 'text', 'title', __( 'Title' ) ),
        Field::make( 'text', 'subtext', __( 'Subtitle' ) ),
        Field::make( 'image', 'image', __( 'Background Image' ) )
        ->set_value_type( 'url' ),
        Field::make( 'text', 'btntext', __( 'Button Text' ) ),
        Field::make( 'text', 'btnurl', __( 'Button Url' ) ),

    ) )

    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $title = !empty($fields['title']) ? $fields['title'] : 'Don\'t hesitate to <br>call us.';
        $subtext = !empty($fields['subtext']) ? $fields['subtext'] : 'It\'s always better to know all your options; we assist you in making the best decision possible.';
        $image = !empty($fields['image']) ? $fields['image'] : 'https://st.ourhtmldemo.com/new/educamb/assets/images/parallax-background/virtual-store-area__bg.jpg';
        $btntext = !empty($fields['btntext']) ? $fields['btntext'] : 'Get a Free 30-min call';
        $btnurl = !empty($fields['btnurl']) ? $fields['btnurl'] : '#';

      ?>
 <!--Start Virtual Store Area-->
 <section class="virtual-store-area">
            <div class="virtual-store-area__bg"
                style="background-image: url(<?php echo $image; ?>);"></div>
            <div class="container">
                <div class="row">
                    <div class="col-xl-12">
                        <div class="virtual-store__content wow fadeInLeft" data-wow-duration="1s" data-wow-delay="0.1s">
                            <div class="icon">
                                <span class="icon-phone"></span>
                            </div>
                            <div class="sec-title">
                                <h2><?php echo $title; ?></h2>
                                <div class="sub-title">
                                    <p><?php echo $subtext; ?> 
                                    </p>
                                </div>
                            </div>
                            <div class="virtual-store-form-box">
                                <div class="btns-box">
                                    <a class="btn-one" href="<?php echo $btnurl; ?>">
                                        <span class="txt"><?php echo $btntext; ?></span>
                                    </a>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--End Virtual Store Area-->
<?php
	} );
}