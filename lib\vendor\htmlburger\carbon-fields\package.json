{"name": "carbon-fields", "version": "3.6.3", "description": "WordPress developer-friendly custom fields for post types, taxonomy terms, users, comments, widgets, options, navigation menus and more.", "directories": {"test": "tests"}, "scripts": {"start": "npm run development -- --watch", "build": "npm run development && npm run production", "development": "cross-env NODE_ENV=development webpack --mode development", "production": "cross-env NODE_ENV=production webpack --mode production"}, "repository": {"type": "git", "url": "git+https://github.com/htmlburger/carbon-fields.git"}, "keywords": ["custom", "fields"], "author": "htmlburger", "license": "GPL-2.0", "bugs": {"url": "https://github.com/htmlburger/carbon-fields/issues"}, "homepage": "https://github.com/htmlburger/carbon-fields#readme", "devDependencies": {"@babel/core": "^7.15.0", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/plugin-proposal-object-rest-spread": "^7.14.7", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@wordpress/babel-plugin-makepot": "^4.2.0", "@wordpress/browserslist-config": "^4.1.0", "autoprefixer": "^9.3.1", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.2", "babel-plugin-module-resolver": "^4.1.0", "cross-env": "^5.2.0", "css-loader": "^1.0.0", "eslint": "^5.8.0", "eslint-config-wordpress": "^2.0.0", "eslint-plugin-react": "^7.11.1", "eslint-plugin-wordpress": "https://github.com/WordPress-Coding-Standards/eslint-plugin-wordpress#a650f73e2461ddae0faec900c0199b3c258cb5da", "husky": "^7.0.1", "lint-staged": "^8.1.0", "mini-css-extract-plugin": "^0.4.4", "optimize-css-assets-webpack-plugin": "^5.0.1", "postcss-loader": "^3.0.0", "postcss-preset-env": "^6.2.0", "sass": "^1.24.0", "sass-loader": "^7.1.0", "sass-resources-loader": "^2.0.0", "terser-webpack-plugin": "^1.1.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-merge": "^5.10.0"}, "dependencies": {"@babel/runtime": "^7.14.8", "@wordpress/api-fetch": "^6.23.1", "@wordpress/blocks": "^12.3.3", "@wordpress/components": "^23.3.6", "@wordpress/compose": "^6.3.3", "@wordpress/data": "^8.3.3", "@wordpress/date": "^4.26.2", "@wordpress/editor": "^13.3.9", "@wordpress/element": "^5.3.2", "@wordpress/hooks": "^3.26.1", "@wordpress/i18n": "^4.26.1", "callbag-basics": "^3.0.0", "callbag-create": "^1.1.1", "callbag-debounce": "^2.1.2", "callbag-distinct-until-changed": "^1.0.0", "callbag-drop-until": "^1.0.0", "callbag-empty": "^1.0.1", "callbag-from-delegated-event": "^2.0.0", "callbag-of": "^1.0.1", "callbag-start-with": "^3.1.0", "callbag-take-until": "^3.0.0", "classnames": "^2.2.6", "immer": "^9.0.6", "lodash": "^4.17.21", "nanoid": "^2.0.0", "react": "^18.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-flatpickr": "^3.6.4", "react-onclickoutside": "^6.11.2", "react-select": "^3.1.1", "refract-callbag": "^5.0.0-rc.0"}}