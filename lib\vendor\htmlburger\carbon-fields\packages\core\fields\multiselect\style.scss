/* ==========================================================================
   Multiselect
   ========================================================================== */

.cf-multiselect__control {
	min-height: 0;
	border-color: $wp-color-gray-light-500;

	&:hover {
		border-color: $wp-color-gray-light-500;
	}

	&--is-focused,
	&--is-focused:hover {
		border-color: $wp-color-medium-blue !important;
		box-shadow: none;
	}
}

.cf-multiselect__placeholder {
	color: $wp-color-gray-light-800;
}

.cf-multiselect__value-container {
	padding-left: $size-base;
	padding-right: $size-base;
}

.cf-multiselect__multi-value {
	align-items: center;
	padding: 5px 3px;
	margin: 0;
	background-color: $wp-color-gray-light-500;

	& + & {
		margin-left: 5px;
	}
}

.cf-multiselect__multi-value__label {
	padding-left: 3px;
	font-size: 13px;
	line-height: 1;
}

.cf-multiselect__multi-value__remove {
	padding: 0;
	margin-top: 1px;
	cursor: pointer;

	&:hover {
		background-color: transparent;
	}
}

.cf-multiselect__input {
	input[id],
	input[id]:focus {
		box-shadow: none;

	}
}

.cf-multiselect__menu {
	z-index: 9999;
}

.cf-multiselect__option {
	padding: $size-base;

	&--is-focused {
		background-color: $wp-color-medium-blue;
		color: $color-white;
	}
}

.cf-multiselect__indicator {
	padding: 5px;
	cursor: pointer;
}
