<?php
class footer_<PERSON>_Nav_Menu extends Walker_Nav_Menu {
    // Add classes to ul sub-menus
    function start_lvl( &$output, $depth = 0, $args = null ): void
    {
        // Depth-dependent classes
        $indent = ( $depth > 0  ? str_repeat( "\t", $depth ) : '' ); // code indent
        $display_depth = ( $depth + 1); // because it counts the first submenu as 0

        // Default class
        $classes = array( 'sub-menu' );

        // Adds the dropdown CSS class
        $class_names = join( ' ', apply_filters( 'nav_menu_submenu_css_class', $classes, $args, $depth ) );
        $class_names = $class_names ? ' class="' . esc_attr( $class_names ) . '"' : '';

        // Build HTML for output
        $output .= "\n" . $indent . '<ul class="sub-menu">' . "\n";
    }

    // Add main/subclasses to li's and links
    function start_el( &$output, $item, $depth = 0, $args = null, $id = 0 ): void
    {
        global $wp_query;
        $indent = ( $depth > 0 ? str_repeat( "\t", $depth ) : '' ); // code indent

        // Depth-dependent classes
        $depth_classes = array(
            ( $depth == 0 ? 'main-menu-item' : 'sub-menu-item' ),
            ( $depth >=2 ? 'sub-sub-menu-item' : '' ),
            ( $depth % 2 ? 'menu-item-odd' : 'menu-item-even' ),
            'menu-item-depth-' . $depth
        );
        $depth_class_names = join( ' ', apply_filters( 'nav_menu_css_class', array_filter( $depth_classes ), $item, $args, $depth ) );
        $depth_class_names = $depth_class_names ? ' class="' . esc_attr( $depth_class_names ) . '"' : '';

        // Passed classes
        $classes = empty( $item->classes ) ? array() : (array) $item->classes;
        $class_names = join( ' ', apply_filters( 'nav_menu_css_class', array_filter( $classes ), $item, $args, $depth ) );
        $class_names = $class_names ? ' class="' . esc_attr( $class_names ) . '"' : '';

        // Build HTML
        $output .= $indent . '<li id="menu-item-'. $item->ID . '"' . $class_names .'>';

        // Link attributes
        $attributes  = ! empty( $item->attr_title ) ? ' title="'  . esc_attr( $item->attr_title ) .'"' : '';
        $attributes .= ! empty( $item->target )     ? ' target="' . esc_attr( $item->target     ) .'"' : '';
        $attributes .= ! empty( $item->xfn )        ? ' rel="'    . esc_attr( $item->xfn        ) .'"' : '';
        $attributes .= ! empty( $item->url )        ? ' href="'   . esc_attr( $item->url        ) .'"' : '';

        // Add the class for the icon
        if (str_contains($item->title, '<i class="fas fa-arrow-right"></i>')) {
            $attributes .= ' class="arrow-right"';
        }

        // Build HTML output and pass through the proper filter
        $item_output = sprintf( '%1$s<a%2$s>%3$s%4$s%5$s</a>%6$s',
            $args->before,
            $attributes,
            $args->link_before,
            apply_filters( 'the_title', '<i class="fas fa-arrow-right"></i> '.$item->title, $item->ID ),
            $args->link_after,
            $args->after
        );
        $output .= apply_filters( 'walker_nav_menu_start_el', $item_output, $item, $depth, $args );
    }

}
