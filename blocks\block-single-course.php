<?php 

use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_single_course' );
function everest_single_course() {
Block::make( __( 'Xbees Single Course' ) )
	->add_fields( array() )
    // ->set_mode( 'preview' ) 
	// ->set_inner_blocks( true )
    // ->set_inner_blocks_template( array(
	// 	array( 'carbon-fields/col' ),
	// 	array( 'carbon-fields/col' ),
	// ) )
	->set_render_callback( function ($field, $atts, $inner_blocks_content, $id) {

        if( get_post_type($id) === 'everest-courses' ) { 
            $terms = get_the_terms( $id, 'courses_category' );
            $cat = $cat_link ='';
            if( !empty( $terms ) ) {
                $cat = $terms[0]->name;
                $cat_link = get_term_link( $terms[0]->term_id, 'courses_category');
            }
            ?>
<!--Start academy course Details Area-->
<section class="academy-course-details-area">
    <div class="container">
        <div class="row">

            <div class="col-xl-12">
                <div class="academy-course-details-content wp-block-post-content">
                 <div class="entry-content">
                     <?php echo wpautop( carbon_get_the_post_meta( 'description' ) ); ?>
                     <div class="text-center">
                         <?php echo  do_shortcode('[contact-form-7 id="408ff69" title="Apply Now"]'); ?>
                     </div>
                 </div>
                </div>

            </div>
        </div>
    </div>
    </div>
</section>
<?php }
	} );

}