!function(e){var n={};function t(o){if(n[o])return n[o].exports;var r=n[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,t),r.l=!0,r.exports}t.m=e,t.c=n,t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:o})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(t.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var r in e)t.d(o,r,function(n){return e[n]}.bind(null,r));return o},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=13)}([function(e,n){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}},function(e,n){e.exports=function(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}},function(e,n){e.exports=cf.vendor.lodash},function(e,n){e.exports=cf.vendor["@wordpress/i18n"]},function(e,n){e.exports=cf.vendor.classnames},function(e,n){e.exports=cf.vendor["@wordpress/element"]},function(e,n){e.exports=cf.core},function(e,n,t){"use strict";(function(e){var o=t(8),r=t.n(o),c=t(9),i=t.n(c),a=t(10),s=t.n(a),u=t(11),l=t.n(u),f=t(12),p=t.n(f),m=t(0),d=t.n(m),h=t(1),v=t.n(h),b=t(2),y=t(4),_=t.n(y),O=t(5),w=t(3),g=function(n){function t(){var e,n;r()(this,t);for(var o=arguments.length,c=new Array(o),i=0;i<o;i++)c[i]=arguments[i];return n=s()(this,(e=l()(t)).call.apply(e,[this].concat(c))),v()(d()(d()(n)),"state",{isFocused:!1,searchTerm:"",chosenIcon:null,iconClass:"",availableOptions:[]}),v()(d()(d()(n)),"handleClick",function(e){n.popup.contains(e.target)||n.closeList()}),v()(d()(d()(n)),"handleChange",function(e){var t=e.provider,o=e.value,r=n.props,c=r.id;(0,r.onChange)(c,{value:o,provider:t,icon:o})}),v()(d()(d()(n)),"handleButtonClearClick",function(e){var t=n.props,o=t.id,r=t.onChange;n.setState({searchTerm:"",iconClass:"",chosenIcon:null}),r(o,{value:"",icon:"",provider:""})}),v()(d()(d()(n)),"onIconChange",function(e){var t=n.props.field.options,o=Object(b.first)(Object(b.filter)(t,function(n){return n.value===e}));o&&""===o.value&&(o=null),n.setState({searchTerm:o?o.value:"",iconClass:o?o.class:"",chosenIcon:o})}),v()(d()(d()(n)),"openList",function(){n.setState({isFocused:!0})}),v()(d()(d()(n)),"closeList",function(){n.setState({isFocused:!1})}),v()(d()(d()(n)),"onFocusInput",function(e){e.preventDefault(),n.setState({isFocused:!0}),n.searchInput.focus()}),v()(d()(d()(n)),"onOptionSelect",function(e){n.handleChange(e),n.onIconChange(e.value),n.closeList()}),v()(d()(d()(n)),"onSearchTermChange",function(e){var t=n.props.field.options,o=e.target.value,r=o?Object(b.filter)(t,function(e){var n=[e.value,e.name].concat(e.search_terms).map(function(e){return e.toLowerCase()});return Object(b.some)(n,function(e){return-1!==e.indexOf(o.toLowerCase())})}):t;n.setState({searchTerm:o,availableOptions:r})}),n}return p()(t,n),i()(t,[{key:"componentWillMount",value:function(){document.addEventListener("mousedown",this.handleClick,!1)}},{key:"componentWillUnmount",value:function(){document.removeEventListener("mousedown",this.handleClick,!1)}},{key:"componentDidMount",value:function(){var e=this,n=this.props,t=n.field,o=n.value,r=t.options,c=Object(b.filter)(r,function(n){var t=[n.value,n.name].concat(n.search_terms).map(function(e){return e.toLowerCase()});return Object(b.some)(t,function(n){return-1!==n.indexOf(e.state.searchTerm.toLowerCase())})});this.onIconChange(o.value),this.setState({searchTerm:o?o.icon:"",availableOptions:c})}},{key:"render",value:function(){var n,t=this,o=this.props,r=o.name,c=o.value,i=this.openList,a=this.onSearchTermChange,s=this.state,u=s.iconClass,l=s.isFocused,f=s.searchTerm,p=s.availableOptions,m=s.chosenIcon;return n=m&&m.icon?e.createElement("img",{src:m.icon}):e.createElement("i",{className:u}),e.createElement("div",{className:"cf-icon-wrapper"},e.createElement("input",{type:"hidden",name:"".concat(r,"[provider]"),value:c.provider,readOnly:!0}),e.createElement("input",{type:"hidden",name:"".concat(r,"[icon]"),value:c.icon,readOnly:!0}),e.createElement("div",{className:"cf-icon-preview"},e.createElement("div",{className:"cf-icon-preview__canvas"},m?n:e.createElement("span",{className:"cf-icon-preview__canvas-label"},Object(w.__)("No icon selected","carbon-field-icon-ui"))),e.createElement("input",{type:"text",className:"cf-icon-preview__label",value:m?m.name:"",readOnly:!0})),e.createElement("div",{className:"cf-icon-switcher",ref:function(e){return t.popup=e}},e.createElement("div",{className:_()({"cf-icon-search":!0,"cf-icon-search--focused":l,"dashicons-before":!0,"dashicons-search":!0})},e.createElement("input",{type:"text",onFocus:i,onChange:a,value:f,className:"cf-icon-search__input",placeholder:Object(w.__)("Search icon ...","carbon-field-icon-ui"),ref:function(e){return t.searchInput=e}}),e.createElement("button",{type:"button",className:"cf-icon-search__clear button button-small",onClick:this.handleButtonClearClick},Object(w.__)("Clear","carbon-field-icon-ui"))),e.createElement("div",{className:_()({"cf-icon-switcher__options":!0,"cf-icon-switcher__options--opened":l})},e.createElement("ul",{className:"cf-icon-switcher__options-list"},p.length?p.map(function(n){return e.createElement("li",{key:n.value,className:"cf-icon-switcher__options-list__item cf-icon-switcher__options-list__item--".concat(n.value)},e.createElement("button",{type:"button",onClick:function(){t.onOptionSelect(n)},className:_()({active:n.value===c})},n.icon?e.createElement("img",{src:n.icon,className:n.class}):e.createElement("i",{className:n.class,dangerouslySetInnerHTML:{__html:n.contents}}),e.createElement("span",null,n.name)))}):e.createElement("li",{key:"no-results",className:"cf-icon-switcher__options-list__item cf-icon-switcher__options-list__item--no-results"},Object(w.__)("No results found","carbon-field-icon-ui"))))))}}]),t}(O.Component);n.a=g}).call(this,t(5))},function(e,n){e.exports=function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}},function(e,n){function t(e,n){for(var t=0;t<n.length;t++){var o=n[t];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}e.exports=function(e,n,o){return n&&t(e.prototype,n),o&&t(e,o),e}},function(e,n,t){var o=t(15),r=t(0);e.exports=function(e,n){return!n||"object"!==o(n)&&"function"!=typeof n?r(e):n}},function(e,n){function t(n){return e.exports=t=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},t(n)}e.exports=t},function(e,n,t){var o=t(16);e.exports=function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&o(e,n)}},function(e,n,t){"use strict";t.r(n);var o=t(6),r=(t(14),t(7));Object(o.registerFieldType)("icon",r.a)},function(e,n,t){},function(e,n){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(n){return"function"==typeof Symbol&&"symbol"===t(Symbol.iterator)?e.exports=o=function(e){return t(e)}:e.exports=o=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":t(e)},o(n)}e.exports=o},function(e,n){function t(n,o){return e.exports=t=Object.setPrototypeOf||function(e,n){return e.__proto__=n,e},t(n,o)}e.exports=t}]);