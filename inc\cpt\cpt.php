
<?php 
function everest_register_cpts() {

/**
 * Post Type: Testimonial.
 */

$labels = [
    "name" => esc_html__( "Testimonial", "everest" ),
    "singular_name" => esc_html__( "Testimonial", "everest" ),
    "menu_name" => esc_html__( "Testimonial", "everest" ),
    "all_items" => esc_html__( "All Testimonial", "everest" ),
    "add_new" => esc_html__( "Add new", "everest" ),
    "add_new_item" => esc_html__( "Add new Testimonial", "everest" ),
    "edit_item" => esc_html__( "Edit Testimonial", "everest" ),
    "new_item" => esc_html__( "New Testimonial", "everest" ),
    "view_item" => esc_html__( "View Testimonial", "everest" ),
    "view_items" => esc_html__( "View Testimonial", "everest" ),
    "search_items" => esc_html__( "Search Testimonial", "everest" ),
    "not_found" => esc_html__( "No Testimonial found", "everest" ),
    "not_found_in_trash" => esc_html__( "No Testimonial found in trash", "everest" ),
    "parent" => esc_html__( "Parent Testimonial:", "everest" ),
    "featured_image" => esc_html__( "Featured image for this Testimonial", "everest" ),
    "set_featured_image" => esc_html__( "Set featured image for this Testimonial", "everest" ),
    "remove_featured_image" => esc_html__( "Remove featured image for this Testimonial", "everest" ),
    "use_featured_image" => esc_html__( "Use as featured image for this Testimonial", "everest" ),
    "archives" => esc_html__( "Testimonial archives", "everest" ),
    "insert_into_item" => esc_html__( "Insert into Testimonial", "everest" ),
    "uploaded_to_this_item" => esc_html__( "Upload to this Testimonial", "everest" ),
    "filter_items_list" => esc_html__( "Filter Testimonial list", "everest" ),
    "items_list_navigation" => esc_html__( "Testimonial list navigation", "everest" ),
    "items_list" => esc_html__( "Testimonial list", "everest" ),
    "attributes" => esc_html__( "Testimonial attributes", "everest" ),
    "name_admin_bar" => esc_html__( "Testimonial", "everest" ),
    "item_published" => esc_html__( "Testimonial published", "everest" ),
    "item_published_privately" => esc_html__( "Testimonial published privately.", "everest" ),
    "item_reverted_to_draft" => esc_html__( "Testimonial reverted to draft.", "everest" ),
    "item_trashed" => esc_html__( "Testimonial trashed.", "everest" ),
    "item_scheduled" => esc_html__( "Testimonial scheduled", "everest" ),
    "item_updated" => esc_html__( "Testimonial updated.", "everest" ),
    "parent_item_colon" => esc_html__( "Parent Testimonial:", "everest" ),
];

$args = [
    "label" => esc_html__( "Testimonial", "everest" ),
    "labels" => $labels,
    "description" => "",
    "public" => true,
    "publicly_queryable" => true,
    "show_ui" => true,
    "show_in_rest" => false,
    "rest_base" => "",
    "rest_controller_class" => "WP_REST_Posts_Controller",
    "rest_namespace" => "wp/v2",
    "has_archive" => false,
    "show_in_menu" => true,
    "show_in_nav_menus" => true,
    "delete_with_user" => false,
    "exclude_from_search" => false,
    "capability_type" => "post",
    "map_meta_cap" => true,
    "hierarchical" => false,
    "can_export" => false,
    "rewrite" => [ "slug" => "everest-testimonial", "with_front" => true ],
    "query_var" => true,
    "menu_position" => 6,
    "menu_icon" => "dashicons-format-status",
    "supports" => [ "title", "editor", "thumbnail", "author" ],
    "show_in_graphql" => false,
];

register_post_type( "everest-testimonial", $args );

/**
 * Post Type: courses.
 */

$labels = [
    "name" => esc_html__( "courses", "everest" ),
    "singular_name" => esc_html__( "course", "everest" ),
    "menu_name" => esc_html__( "Courses", "everest" ),
    "all_items" => esc_html__( "All courses", "everest" ),
    "add_new" => esc_html__( "Add new", "everest" ),
    "add_new_item" => esc_html__( "Add new course", "everest" ),
    "edit_item" => esc_html__( "Edit course", "everest" ),
    "new_item" => esc_html__( "New course", "everest" ),
    "view_item" => esc_html__( "View course", "everest" ),
    "view_items" => esc_html__( "View courses", "everest" ),
    "search_items" => esc_html__( "Search courses", "everest" ),
    "not_found" => esc_html__( "No courses found", "everest" ),
    "not_found_in_trash" => esc_html__( "No courses found in trash", "everest" ),
    "parent" => esc_html__( "Parent course:", "everest" ),
    "featured_image" => esc_html__( "Featured image for this course", "everest" ),
    "set_featured_image" => esc_html__( "Set featured image for this course", "everest" ),
    "remove_featured_image" => esc_html__( "Remove featured image for this course", "everest" ),
    "use_featured_image" => esc_html__( "Use as featured image for this course", "everest" ),
    "archives" => esc_html__( "course archives", "everest" ),
    "insert_into_item" => esc_html__( "Insert into course", "everest" ),
    "uploaded_to_this_item" => esc_html__( "Upload to this course", "everest" ),
    "filter_items_list" => esc_html__( "Filter courses list", "everest" ),
    "items_list_navigation" => esc_html__( "courses list navigation", "everest" ),
    "items_list" => esc_html__( "courses list", "everest" ),
    "attributes" => esc_html__( "courses attributes", "everest" ),
    "name_admin_bar" => esc_html__( "course", "everest" ),
    "item_published" => esc_html__( "course published", "everest" ),
    "item_published_privately" => esc_html__( "course published privately.", "everest" ),
    "item_reverted_to_draft" => esc_html__( "course reverted to draft.", "everest" ),
    "item_trashed" => esc_html__( "course trashed.", "everest" ),
    "item_scheduled" => esc_html__( "course scheduled", "everest" ),
    "item_updated" => esc_html__( "course updated.", "everest" ),
    "parent_item_colon" => esc_html__( "Parent course:", "everest" ),
];

$args = [
    "label" => esc_html__( "courses", "everest" ),
    "labels" => $labels,
    "description" => "",
    "public" => true,
    "publicly_queryable" => true,
    "show_ui" => true,
    "show_in_rest" => true,
    "rest_base" => "",
    "rest_controller_class" => "WP_REST_Posts_Controller",
    "rest_namespace" => "wp/v2",
    "has_archive" => true,
    "show_in_menu" => true,
    "show_in_nav_menus" => true,
    "delete_with_user" => false,
    "exclude_from_search" => false,
    "capability_type" => "post",
    "map_meta_cap" => true,
    "hierarchical" => true,
    "can_export" => true,
    "rewrite" => [ "slug" => "everest-courses", "with_front" => true ],
    "query_var" => true,
    "menu_position" => 7,
    "menu_icon" => "dashicons-welcome-learn-more",
    "supports" => [ "title", "thumbnail" ],
    "show_in_graphql" => false,
];

register_post_type( "everest-courses", $args );

/**
 * Post Type: Eventes.
 */

$labels = [
    "name" => esc_html__( "Eventes", "everest" ),
    "singular_name" => esc_html__( "Evente", "everest" ),
    "menu_name" => esc_html__( "Eventes", "everest" ),
    "all_items" => esc_html__( "All Eventes", "everest" ),
    "add_new" => esc_html__( "Add new", "everest" ),
    "add_new_item" => esc_html__( "Add new Evente", "everest" ),
    "edit_item" => esc_html__( "Edit Evente", "everest" ),
    "new_item" => esc_html__( "New Evente", "everest" ),
    "view_item" => esc_html__( "View Evente", "everest" ),
    "view_items" => esc_html__( "View Eventes", "everest" ),
    "search_items" => esc_html__( "Search Eventes", "everest" ),
    "not_found" => esc_html__( "No Eventes found", "everest" ),
    "not_found_in_trash" => esc_html__( "No Eventes found in trash", "everest" ),
    "parent" => esc_html__( "Parent Evente:", "everest" ),
    "featured_image" => esc_html__( "Featured image for this Evente", "everest" ),
    "set_featured_image" => esc_html__( "Set featured image for this Evente", "everest" ),
    "remove_featured_image" => esc_html__( "Remove featured image for this Evente", "everest" ),
    "use_featured_image" => esc_html__( "Use as featured image for this Evente", "everest" ),
    "archives" => esc_html__( "Evente archives", "everest" ),
    "insert_into_item" => esc_html__( "Insert into Evente", "everest" ),
    "uploaded_to_this_item" => esc_html__( "Upload to this Evente", "everest" ),
    "filter_items_list" => esc_html__( "Filter Eventes list", "everest" ),
    "items_list_navigation" => esc_html__( "Eventes list navigation", "everest" ),
    "items_list" => esc_html__( "Eventes list", "everest" ),
    "attributes" => esc_html__( "Eventes attributes", "everest" ),
    "name_admin_bar" => esc_html__( "Evente", "everest" ),
    "item_published" => esc_html__( "Evente published", "everest" ),
    "item_published_privately" => esc_html__( "Evente published privately.", "everest" ),
    "item_reverted_to_draft" => esc_html__( "Evente reverted to draft.", "everest" ),
    "item_trashed" => esc_html__( "Evente trashed.", "everest" ),
    "item_scheduled" => esc_html__( "Evente scheduled", "everest" ),
    "item_updated" => esc_html__( "Evente updated.", "everest" ),
    "parent_item_colon" => esc_html__( "Parent Evente:", "everest" ),
];

$args = [
    "label" => esc_html__( "Eventes", "everest" ),
    "labels" => $labels,
    "description" => "",
    "public" => true,
    "publicly_queryable" => true,
    "show_ui" => true,
    "show_in_rest" => false,
    "rest_base" => "",
    "rest_controller_class" => "WP_REST_Posts_Controller",
    "rest_namespace" => "wp/v2",
    "has_archive" => true,
    "show_in_menu" => true,
    "show_in_nav_menus" => true,
    "delete_with_user" => false,
    "exclude_from_search" => false,
    "capability_type" => "post",
    "map_meta_cap" => true,
    "hierarchical" => false,
    "can_export" => true,
    "rewrite" => [ "slug" => "everest-eventes", "with_front" => true ],
    "query_var" => true,
    "menu_position" => 8,
    "menu_icon" => "dashicons-megaphone",
    "supports" => [ "title", "editor", "thumbnail" ],
    "show_in_graphql" => false,
];

// register_post_type( "everest-eventes", $args );
}

add_action( 'init', 'everest_register_cpts' );

function everest_register_taxes() {

	/**
	 * Taxonomy: Courses Category.
	 */

	$labels = [
		"name" => esc_html__( "Courses Category", "everest" ),
		"singular_name" => esc_html__( "Course Category", "everest" ),
		"menu_name" => esc_html__( "Courses Category", "everest" ),
		"all_items" => esc_html__( "All Courses Category", "everest" ),
		"edit_item" => esc_html__( "Edit Course Category", "everest" ),
		"view_item" => esc_html__( "View Course Category", "everest" ),
		"update_item" => esc_html__( "Update Course Category name", "everest" ),
		"add_new_item" => esc_html__( "Add new Course Category", "everest" ),
		"new_item_name" => esc_html__( "New Course Category name", "everest" ),
		"parent_item" => esc_html__( "Parent Course Category", "everest" ),
		"parent_item_colon" => esc_html__( "Parent Course Category:", "everest" ),
		"search_items" => esc_html__( "Search Courses Category", "everest" ),
		"popular_items" => esc_html__( "Popular Courses Category", "everest" ),
		"separate_items_with_commas" => esc_html__( "Separate Courses Category with commas", "everest" ),
		"add_or_remove_items" => esc_html__( "Add or remove Courses Category", "everest" ),
		"choose_from_most_used" => esc_html__( "Choose from the most used Courses Category", "everest" ),
		"not_found" => esc_html__( "No Courses Category found", "everest" ),
		"no_terms" => esc_html__( "No Courses Category", "everest" ),
		"items_list_navigation" => esc_html__( "Courses Category list navigation", "everest" ),
		"items_list" => esc_html__( "Courses Category list", "everest" ),
		"back_to_items" => esc_html__( "Back to Courses Category", "everest" ),
		"name_field_description" => esc_html__( "The name is how it appears on your site.", "everest" ),
		"parent_field_description" => esc_html__( "Assign a parent term to create a hierarchy. The term Jazz, for example, would be the parent of Bebop and Big Band.", "everest" ),
		"slug_field_description" => esc_html__( "The slug is the URL-friendly version of the name. It is usually all lowercase and contains only letters, numbers, and hyphens.", "everest" ),
		"desc_field_description" => esc_html__( "The description is not prominent by default; however, some themes may show it.", "everest" ),
	];

	
	$args = [
		"label" => esc_html__( "Courses Category", "everest" ),
		"labels" => $labels,
		"public" => true,
		"publicly_queryable" => true,
		"hierarchical" => true,
		"show_ui" => true,
		"show_in_menu" => true,
		"show_in_nav_menus" => true,
		"query_var" => true,
		"rewrite" => [ 'slug' => 'courses_category', 'with_front' => true,  'hierarchical' => true, ],
		"show_admin_column" => true,
		"show_in_rest" => true,
		"show_tagcloud" => false,
		"rest_base" => "courses_category",
		"rest_controller_class" => "WP_REST_Terms_Controller",
		"rest_namespace" => "wp/v2",
		"show_in_quick_edit" => true,
		"sort" => true,
		"show_in_graphql" => false,
	];
	register_taxonomy( "courses_category", [ "everest-courses" ], $args );
}
add_action( 'init', 'everest_register_taxes' );
