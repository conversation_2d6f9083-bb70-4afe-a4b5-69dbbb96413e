<?php 

use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_archive_course' );
function everest_archive_course() {
Block::make( __( 'Xbees Archive Course' ) )
	->add_fields(array(
		Field::make('select', 'course_category', __('Course Category'))
			->set_options(function () {
				$categories = get_terms([
							'taxonomy' => 'courses_category',
							'hide_empty' => false,
						]);
				$options = [];
				if (!is_wp_error($categories)) {
					foreach ($categories as $category) {
						$options[$category->term_id] = $category->name;
					}
				}
				return $options;
			})
	))
	->set_mode( 'preview' )
	->set_render_callback( function ($field, $atts, $inner_blocks_content, $id) {
		$term_id = $field['course_category'];
            if ($term_id) {
                $term = get_term($term_id, 'courses_category');
                if (!is_wp_error($term)) {
                    everest_courses_related($term->term_id, "", term_description($term->term_id));
                }
            } else {
				everest_courses_related(get_queried_object_id(), 'Our ' . get_the_archive_title(), get_the_archive_description()); 
			}
	} );
}