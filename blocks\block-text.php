<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_text' );
function everest_block_text() {
Block::make( __( 'Xbees Text' ) )
	->add_fields( array(
		Field::make( 'textarea', 'content', __( 'Block Content' ) ),
        Field::make( 'select', 'tag', __( 'Text Tag' ) )
        ->add_options( array(
            'left' => __( 'Left' ),
            'center' => __( 'Center' ),
            'right' => __( 'Right' ),
        ) ),
        Field::make( 'select', 'align', __( 'Text alignment' ) )
        ->add_options( array(
            'left' => __( 'Left' ),
            'center' => __( 'Center' ),
            'right' => __( 'Right' ),
        ) )
	) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'gear' )
    ->set_mode( 'preview' )
    
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks ) {


        var_dump($attributes);
		?>

		<div class="block">
			<div class="block__heading">
				<h1><?php echo esc_html( $fields['heading'] ); ?></h1>
			</div><!-- /.block__heading -->

			<div class="block__image">
				<?php echo wp_get_attachment_image( $fields['image'], 'full' ); ?>
			</div><!-- /.block__image -->

			<div class="block__content">
				<?php echo apply_filters( 'the_content', $fields['content'] ); ?>
			</div><!-- /.block__content -->
		</div><!-- /.block -->

		<?php
	} );
}