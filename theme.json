{"version": 2, "$schema": "https://schemas.wp.org/trunk/theme.json", "settings": {"appearanceTools": true, "color": {"palette": [{"slug": "base", "color": "#ffffff", "name": "Base"}, {"slug": "contrast", "color": "#4e4f53", "name": "Contrast"}, {"slug": "primary", "color": "#003399", "name": "Primary"}, {"slug": "secondary", "color": "#b7121f", "name": "Secondary"}, {"slug": "transparent", "color": "transparent", "name": "Transparent"}], "gradients": [{"slug": "primary-white", "gradient": "linear-gradient(#003399 49.9%,#fff 50%)", "name": "Primary color to white"}, {"slug": "white-primary", "gradient": "linear-gradient(#fff 49.9%,#003399 50%)", "name": "Horizontal white to Primary color"}], "duotone": [{"colors": ["#b7121f", "#003399"], "slug": "secondary-primary", "name": "Secondary and primary"}, {"colors": ["#003399", "#4e4f53"], "slug": "primary-contrast", "name": "Primary and Contrast"}]}, "layout": {"contentSize": "1170px", "wideSize": "1170px"}, "spacing": {"units": ["px", "em", "rem", "vh", "vw", "%"]}, "typography": {"dropCap": true, "fluid": true, "fontFamilies": [{"name": "System", "slug": "system", "fontFamily": "-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,Oxygen-Sans,Ubuntu,Cantarell,\"Helvetica Neue\",sans-serif"}, {"name": "<PERSON><PERSON>", "slug": "serif", "fontFamily": "\"Times New Roman\",\"New York\",Times,\"Noto Serif\",serif"}, {"name": "Monospace", "slug": "monospace", "fontFamily": "Consolas,Menlo,Monaco,\"SF Mono\",\"DejaVu Sans Mono\",\"Roboto Mono\",\"Courier New\",Courier,monospace"}], "fontSizes": [{"slug": "small", "size": "1.125rem", "name": "Small", "fluid": false}, {"slug": "medium", "size": "1.5rem", "name": "Medium", "fluid": false}, {"slug": "large", "size": "2rem", "name": "Large", "fluid": {"min": "1.75rem", "max": "2rem"}}, {"slug": "x-large", "size": "2.75rem", "name": "XL", "fluid": {"min": "2.5rem", "max": "2.75rem"}}, {"slug": "xx-large", "size": "3.75rem", "name": "XXL", "fluid": {"min": "3rem", "max": "3.75rem"}}]}, "useRootPaddingAwareAlignments": true}, "styles": {"blocks": {"core/code": {"color": {"text": "var(--wp--preset--color--contrast)"}}, "core/comment-author-name": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/comment-date": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/comment-edit-link": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/post-comments-form": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/comment-reply-link": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/navigation": {"css": "& .wp-block-site-title{ margin:0; font-weight:400;}", "elements": {"link": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"textDecoration": "underline"}, ":hover": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"textDecoration": "none"}}, ":focus": {"color": {"text": "var(--wp--preset--color--contrast)"}}, ":active": {"color": {"text": "var(--wp--preset--color--contrast)"}}}}}, "core/post-author-name": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/post-content": {"spacing": {"margin": {"top": "var(--wp--preset--spacing--60)", "bottom": "var(--wp--preset--spacing--60)"}}}, "core/post-date": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/post-excerpt": {"elements": {"link": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}}}, "core/post-featured-image": {"spacing": {"margin": {"bottom": "var(--wp--preset--spacing--40)"}}}, "core/post-template": {"spacing": {"padding": {"top": "var(--wp--preset--spacing--50)", "bottom": "var(--wp--preset--spacing--50)"}}}, "core/post-terms": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/query-pagination": {"spacing": {"padding": {"top": "var(--wp--preset--spacing--50)"}}}, "core/search": {"css": ".wp-block-search__button-inside .wp-block-search__inside-wrapper{border: none;}", "typography": {"lineHeight": "1"}}}, "color": {"background": "var(--wp--preset--color--base)", "text": "var(--wp--preset--color--contrast)"}, "elements": {"button": {"spacing": {"padding": {"top": "var(--wp--preset--spacing--30)", "right": "var(--wp--preset--spacing--30)", "bottom": "var(--wp--preset--spacing--30)", "left": "var(--wp--preset--spacing--30)"}}, "border": {"color": "var(--wp--preset--color--transparent)", "width": "4px", "style": "solid", "radius": "4px"}, "color": {"background": "var(--wp--preset--color--primary)", "text": "var(--wp--preset--color--contrast)"}, "typography": {"fontSize": "var(--wp--preset--font-size--small)", "fontWeight": "700"}, "shadow": "var(--wp--preset--shadow--natural)", ":hover": {"border": {"color": "var(--wp--preset--color--secondary)"}}}, "caption": {"color": {"text": "var(--wp--preset--color--primary)"}}, "cite": {"color": {"text": "var(--wp--preset--color--primary)"}}, "link": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"textDecoration": "none"}, ":hover": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"textDecoration": "none"}}, ":focus": {"color": {"text": "var(--wp--preset--color--contrast)"}}, ":active": {"color": {"text": "var(--wp--preset--color--contrast)"}}}}, "spacing": {"padding": {"right": "var(--wp--preset--spacing--50)", "left": "var(--wp--preset--spacing--50)"}}, "typography": {}}, "templateParts": [{"name": "header", "title": "Header", "area": "header"}, {"name": "footer", "title": "Footer", "area": "footer"}], "customTemplates": [{"name": "no-title", "title": "No title", "postTypes": ["post", "page"]}]}