this.cf=this.cf||{},this.cf.metaboxes=function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=44)}([function(e,t){!function(){e.exports=this.lodash}()},function(e,t){!function(){e.exports=this.cf.vendor["callbag-basics"]}()},function(e,t){!function(){e.exports=this.wp.data}()},function(e,t){!function(){e.exports=this.wp.hooks}()},function(e,t,n){var r=n(22);e.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){!function(){e.exports=this.cf.core}()},function(e,t){!function(){e.exports=this.wp.i18n}()},function(e,t){!function(){e.exports=this.wp.element}()},function(e,t){!function(){e.exports=this.cf.vendor.immer}()},function(e,t){!function(){e.exports=this.wp.compose}()},function(e,t){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){!function(){e.exports=this.cf.vendor["refract-callbag"]}()},function(e,t,n){var r=n(28),o=n(29),c=n(23),i=n(30);e.exports=function(e,t){return r(e)||o(e,t)||c(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(){return e.exports=n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,n.apply(this,arguments)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(37),o=n(38),c=n(23),i=n(39);e.exports=function(e){return r(e)||o(e)||c(e)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t){return e.exports=n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(22);function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,r(o.key),o)}}e.exports=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(31);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(20).default,o=n(10);e.exports=function(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t){return e.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){!function(){e.exports=this.cf.vendor.classnames}()},function(e,t,n){var r=n(20).default,o=n(27);e.exports=function(e){var t=o(e,"string");return"symbol"===r(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(24);e.exports=function(e,t){if(e){if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=e=>t=>(n,r)=>{if(0!==n)return;let o;t(0,(t,n)=>{0===t?(o=n,r(t,n)):1===t?e(n)?r(t,n):o(1):r(t,n)})}},function(e,t){e.exports=e=>(t,n)=>{if(0!==t)return;if("function"!=typeof e)return n(0,()=>{}),void n(2);let r,o;const c=e=>{r=r||2===e,r&&"function"==typeof o&&o()};n(0,c),o=e((e,t)=>{r||0===e||(n(e,t),c(e))})}},function(e,t,n){var r=n(20).default;e.exports=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,c,i,a=[],u=!0,s=!1;try{if(c=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=c.call(n)).done)&&(a.push(r.value),a.length!==t);u=!0);}catch(e){s=!0,o=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return a}},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t,r){return e.exports=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t,r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){var r=n(24);e.exports=function(e){if(Array.isArray(e))return r(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){"use strict";n.r(t),n.d(t,"registerContainerType",(function(){return ot})),n.d(t,"getContainerType",(function(){return ct}));var r={};n.r(r),n.d(r,"setupState",(function(){return g})),n.d(r,"updateState",(function(){return h})),n.d(r,"updateFieldValue",(function(){return w})),n.d(r,"addFields",(function(){return x})),n.d(r,"cloneFields",(function(){return E})),n.d(r,"removeFields",(function(){return P})),n.d(r,"addContainer",(function(){return S})),n.d(r,"removeContainer",(function(){return D})),n.d(r,"receiveSidebar",(function(){return I})),n.d(r,"lockSaving",(function(){return F})),n.d(r,"unlockSaving",(function(){return T}));var o={};n.r(o),n.d(o,"getContainers",(function(){return A})),n.d(o,"getContainerById",(function(){return C})),n.d(o,"getFields",(function(){return k})),n.d(o,"getFieldsByContainerId",(function(){return N})),n.d(o,"getFieldById",(function(){return R})),n.d(o,"isSavingLocked",(function(){return G})),n.d(o,"isDirty",(function(){return L})),n.d(o,"isFieldUpdated",(function(){return M})),n.d(o,"getComplexGroupValues",(function(){return B}));var c=n(6),i=n(3),a=n(2),u=n(0),s=n.n(u),l=n(4),f=n.n(l),d=n(8),p=n.n(d),b=n(5);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function y(e,t,n,r){var o=Object(u.cloneDeep)(n[e]);return o.id=t,"complex"===o.type&&o.value.forEach((function(e){e.id=Object(b.uniqueId)(),r=e.fields.reduce((function(e,t){var r=t.id,o=Object(b.uniqueId)();return t.id=o,y(r,o,n,e)}),r)})),r.concat(o)}function j(e,t,n){var r=t[e];return"complex"===r.type&&r.value.forEach((function(e){n=e.fields.reduce((function(e,n){return j(n.id,t,e)}),n)})),n.concat(e)}var v=Object(a.combineReducers)({containers:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SETUP_STATE":return t.payload.containers;case"UPDATE_STATE":return p()(e,(function(e){Object(u.values)(t.payload.containers).forEach((function(t){e[t.id]=t}))}));case"ADD_CONTAINER":return p()(e,(function(e){e[t.payload.id]=t.payload}));case"REMOVE_CONTAINER":return Object(u.omit)(e,t.payload);default:return e}},fields:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SETUP_STATE":return t.payload.fields;case"UPDATE_STATE":return p()(e,(function(e){Object(u.values)(t.payload.fields).forEach((function(t){e[t.id]=t}))}));case"UPDATE_FIELD_VALUE":return p()(e,(function(n){var r=t.payload,o=r.fieldId,c=r.value,i=r.fieldsToRemove;n[o].value=c,i.reduce((function(t,n){return j(n,e,t)}),[]).forEach((function(e){Object(u.unset)(n,e)}))}));case"ADD_FIELDS":return p()(e,(function(e){t.payload.fields.forEach((function(t){e[t.id]=t}))}));case"CLONE_FIELDS":return p()(e,(function(e){var n=t.payload,r=n.originFieldIds,o=n.cloneFieldIds,c=r.reduce((function(t,n,r){return y(n,o[r],e,t)}),[]);Object(u.assign)(e,Object(u.keyBy)(c,"id"))}));case"REMOVE_FIELDS":var n=t.payload.fieldIds.reduce((function(t,n){return j(n,e,t)}),[]);return Object(u.omit)(e,n);case"RECEIVE_SIDEBAR":return p()(e,(function(e){Object(u.forEach)(e,(function(e){"sidebar"===e.type&&e.options.unshift(t.payload)}))}));default:return e}},savingLock:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"LOCK_SAVING":return m(m({},e),{},f()({},t.payload.lockName,!0));case"UNLOCK_SAVING":return Object(u.omit)(e,[t.payload.lockName]);default:return e}},isDirty:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"UPDATE_FIELD_VALUE":return!0;default:return e}},isFieldUpdated:function(e,t){switch(t.type){case"UPDATE_FIELD_VALUE":return{action:t};default:return!1}}});function g(e,t){return{type:"SETUP_STATE",payload:{containers:e,fields:t}}}function h(e,t){return{type:"UPDATE_STATE",payload:{containers:e,fields:t}}}function w(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return{type:"UPDATE_FIELD_VALUE",payload:{fieldId:e,value:t,fieldsToRemove:n}}}function x(e){return{type:"ADD_FIELDS",payload:{fields:e}}}function E(e,t){return{type:"CLONE_FIELDS",payload:{originFieldIds:e,cloneFieldIds:t}}}function P(e){return{type:"REMOVE_FIELDS",payload:{fieldIds:e}}}function S(e){return{type:"ADD_CONTAINER",payload:e}}function D(e){return{type:"REMOVE_CONTAINER",payload:e}}function I(e){return{type:"RECEIVE_SIDEBAR",payload:e}}function F(e){return{type:"LOCK_SAVING",payload:{lockName:e}}}function T(e){return{type:"UNLOCK_SAVING",payload:{lockName:e}}}function A(e){return e.containers}function C(e,t){return e.containers[t]}function k(e){return e.fields}function N(e,t){return Object(u.filter)(e.fields,["container_id",t])}function R(e,t){return e.fields[t]}function G(e){return Object.keys(e.savingLock).length>0}function L(e){return e.isDirty}function M(e){return e.isFieldUpdated}function B(e,t){var n=Object(u.pick)(k(e),t);return n=Object(u.mapKeys)(n,(function(e){return e.base_name.replace(/\-/g,"_")})),Object(u.mapValues)(n,(function(e){return e.value}))}function U(e,t,n){return(e=Object(u.cloneDeep)(e)).id=Object(b.uniqueId)(),e.container_id=t,"complex"===e.type&&e.value.forEach((function(e){e.id=Object(b.uniqueId)(),e.container_id=t,e.fields=e.fields.map((function(e){return U(e,t,n)}))})),n.push(e),Object(u.pick)(e,["id","type","name","base_name"])}function V(e){var t=[];return{containers:e.filter((function(e){var t=e.id;return!Object(u.endsWith)(t,"__i__")})).map((function(e){return Object(u.assign)({},e,{fields:e.fields.map((function(n){return U(n,e.id,t)}))})})),fields:t}}Object(a.registerStore)("carbon-fields/metaboxes",{reducer:v,actions:r,selectors:o});var q=V(Object(u.get)(window.cf,"preloaded.containers",[])),Q=q.containers,K=q.fields;Object(a.dispatch)("carbon-fields/metaboxes").setupState(Object(u.keyBy)(Q,"id"),Object(u.keyBy)(K,"id"));var $=n(9),W=Object($.createHigherOrderComponent)((function(e){var t=Object(a.withSelect)((function(e,t){var n=window.cf.config,r=n.compactInput,o=n.compactInputKey,c=e("carbon-fields/metaboxes").getFieldById(t.id),i=c&&c.value,a=t.name||c.name;return r&&!t.name&&-1===a.indexOf("widget-carbon_fields")&&(a="".concat(o,"[").concat(a,"]")),{field:c,name:a,value:i}})),n=Object(a.withDispatch)((function(e){return{onChange:e("carbon-fields/metaboxes").updateFieldValue}}));return Object($.compose)(t,n)(e)}),"withField"),z=n(12),H=n.n(z),J=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,n){if(0===e){var r=!1;for(n(0,(function(e){2===e&&(r=!0,t.length=0)}));0!==t.length;)n(1,t.shift());r||n(2)}}};const X={};var Y=function(e,t){return e===t},Z=function(e){return void 0===e&&(e=Y),function(t){return function(n,r){if(0===n){var o,c,i=!1;t(0,(function(t,n){0===t&&(c=n),1===t?i&&e(o,n)?c(1):(i=!0,o=n,r(1,n)):r(t,n)}))}}}},ee=n(1);function te(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return void 0===e?[]:Object(u.pick)(t,Object(u.difference)(Object(u.map)(e.fields,"id"),n))}function ne(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Object(u.map)(e,(function(e){return[e.id,"".concat(Object(u.repeat)("parent.",t)).concat(e.base_name)]}))}var re=Object(b.withConditionalLogic)((function(e,t){var n=Object(a.select)("carbon-fields/metaboxes").getFieldsByContainerId;return Object(ee.pipe)(Object(ee.merge)(J(n(e.containerId)),Object(b.fromSelector)(n,e.containerId)),(e=>t=>(n,r)=>{if(0!==n)return;let o,c,i=!1,a=X;t(0,(t,n)=>{if(0===t)return o=n,e(0,(e,t)=>0===e?(c=t,void c(1)):1===e?(a=void 0,c(2),o(2),void(i&&r(2))):void(2===e&&(c=null,null!=t&&(a=t,o(2),i&&r(e,t))))),i=!0,r(0,(e,t)=>{a===X&&(2===e&&c&&c(2),o(e,t))}),void(a!==X&&r(2,a));2===t&&c&&c(2),r(t,n)})})(t.unmount),Z(u.isEqual))}),(function(e,t){t=Object(u.keyBy)(t,"id");var n=Object(a.select)("carbon-fields/metaboxes").getContainerById(e.containerId),r=[];if(Object(u.some)(n.fields,["id",e.id]))r=ne(r=te(n,t,[e.id]));else{var o=e.name.replace(new RegExp("^".concat(window.cf.config.compactInputKey,"\\[(.+?)\\]")),"$1"),c=Object(u.find)(t,(function(t){return t.container_id===e.containerId&&Object(u.startsWith)(o,t.name)})),i=o.split(/\[|\]/g);i.shift(),(i=i.filter((function(e){return""!==e}))).pop();var s=i.reduce((function(e,t){return isNaN(t)?e+1:e}),0);r=ne(r=te(n,t,[c.id]),s+1);for(var l="".concat(c.id,".value");i.length>0;){var f=i.shift(),d=!isNaN(f),p=!d;if(d){l="".concat(l,"[").concat(f,"]");var b=te(Object(u.get)(t,l),t,[e.id]);r=r.concat(ne(b,s)),l="".concat(l,".fields")}if(p){var O=Object(u.find)(Object(u.get)(t,l),["name",f]);O&&(l="".concat(O.id,".value")),s--}}}return r=r.map((function(e){var n=H()(e,2),r=n[0];return[n[1],Object(u.get)(t,"".concat(r,".value"))]})),Object(u.fromPairs)(r)}));function oe(){return!Object(u.isUndefined)(window._wpLoadBlockEditor)}function ce(e,t){return Object(u.find)(e,(function(e){return e.name===t}))}Object(i.addFilter)("carbon-fields.association.metabox","carbon-fields/metaboxes",Object(b.withProps)((function(e){return{hierarchyResolver:function(){var t,n,r,o,c=Object(a.select)("carbon-fields/metaboxes").getContainerById(e.containerId),i=Object(a.select)("carbon-fields/metaboxes").getFieldsByContainerId(e.containerId),s=(t=e.name,n=window.cf.config,r=n.compactInput,o=n.compactInputKey,r&&0===t.indexOf(o)?t.replace(new RegExp("^".concat(o,"\\[(.+?)\\]")),"$1"):t).split(/\[|\]/g);if(s=s.filter((function(e){return""!==e})),"widget"===c.type)return e.field.base_name;for(var l=ce(i,s.shift()),f=i.indexOf(l),d=l.base_name;s.length>0;){var p=s.shift(),b=!isNaN(p),O=p===e.field.base_name,m=!b&&!O;if(b&&(f="".concat(f,".value.").concat(p,".name"),d="".concat(d,"[").concat(p,"]:").concat(Object(u.get)(i,f),"/")),m){var y=ce(Object(u.get)(i,f.replace(/\.name$/,".fields")),p),j=Object(u.find)(i,["id",y.id]);f=i.indexOf(j),d="".concat(d).concat(j.base_name)}O&&(d="".concat(d).concat(p))}return d}}})));var ie=n(16),ae=n.n(ie),ue=n(17),se=n.n(ue),le=n(10),fe=n.n(le),de=n(18),pe=n.n(de),be=n(19),Oe=n.n(be),me=n(15),ye=n.n(me),je=n(7),ve=(n(32),n(33),Object($.compose)(W,Object(b.withFilters)("carbon-fields.field-wrapper.metabox"))(b.Field));var ge=function(e){pe()(n,e);var t=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=ye()(e);if(t){var o=ye()(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Oe()(this,n)}}(n);function n(){var e;ae()(this,n);for(var r=arguments.length,o=new Array(r),c=0;c<r;c++)o[c]=arguments[c];return e=t.call.apply(t,[this].concat(o)),f()(fe()(e),"handleAddGroup",(function(t,n){var r=e.props,o=r.id,c=r.field,i=r.value,a=r.addFields,s=r.onChange;t=Object(u.cloneDeep)(t);var l=[];t.id=Object(b.uniqueId)(),t.container_id=c.container_id,t.fields=t.fields.map((function(e){return U(e,c.container_id,l)})),t.collapsed=!1,a(l),s(o,i.concat(t)),n(t)})),f()(fe()(e),"handleCloneGroup",(function(t,n){var r=e.props,o=r.id,c=r.value,i=r.cloneFields,a=r.onChange,s=t.fields.map((function(e){return e.id})),l=s.map((function(){return Object(b.uniqueId)()})),f=Object(u.cloneDeep)(t);f.id=Object(b.uniqueId)(),f.fields.forEach((function(e,t){e.id=l[t]})),i(s,l),a(o,p()(c,(function(e){e.splice(c.indexOf(t)+1,0,f)}))),n(f)})),f()(fe()(e),"handleRemoveGroup",(function(t){var n=e.props,r=n.id,o=n.value;(0,n.onChange)(r,Object(u.without)(o,t),t.fields.map((function(e){return e.id})))})),f()(fe()(e),"handleToggleGroup",(function(t){var n=e.props,r=n.field,o=n.value;(0,n.onChange)(r.id,p()(o,(function(e){var n=Object(u.find)(e,["id",t]);n.collapsed=!n.collapsed})))})),f()(fe()(e),"handleToggleAllGroups",(function(t){var n=e.props,r=n.field,o=n.value;(0,n.onChange)(r.id,p()(o,(function(e){e.forEach((function(e){e.collapsed=t}))})))})),f()(fe()(e),"handleGroupSetup",(function(t,n){return Object(u.assign)({},n,{id:t.id,name:t.name,prefix:"".concat(e.props.name,"[").concat(n.index,"]"),fields:t.fields,collapsed:t.collapsed,context:"metabox"})})),f()(fe()(e),"handleGroupFieldSetup",(function(t,n,r){return[ve,Object(u.assign)({},n,{key:t.id,id:t.id,containerId:e.props.containerId,name:"".concat(r.prefix,"[").concat(t.name,"]")})]})),e}return se()(n,[{key:"render",value:function(){var e=this.handleGroupSetup,t=this.handleGroupFieldSetup,n=this.handleAddGroup,r=this.handleCloneGroup,o=this.handleRemoveGroup,c=this.handleToggleGroup,i=this.handleToggleAllGroups,a=this.props,u=a.value;return(0,a.children)({allGroupsAreCollapsed:u.every((function(e){return e.collapsed})),handleGroupSetup:e,handleGroupFieldSetup:t,handleAddGroup:n,handleCloneGroup:r,handleRemoveGroup:o,handleToggleGroup:c,handleToggleAllGroups:i})}}]),n}(je.Component),he=Object(a.withSelect)((function(e,t){var n=e("carbon-fields/metaboxes").getComplexGroupValues;return{groupValues:t.value.map((function(e){var t=e.fields.map((function(e){return e.id}));return[e.name,n(t)]}))}})),we=Object(a.withDispatch)((function(e){var t=e("carbon-fields/metaboxes");return{addFields:t.addFields,cloneFields:t.cloneFields}}));Object(i.addFilter)("carbon-fields.complex.metabox","carbon-fields/metaboxes",(function(e){return Object($.compose)(he,we)((function(t){var n=t.id,r=t.field,o=t.name,c=t.value,i=t.groupValues;return wp.element.createElement(ge,t,(function(a){var u=a.allGroupsAreCollapsed,s=a.handleGroupSetup,l=a.handleGroupFieldSetup,f=a.handleAddGroup,d=a.handleCloneGroup,p=a.handleRemoveGroup,b=a.handleToggleGroup,O=a.handleToggleAllGroups;return wp.element.createElement(e,{groupIdKey:"id",groupFilterKey:"name",id:n,field:r,name:o,value:c,groupValues:i,allGroupsAreCollapsed:u,onGroupSetup:s,onGroupFieldSetup:l,onAddGroup:f,onCloneGroup:d,onRemoveGroup:p,onToggleGroup:b,onToggleAllGroups:O,onChange:t.onChange})}))}))}));var _e=n(13),xe=n.n(_e);Object(i.addFilter)("carbon-fields.date_time.metabox","carbon-fields/metaboxes",(function(e){return function(t){return wp.element.createElement(e,xe()({buttonText:Object(c.__)("Select Date","carbon-fields-ui")},t))}})),Object(i.addFilter)("carbon-fields.file.metabox","carbon-fields/metaboxes",(function(e){return function(t){return wp.element.createElement(e,xe()({buttonLabel:Object(c.__)("Select File","carbon-fields-ui"),mediaLibraryButtonLabel:Object(c.__)("Use File","carbon-fields-ui"),mediaLibraryTitle:Object(c.__)("Select File","carbon-fields-ui")},t))}})),Object(i.addFilter)("carbon-fields.image.metabox","carbon-fields/metaboxes",(function(e){return function(t){return wp.element.createElement(e,xe()({buttonLabel:Object(c.__)("Select Image","carbon-fields-ui"),mediaLibraryButtonLabel:Object(c.__)("Use Image","carbon-fields-ui"),mediaLibraryTitle:Object(c.__)("Select Image","carbon-fields-ui")},t))}})),n(34),n(35),Object(i.addFilter)("carbon-fields.media_gallery.metabox","carbon-fields/metaboxes",(function(e){return function(t){return wp.element.createElement(e,xe()({buttonLabel:Object(c.__)("Select Attachments","carbon-fields-ui"),mediaLibraryButtonLabel:Object(c.__)("Use Attachments","carbon-fields-ui"),mediaLibraryTitle:Object(c.__)("Select Attachments","carbon-fields-ui")},t))}})),n(36),Object(i.addFilter)("carbon-fields.sidebar.metabox","carbon-fields/metaboxes",Object(a.withDispatch)((function(e){return{onAdded:e("carbon-fields/metaboxes").receiveSidebar}}))),Object(i.addFilter)("carbon-fields.field-edit.metabox","carbon-fields/metaboxes",Object($.compose)(W,re,Object(a.withDispatch)((function(e){if(oe()){var t=e("core/editor");return{lockSaving:t.lockPostSaving,unlockSaving:t.unlockPostSaving}}var n=e("carbon-fields/metaboxes");return{lockSaving:n.lockSaving,unlockSaving:n.unlockSaving}})),b.withValidation));var Ee=n(11),Pe=Object(Ee.withEffects)((function(){return Object(b.fromSelector)(Object(a.select)("carbon-fields/metaboxes").isSavingLocked)}),{handler:function(){return function(e){document.querySelectorAll('\n\t\t\t#publishing-action input#publish,\n\t\t\t#publishing-action input#save,\n\t\t\t#addtag input#submit,\n\t\t\t#edittag input[type="submit"],\n\t\t\t#your-profile input#submit\n\t\t').forEach((function(t){t.disabled=e}))}}})((function(){return null})),Se=(...e)=>t=>(n,r)=>{if(0!==n)return;let o,c,i=!1,a=!1;for(r(0,(t,n)=>{a&&1===t&&(c=[1,n]),2===t&&(i=!0,e.length=0),o&&o(t,n)});0!==e.length;)1===e.length&&(a=!0),r(1,e.shift());i||t(0,(e,t)=>{if(0===e)return o=t,a=!1,void(c&&(o(...c),c=null));r(e,t)})};function De(e){var t=parseInt(e.value,10);return!isNaN(t)&&t>=0?t:0}function Ie(e){var t=0;if(e.className){var n=e.className.match(/^level-(\d+)/);n&&(t=parseInt(n[1],10)+1)}return t}function Fe(e){for(var t=[],n=e,r=Ie(e);r>0&&n;)if(Ie(n)===r){var o=parseInt(n.value,10);o>0&&t.unshift(o),n=n.previousSibling,r--}else n=n.previousSibling;return t}var Te={post_ancestors:[],post_parent_id:0,post_level:1};function Ae(e){var t=e.options[e.selectedIndex];return{post_ancestors:Fe(t),post_parent_id:De(t),post_level:Ie(t)+1}}Object(i.addFilter)("carbon-fields.conditional-display-post-parent.classic","carbon-fields/metaboxes",(function(){var e=document.querySelector("select#parent_id");return e?Object(ee.pipe)(ee.fromEvent.default(e,"change"),Object(ee.map)((function(e){return Ae(e.target)})),Se(Ae(e))):J(Te)})),Object(i.addFilter)("carbon-fields.conditional-display-post-parent.gutenberg","carbon-fields/metaboxes",(function(){var e=Object(a.select)("core"),t=e.getPostType,n=e.getEntityRecords;return Object(ee.pipe)(Object(ee.combine)(Object(b.fromSelector)(Object(a.select)("core/editor").getCurrentPostId),Object(b.fromSelector)(Object(a.select)("core/editor").getEditedPostAttribute,"type"),Object(b.fromSelector)(Object(a.select)("core/editor").getEditedPostAttribute,"parent")),Z(u.isEqual),Object(ee.map)((function(e){var r=H()(e,3),o=r[0],c=r[1],i=r[2];if(i=parseInt(i,10),isNaN(i))return Te;var a=t(c);if(!Object(u.get)(a,["hierarchical"],!1))return Te;var s=function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],o=Object(u.find)(n,["id",t]);return o?(r.unshift(o.id),o.parent?e(o.parent,n,r):r):r}(i,n("postType",c,{per_page:-1,exclude:o,parent_exclude:o,orderby:"menu_order",order:"asc"})||[]);return{post_ancestors:s,post_parent_id:i,post_level:s.length+1}})))}));var Ce=(e,t,n)=>(r,o)=>{if(0!==r)return;let c=!1;const i=e=>{o(1,e)};if(o(0,r=>{if(2===r)if(c=!0,e.removeEventListener)e.removeEventListener(t,i,n);else{if(!e.removeListener)throw new Error("cannot remove listener from node. No method found.");e.removeListener(t,i)}}),!c)if(e.addEventListener)e.addEventListener(t,i,n);else{if(!e.addListener)throw new Error("cannot add listener to node. No method found.");e.addListener(t,i)}},ke=n(25),Ne=n.n(ke),Re=(e,t,n)=>Ne()(n=>{let r=n.target;for(;r!==e;){if(r.matches(t))return!0;r=r.parentElement}return!1})(Ce(e,n)),Ge={post_format:"standard"};function Le(e){var t=e.value;return"0"===t&&(t="standard"),{post_format:t}}Object(i.addFilter)("carbon-fields.conditional-display-post-format.classic","carbon-fields/metaboxes",(function(){var e=document.querySelector("div#post-formats-select");return e?Object(ee.pipe)(Re(e,"input.post-format","change"),Object(ee.map)((function(e){return Le(e.target)})),Se(Le(e.querySelector("input.post-format:checked")))):J(Ge)})),Object(i.addFilter)("carbon-fields.conditional-display-post-format.gutenberg","carbon-fields/metaboxes",(function(){return Object(ee.pipe)(Object(b.fromSelector)(Object(a.select)("core/editor").getEditedPostAttribute,"format"),Z(),Object(ee.filter)(Boolean),Object(ee.map)((function(e){return{post_format:e}})),Se(Ge))}));var Me={post_template:""};function Be(e){var t=e.value;return"default"===t&&(t=""),{post_template:t}}Object(i.addFilter)("carbon-fields.conditional-display-post-template.classic","carbon-fields/metaboxes",(function(){var e=document.querySelector("select#page_template");return e?Object(ee.pipe)(ee.fromEvent.default(e,"change"),Object(ee.map)((function(e){return Be(e.target)})),Se(Be(e))):J(Me)})),Object(i.addFilter)("carbon-fields.conditional-display-post-template.gutenberg","carbon-fields/metaboxes",(function(){return Object(ee.pipe)(Object(b.fromSelector)(Object(a.select)("core/editor").getEditedPostAttribute,"template"),Z(),Object(ee.filter)(u.isString),Object(ee.map)((function(e){return{post_template:e}})),Se(Me))}));var Ue=n(14),Ve=n.n(Ue);function qe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Qe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qe(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ke(e,t){e["original_".concat(t)]=e[t],e[t]=function(){var n=new Event("change"),r=window.jQuery(arguments.length<=0?void 0:arguments[0]).closest(".postbox").find("textarea.the-tags").get(0),o=e["original_".concat(t)].apply(e,arguments);return r.dispatchEvent(n),o}}window.tagBox&&(Ke(window.tagBox,"parseTags"),Ke(window.tagBox,"flushTags")),Object(i.addFilter)("carbon-fields.conditional-display-post-term.classic","carbon-fields/metaboxes",(function(){return Object(ee.pipe)(ee.merge.apply(void 0,Ve()(function(){var e=document.querySelectorAll('div[id^="taxonomy-"]');return Ve()(e).map((function(e){var t=e.id.replace("taxonomy-","");return Object(ee.pipe)(Re(e.querySelector("#".concat(t,"checklist")),'input[type="checkbox"]',"change"),Object(ee.scan)((function(e,n){var r=n.target;return p()(e,(function(e){var n=parseInt(r.value,10);r.checked?e[t].push(n):Object(u.pull)(e[t],n)}))}),f()({},t,[])),Se(function(e){var t=document.querySelectorAll("#".concat(e,'checklist input[type="checkbox"]:checked'));return Ve()(t).reduce((function(t,n){var r=parseInt(n.value,10);return t[e].push(r),t}),f()({},e,[]))}(t)))}))}()).concat(Ve()(function(){var e=document.querySelectorAll('div[id^="tagsdiv-"]');return Ve()(e).map((function(e){var t=e.id.replace("tagsdiv-","");return Object(ee.pipe)(ee.fromEvent.default(e.querySelector("textarea.the-tags"),"change"),Object(ee.map)((function(e){var n=e.target;return f()({},t,n.value?n.value.split(","):[])})),Se(function(e){var t=document.querySelector("#tagsdiv-".concat(e," textarea.the-tags")),n=t.value?t.value.split(","):[];return f()({},e,n)}(t)))}))}()))),Object(ee.scan)((function(e,t){return{post_term:Qe(Qe({},e.post_term),t)}}),{post_term:{}}))})),Object(i.addFilter)("carbon-fields.conditional-display-post-term.gutenberg","carbon-fields/metaboxes",(function(){var e=Object(a.select)("core").getTaxonomies,t=Object(a.select)("core/editor").getEditedPostAttribute;return Object(ee.pipe)(Object(b.fromSelector)(e,{per_page:-1}),Object(ee.filter)(Boolean),Object(ee.map)((function(e){var n=e.map((function(e){return[e.slug,t(e.rest_base)||[]]}));return{post_term:Object(u.fromPairs)(n)}})))}));var $e={term_ancestors:[],term_parent:0,term_level:1};function We(e){var t=e.options[e.selectedIndex];return{term_ancestors:Fe(t),term_parent:De(t),term_level:Ie(t)+1}}Object(i.addFilter)("carbon-fields.conditional-display-term-parent.classic","carbon-fields/metaboxes",(function(){var e=document.querySelector("select#parent");return e?Object(ee.pipe)(ee.fromEvent.default(e,"change"),Object(ee.map)((function(e){return We(e.target)})),Se(We(e))):J($e)}));var ze={user_role:""};function He(e){return{user_role:e.value}}Object(i.addFilter)("carbon-fields.conditional-display-user-role.classic","carbon-fields/metaboxes",(function(){var e=document.querySelector("select#role");if(!e){var t=document.querySelector("fieldset[data-profile-role]");return J(t?{user_role:t.dataset.profileRole}:ze)}return Object(ee.pipe)(ee.fromEvent.default(e,"change"),Object(ee.map)((function(e){return He(e.target)})),Se(He(e)))}));var Je=Object($.createHigherOrderComponent)((function(e){return Object(a.withSelect)((function(e,t){var n=t.id;return{container:e("carbon-fields/metaboxes").getContainerById(n)}}))(e)}),"withContainer");Object(i.addFilter)("carbon-fields.register-container-type","carbon-fields/metaboxes",(function(e,t,n){return Object($.compose)(Je,Object(b.withFilters)("carbon-fields.".concat(e,".").concat(t)))(n)})),n(40),Object(i.addFilter)("carbon-fields.widget.classic","carbon-fields/metaboxes",Object(Ee.withEffects)((function(){return Object(b.fromSelector)(Object(a.select)("carbon-fields/metaboxes").isFieldUpdated)}),{handler:function(e){return function(t){var n=t.action;if(n){var r=e.container,o=n.payload;r.fields.map((function(e){return e.id})).indexOf(o.fieldId)>=0&&window.jQuery(".container-".concat(r.id)).closest(".widget-inside").trigger("change")}}}}));var Xe=n(26),Ye=n.n(Xe);function Ze(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t};return Ye()((function(r){var o=function(){return r(1,n.apply(void 0,arguments))};return e(o),function(){return t(o)}}))}Object(i.addFilter)("carbon-fields.term_meta.classic","carbon-fields/metaboxes",Object(Ee.withEffects)((function(){return Object(ee.pipe)(Ze((function(e){return window.jQuery(document).on("ajaxSuccess",e)}),(function(e){return window.jQuery(document).off("ajaxSuccess",e)}),(function(e,t,n,r){return{options:n,data:r}})),Object(ee.filter)((function(e){var t=e.options,n=e.data;return t.data&&t.data.indexOf("carbon_fields_container")>-1&&t.data.indexOf("add-tag")>-1&&!n.documentElement.querySelector("wp_error")})))}),{handler:function(e){return function(){var t=s.a.map(e.container.fields,"id"),n=V(s.a.get(window.cf,"preloaded.containers",[])),r=n.containers,o=n.fields,c=s.a.find(r,["id",e.id]),i=s.a.filter(o,["container_id",e.id]),u=Object(a.dispatch)("carbon-fields/metaboxes"),l=u.updateState,f=u.removeFields;l(s.a.keyBy([c],"id"),s.a.keyBy(i,"id")),f(t)}}})),n(41),Object(i.addFilter)("carbon-fields.theme_options.classic","carbon-fields/metaboxes",Object(Ee.withEffects)((function(){return Object(ee.pipe)(Ce(window,"scroll"),Object(ee.map)((function(){return window.jQuery(window).scrollTop()})))}),{handler:function(){return function(e){var t=window.jQuery(".carbon-box:first"),n=window.jQuery("#postbox-container-1"),r=window.jQuery("#wpadminbar").height()+10,o=t.offset().top-r;o>0&&n.toggleClass("fixed",e>=o).css("top",r)}}})),n(42);var et=n(21),tt=n.n(et);n(43);var nt=function(e){pe()(n,e);var t=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=ye()(e);if(t){var o=ye()(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Oe()(this,n)}}(n);function n(){var e;ae()(this,n);for(var r=arguments.length,o=new Array(r),c=0;c<r;c++)o[c]=arguments[c];return e=t.call.apply(t,[this].concat(o)),f()(fe()(e),"state",{currentTab:null}),f()(fe()(e),"renderField",(function(t){var n=Object(b.getFieldType)(t.type,"metabox");return n?wp.element.createElement(ve,{key:t.id,id:t.id},wp.element.createElement(n,{id:t.id,containerId:e.props.id})):null})),f()(fe()(e),"handleTabClick",(function(t){e.setState({currentTab:t})})),e}return se()(n,[{key:"componentDidMount",value:function(){var e=this.props.container;this.isTabbed(e)&&this.setState({currentTab:Object.keys(e.settings.tabs)[0]})}},{key:"isTabbed",value:function(e){return Object(u.isPlainObject)(e.settings.tabs)}},{key:"render",value:function(){var e=this,t=this.state.currentTab,n=this.props.container,r=this.isTabbed(n),o=tt()(["cf-container","cf-container-".concat(n.id),"cf-container-".concat(Object(u.kebabCase)(n.type))].concat(Ve()(n.classes),[f()({"cf-container--plain":!r},"cf-container--tabbed cf-container--".concat(n.layout),r)]));return wp.element.createElement("div",{className:o},wp.element.createElement("input",{type:"hidden",name:n.nonce.name,value:n.nonce.value}),r&&wp.element.createElement("div",{className:"cf-container__tabs cf-container__tabs--".concat(n.layout)},wp.element.createElement("ul",{className:"cf-container__tabs-list"},Object(u.map)(n.settings.tabs,(function(n,r){var o=tt()("cf-container__tabs-item",{"cf-container__tabs-item--current":r===t});return wp.element.createElement("li",{key:r,className:o,tabIndex:-1,role:"tab","aria-selected":t===r},wp.element.createElement("button",{type:"button",onClick:function(){return e.handleTabClick(r)},dangerouslySetInnerHTML:{__html:r}}))})))),r&&Object(u.map)(n.settings.tabs,(function(r,o){return wp.element.createElement("div",{className:"cf-container__fields",key:o,hidden:o!==t},Object(u.map)(r,(function(t){var r=Object(u.find)(n.fields,["name",t]);return e.renderField(r)})))})),!r&&wp.element.createElement("div",{className:"cf-container__fields"},Object(u.map)(n.fields,this.renderField)))}}]),n}(je.Component),rt=Object(b.createRegistry)("container",["classic","gutenberg"]),ot=rt.registerContainerType,ct=rt.getContainerType;function it(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function at(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?it(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):it(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ut={};function st(e,t){var n,r,o=document.querySelector(".container-".concat(e.id)),i=ct(e.type,t);if(o){var a=wp.element.createElement(i,{id:e.id});if(je.createRoot){var u=Object(je.createRoot)(o);u.render(a),n=e.id,r=u,ut[n]=at(at({createdAt:Math.floor(Date.now()/1e3)},r),{},{unmount:function(){parseFloat(window.cf.config.wp_version)>=6.2?Math.floor(Date.now()/1e3)-ut[n].createdAt>=3&&(r.unmount(),delete ut[n]):(r.unmount(),delete ut[n])}})}else Object(je.render)(a,o,(function(){o.dataset.mounted=!0}))}else console.error(Object(c.sprintf)(Object(c.__)('Could not find DOM element for container "%1$s".',"carbon-fields-ui"),e.id))}["post_meta","term_meta","user_meta","comment_meta","network","theme_options","nav_menu_item","widget"].forEach((function(e){return ot(e,nt)}));var lt={operators:[],isOperatorSupported:function(e){return this.operators.indexOf(e)>-1},evaluate:function(){return!1}};function ft(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function dt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ft(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ft(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function pt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function bt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pt(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ot(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function mt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ot(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ot(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var yt={comparers:[dt(dt({},lt),{},{operators:["=","!="],evaluate:function(e,t,n){switch(t){case"=":return e==n;case"!=":return e!=n;default:return!1}}}),bt(bt({},lt),{},{operators:["IN","NOT IN"],evaluate:function(e,t,n){switch(t){case"IN":return n.indexOf(e)>-1;case"NOT IN":return-1===n.indexOf(e);default:return!1}}}),mt(mt({},lt),{},{operators:[">",">=","<","<="],evaluate:function(e,t,n){switch(t){case">":return e>n;case">=":return e>=n;case"<":return e<n;case"<=":return e<=n;default:return!1}}})],isFulfiled:function(e,t){var n=e.compare,r=e.value;return this.firstComparerIsCorrect(this.getEnvironmentValue(e,t),n,r)},firstComparerIsCorrect:function(e,t,n){var r=Object(u.find)(this.comparers,(function(e){return e.isOperatorSupported(t)}));return r?r.evaluate(e,t,n):(console.error(Object(c.sprintf)(Object(c.__)('Unsupported container condition comparison operator used - "%1$s".',"carbon-fields-ui"),t)),!1)},getEnvironmentValue:function(e,t){return t[e.type]}};function jt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function vt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?jt(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):jt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var gt=vt(vt({},yt),{},{getEnvironmentValue:function(){return!0}});function ht(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function wt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ht(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ht(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var _t=wt(wt({},lt),{},{operators:["=","!="],evaluate:function(e,t,n){switch(t){case"=":return Object(u.includes)(e,n);case"!=":return!Object(u.includes)(e,n);default:return!1}}});function xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Et(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?xt(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):xt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Pt=Et(Et({},yt),{},{comparers:[_t],isFulfiled:function(e,t){var n=this,r=e.compare,o=e.value;if(Object(u.isArray)(o)){var c;switch(r){case"IN":r="=",c="some";break;case"NOT IN":r="!=",c="every";break;default:return!1}return o.map((function(o){return n.isFulfiled(Et(Et({},e),{},{compare:r,value:o}),t)}))[c](Boolean)}return o=o.taxonomy_object.hierarchical?o.term_object.term_id:o.term_object.name,this.firstComparerIsCorrect(this.getEnvironmentValue(e,t),r,o)},getEnvironmentValue:function(e,t){return Object(u.get)(t,"post_term.".concat(e.value.taxonomy),[])}});function St(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Dt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?St(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):St(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var It=Dt(Dt({},yt),{},{isFulfiled:function(e,t){if("default"===(e=Dt({},e)).value)e.value="";else if(Object(u.isArray)(e.value)){var n=e.value.indexOf("default");-1!==n&&(e.value[n]="")}return yt.isFulfiled(e,t)}});function Ft(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Tt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ft(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ft(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var At=Tt(Tt({},lt),{},{operators:["IN","NOT IN"],evaluate:function(e,t,n){switch(t){case"IN":return Object(u.intersection)(e,n).length>0;case"NOT IN":return 0===Object(u.intersection)(e,n).length;default:return!1}}});function Ct(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function kt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ct(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ct(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Nt=kt(kt({},yt),{},{comparers:[_t,At],getEnvironmentValue:function(e,t){return Object(u.get)(t,"post_ancestors",[])}});function Rt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Gt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Rt(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Rt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Lt=Gt(Gt({},yt),{},{isFulfiled:function(e,t){var n=e.compare,r=e.value;return Object(u.isArray)(r)?r=r.map((function(e){return e.term_object.term_id})):Object(u.isPlainObject)(r)&&(r=r.term_object.term_id),this.firstComparerIsCorrect(this.getEnvironmentValue(e,t),n,r)}});function Mt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Bt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Mt(Object(n),!0).forEach((function(t){f()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Mt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ut=Bt(Bt({},yt),{},{comparers:[_t,At],isFulfiled:function(e,t){var n=e.compare,r=e.value;return Object(u.isArray)(r)?r=r.map((function(e){return e.term_object.term_id})):Object(u.isPlainObject)(r)&&(r=r.term_object.term_id),this.firstComparerIsCorrect(this.getEnvironmentValue(e,t),n,r)},getEnvironmentValue:function(e,t){return Object(u.get)(t,"term_ancestors",[])}}),Vt={boolean:gt,post_term:Pt,post_ancestor_id:Nt,post_parent_id:yt,post_level:yt,post_format:yt,post_template:It,term_level:yt,term_parent:Lt,term_ancestor:Ut,user_role:yt};function qt(e,t,n){var r=e.map((function(e){if(e.relation)return qt(e.conditions,t,e.relation);var n=Object(u.get)(Vt,e.type);return n?n.isFulfiled(e,t):(console.error(Object(c.sprintf)(Object(c.__)('Unsupported container condition - "%1$s".',"carbon-fields-ui"),e.type)),!1)}));switch(n){case"AND":return r.every(Boolean);case"OR":return r.some(Boolean);default:return console.error(Object(c.sprintf)(Object(c.__)('Unsupported container condition relation used - "%1$s".',"carbon-fields-ui"),n)),!1}}var Qt=Object(a.withSelect)((function(e){return{containers:e("carbon-fields/metaboxes").getContainers()}})),Kt=Object(Ee.withEffects)((function(e,t){var n=t.context,r=Object(i.applyFilters)("carbon-fields.conditional-display-post-parent.".concat(n)),o=Object(i.applyFilters)("carbon-fields.conditional-display-post-format.".concat(n)),c=Object(i.applyFilters)("carbon-fields.conditional-display-post-template.".concat(n)),a=Object(i.applyFilters)("carbon-fields.conditional-display-post-term.".concat(n)),s=Object(i.applyFilters)("carbon-fields.conditional-display-term-parent.".concat(n)),l=Object(i.applyFilters)("carbon-fields.conditional-display-user-role.".concat(n));return Object(ee.pipe)(Object(ee.merge)(r,o,c,a,s,l),Object(ee.scan)((function(e,t){return p()(e,(function(e){Object(u.assign)(e,t)}))})))}),{handler:function(e){var t=e.containers,n=e.context;return function(e){Object(u.map)(t,(function(t,n){return[n,qt(t.conditions.conditions,e,t.conditions.relation)]})).forEach((function(e){var r=H()(e,2),o=r[0],c=r[1],i=document.getElementById(o),a=document.querySelector(".container-".concat(o)),u=!!a.dataset.mounted;if(i&&(i.hidden=!c),a)if(je.createRoot){var s=ut[o]||null;c&&!s&&st(t[o],n),!c&&s&&s.unmount()}else{var l,f;c&&!u&&st(t[o],n),!c&&u&&(null==a||null===(l=a.dataset)||void 0===l||delete l.mounted,null==a||null===(f=a._reactRootContainer)||void 0===f||f.unmount())}}))}}}),$t=Object($.compose)(Qt,Kt)((function(){return null}));function Wt(e){return decodeURIComponent((e+"").replace(/%(?![\da-f]{2})/gi,(function(){return"%25"})).replace(/\+/g,"%20"))}var zt=Object(Ee.withEffects)((function(){return Object(ee.merge)(Object(ee.pipe)(Ze((function(e){return window.jQuery(document).on("widget-added widget-updated",e)}),(function(e){return window.jQuery(document).off("widget-added widget-updated",e)}),(function(e,t){return{event:e,$widget:t}})),Object(ee.filter)((function(e){return e.$widget[0].id.indexOf("carbon_fields_")>-1})),Object(ee.map)((function(e){return{type:"WIDGET_CREATED_OR_UPDATED",payload:e}}))),Object(ee.pipe)(Ze((function(e){return window.jQuery(document).on("ajaxSend",e)}),(function(e){return window.jQuery(document).off("ajaxSend",e)}),(function(e,t,n,r){return{event:e,xhr:t,options:n,data:r}})),Object(ee.filter)((function(e){var t=e.options;return Object(u.startsWith)(t.data,"carbon_fields_container_")})),Object(ee.map)((function(e){return{type:"WIDGET_BEIGN_UPDATED_OR_DELETED",payload:e}}))))}),{handler:function(){return function(e){var t=Object(a.select)("carbon-fields/metaboxes").getContainerById,n=Object(a.dispatch)("carbon-fields/metaboxes"),r=n.addContainer,o=n.removeContainer,c=n.addFields,i=n.removeFields;switch(e.type){case"WIDGET_CREATED_OR_UPDATED":var s=e.payload,l=s.event,f=s.$widget,d=Object(u.flow)(Wt,JSON.parse)(f.find("[data-json]").data("json")),p=[];if(d.fields=d.fields.map((function(e){return U(e,d,p)})),c(p),r(d),st(d,"classic"),"customize.php"===window.cf.config.pagenow&&"widget-added"===l.type){var b=f.find('[name="widget-id"]').val();f.find('[name="savewidget"]').show().end().find(".widget-content:first").off("keydown","input").off("change input propertychange",":input"),wp.customize.Widgets.getWidgetFormControlForWidget(b).liveUpdateMode=!1}break;case"WIDGET_BEIGN_UPDATED_OR_DELETED":var O=e.payload.options.data.match(/widget-id=(.+?)&/),m=H()(O,2)[1],y="".concat("carbon_fields_container_").concat(m),j=t(y);Object(je.unmountComponentAtNode)(document.querySelector(".container-".concat(y)));var v=_.map(j.fields,"id");o(y),i(v)}}}})((function(){return null})),Ht=Object(a.withSelect)((function(e){return{isDirty:e("carbon-fields/metaboxes").isDirty()}}))((function(e){return wp.element.createElement("input",{type:"hidden",name:window.cf.config.revisionsInputKey,disabled:!e.isDirty,value:"1"})}));Object(c.setLocaleData)(window.cf.config.locale,"carbon-fields-ui");var Jt=oe()?"gutenberg":"classic";Object(i.addAction)("carbon-fields.init","carbon-fields/metaboxes",(function(){var e,t;e=Jt,t=Object(a.select)("carbon-fields/metaboxes").getContainers(),Object(u.forEach)(t,(function(t){st(t,e)})),function(e){var t=window.cf.config.pagenow,n=document.createElement("div"),r=wp.element.createElement(je.Fragment,null,!oe()&&wp.element.createElement(Pe,null),("widgets.php"===t||"customize.php"===t)&&wp.element.createElement(zt,null),wp.element.createElement($t,{context:e}));je.createRoot?Object(je.createRoot)(n).render(r):Object(je.render)(r,n);var o=document.querySelector("#poststuff");if(o){var c=document.createElement("div"),i=wp.element.createElement(Ht,null),a=o.appendChild(c);je.createRoot?Object(je.createRoot)(a).render(i):Object(je.render)(i,a)}}(Jt)}))}]);