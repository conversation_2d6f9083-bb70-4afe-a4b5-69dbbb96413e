<?php 
$terms = get_categories([
    'taxonomy'    => 'courses_category',
    'hide_empty'  => 1,
    ] );
?>

<!--Start Popular Quizzes Area-->
<section class="popular-quizzes-area">
    <div class="container">
        <div class="sec-title-style8 text-center">
            <h2><?php echo $title; ?></h2>
            <p><?php echo $subtext; ?></p>
        </div>
        <div class="popular-quizzes__menu-box">
            <div class="project-menu-box">
                <ul class="project-filter clearfix post-filter has-dynamic-filters-counter">
                    <li data-filter=".filter-item" class="active">
                        <span class="filter-text">All Classes</span>
                    </li>
                    <?php if( $terms ) {
                            foreach ( $terms as $key => $term ) {  ?>
                    <li data-filter=".<?php echo $term->slug; ?>"><span class="filter-text"><?php echo $term->name; ?></span></li>
                    <?php } } ?>
                </ul>
            </div>
        </div>

        <div class="row filter-layout masonary-layout">
            <?php
            $posts_per_page = 3;
            $related_args = array(
                'post_type' => 'everest-courses',
                'posts_per_page' => $posts_per_page,
            );
            
            $related_query = new WP_Query( $related_args );
            if ( $related_query->have_posts() ) :
                while ( $related_query->have_posts() ) : $related_query->the_post();
                $terms = get_the_terms( get_the_ID(), 'courses_category' );
                $cat = $cat_link = $slug = '';
                if( !empty( $terms ) ) {
                    $cat = $terms[0]->name;
                    $slug  = $terms[0]->slug;
                    $cat_link = get_term_link( $terms[0]->term_id, 'courses_category');
                }
            ?>
            <!--Start Single popular quizzes Box-->
            <div class="col-xl-4 col-lg-4 col-md-12 filter-item <?php echo $slug; ?>">
                <div class="single-popular-quizzes-box">
                    <div class="img-holder">
                        <?php the_post_thumbnail(); ?>
                        <div class="rate-box">
                            <h3>$24.90</h3>
                        </div>
                    </div>
                    <div class="title-holder">
                        <p><?php echo $cat; ?></p>
                        <h3><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                        <div class="meta-info">
                            <ul>
                                <li>
                                    <span class="flaticon-list-interface-symbol"></span>
                                    <a href="#">20 Hours</a>
                                </li>
                                <li>
                                    <span class="flaticon-person-info"></span>
                                    <a href="#">05 Students</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!--End Single popular quizzes Box-->
            <?php
                endwhile;
            endif;
            wp_reset_postdata();
            ?>
        </div>
    </div>
</section>
<!--End Popular Quizzes Area-->