(function(e,t,n){"use strict";function $(n){u=t.documentElement;a=t.body;z();yt=this;n=n||{};Nt=n.constants||{};if(n.easing){for(var r in n.easing){V[r]=n.easing[r]}}It=n.edgeStrategy||"set";Et={beforerender:n.beforerender,render:n.render};St=n.forceHeight!==false;if(St){Tt=n.scale||1}Ct=n.mobileDeceleration||x;Pt=n.smoothScrolling!==false;Ht=n.smoothScrollingDuration||T;Bt={targetTop:yt.getScrollTop()};qt=(n.mobileCheck||function(){return/Android|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent||navigator.vendor||e.opera)})();if(qt){wt=t.getElementById("skrollr-body");if(wt){st()}J();pt(u,[g,w],[y])}else{pt(u,[g,b],[y])}yt.refresh();ot(e,"resize orientationchange",function(){var e=u.clientWidth;var t=u.clientHeight;if(t!==Mt||e!==Ot){Mt=t;Ot=e;_t=true}});var i=W();(function s(){G();Wt=i(s)})();return yt}var r={get:function(){return yt},init:function(e){return yt||new $(e)},VERSION:"0.6.22"};var i=Object.prototype.hasOwnProperty;var s=e.Math;var o=e.getComputedStyle;var u;var a;var f="touchstart";var l="touchmove";var c="touchcancel";var h="touchend";var p="skrollable";var d=p+"-before";var v=p+"-between";var m=p+"-after";var g="skrollr";var y="no-"+g;var b=g+"-desktop";var w=g+"-mobile";var E="linear";var S=1e3;var x=.004;var T=200;var N="start";var C="end";var k="center";var L="bottom";var A="___skrollable_id";var O=/^(?:input|textarea|button|select)$/i;var M=/^\s+|\s+$/g;var _=/^data(?:-(_\w+))?(?:-?(-?\d*\.?\d+p?))?(?:-?(start|end|top|center|bottom))?(?:-?(top|center|bottom))?$/;var D=/\s*([\w\-\[\]]+)\s*:\s*(.+?)\s*(?:;|$)/gi;var P=/^([a-z\-]+)\[(\w+)\]$/;var H=/-([a-z])/g;var B=function(e,t){return t.toUpperCase()};var j=/[\-+]?[\d]*\.?[\d]+/g;var F=/\{\?\}/g;var I=/rgba?\(\s*-?\d+\s*,\s*-?\d+\s*,\s*-?\d+/g;var q=/[a-z\-]+-gradient/g;var R="";var U="";var z=function(){var e=/^(?:O|Moz|webkit|ms)|(?:-(?:o|moz|webkit|ms)-)/;if(!o){return}var t=o(a,null);for(var n in t){R=n.match(e)||+n==n&&t[n].match(e);if(R){break}}if(!R){R=U="";return}R=R[0];if(R.slice(0,1)==="-"){U=R;R={"-webkit-":"webkit","-moz-":"Moz","-ms-":"ms","-o-":"O"}[R]}else{U="-"+R.toLowerCase()+"-"}};var W=function(){var t=e.requestAnimationFrame||e[R.toLowerCase()+"RequestAnimationFrame"];var n=mt();if(qt||!t){t=function(t){var r=mt()-n;var i=s.max(0,1e3/60-r);return e.setTimeout(function(){n=mt();t()},i)}}return t};var X=function(){var t=e.cancelAnimationFrame||e[R.toLowerCase()+"CancelAnimationFrame"];if(qt||!t){t=function(t){return e.clearTimeout(t)}}return t};var V={begin:function(){return 0},end:function(){return 1},linear:function(e){return e},quadratic:function(e){return e*e},cubic:function(e){return e*e*e},swing:function(e){return-s.cos(e*s.PI)/2+.5},sqrt:function(e){return s.sqrt(e)},outCubic:function(e){return s.pow(e-1,3)+1},bounce:function(e){var t;if(e<=.5083){t=3}else if(e<=.8489){t=9}else if(e<=.96208){t=27}else if(e<=.99981){t=91}else{return 1}return 1-s.abs(3*s.cos(e*t*1.028)/t)}};$.prototype.refresh=function(e){var r;var i;var s=false;if(e===n){s=true;bt=[];Ft=0;e=t.getElementsByTagName("*")}else if(e.length===n){e=[e]}r=0;i=e.length;for(;r<i;r++){var o=e[r];var u=o;var a=[];var f=Pt;var l=It;if(!o.attributes){continue}var c=0;var h=o.attributes.length;for(;c<h;c++){var d=o.attributes[c];if(d.name==="data-anchor-target"){u=t.querySelector(d.value);if(u===null){throw'Unable to find anchor target "'+d.value+'"'}continue}if(d.name==="data-smooth-scrolling"){f=d.value!=="off";continue}if(d.name==="data-edge-strategy"){l=d.value;continue}var v=d.name.match(_);if(v===null){continue}var m={props:d.value,element:o};a.push(m);var g=v[1];if(g){m.constant=g.substr(1)}var y=v[2];if(/p$/.test(y)){m.isPercentage=true;m.offset=(y.slice(0,-1)|0)/100}else{m.offset=y|0}var b=v[3];var w=v[4]||b;if(!b||b===N||b===C){m.mode="absolute";if(b===C){m.isEnd=true}else if(!m.isPercentage){m.offset=m.offset*Tt}}else{m.mode="relative";m.anchors=[b,w]}}if(!a.length){continue}var E,S;var x;if(!s&&A in o){x=o[A];E=bt[x].styleAttr;S=bt[x].classAttr}else{x=o[A]=Ft++;E=o.style.cssText;S=ht(o)}bt[x]={element:o,styleAttr:E,classAttr:S,anchorTarget:u,keyFrames:a,smoothScrolling:f,edgeStrategy:l};pt(o,[p],[])}ft();r=0;i=e.length;for(;r<i;r++){var T=bt[e[r][A]];if(T===n){continue}Y(T);et(T)}return yt};$.prototype.relativeToAbsolute=function(e,t,n){var r=u.clientHeight;var i=e.getBoundingClientRect();var s=i.top;var o=i.bottom-i.top;if(t===L){s-=r}else if(t===k){s-=r/2}if(n===L){s+=o}else if(n===k){s+=o/2}s+=yt.getScrollTop();return s+.5|0};$.prototype.animateTo=function(e,t){t=t||{};var r=mt();var i=yt.getScrollTop();Dt={startTop:i,topDiff:e-i,targetTop:e,duration:t.duration||S,startTime:r,endTime:r+(t.duration||S),easing:V[t.easing||E],done:t.done};if(!Dt.topDiff){if(Dt.done){Dt.done.call(yt,false)}Dt=n}return yt};$.prototype.stopAnimateTo=function(){if(Dt&&Dt.done){Dt.done.call(yt,true)}Dt=n};$.prototype.isAnimatingTo=function(){return!!Dt};$.prototype.setScrollTop=function(t,n){jt=n===true;if(qt){Rt=s.min(s.max(t,0),xt)}else{e.scrollTo(0,t)}return yt};$.prototype.getScrollTop=function(){if(qt){return Rt}else{return e.pageYOffset||u.scrollTop||a.scrollTop||0}};$.prototype.getMaxScrollTop=function(){return xt};$.prototype.on=function(e,t){Et[e]=t;return yt};$.prototype.off=function(e){delete Et[e];return yt};$.prototype.destroy=function(){var e=X();e(Wt);at();pt(u,[y],[g,b,w]);var t=0;var i=bt.length;for(;t<i;t++){it(bt[t].element)}u.style.overflow=a.style.overflow="auto";u.style.height=a.style.height="auto";if(wt){r.setStyle(wt,"transform","none")}yt=n;wt=n;Et=n;St=n;xt=0;Tt=1;Nt=n;Ct=n;kt="down";Lt=-1;Ot=0;Mt=0;_t=false;Dt=n;Pt=n;Ht=n;Bt=n;jt=n;Ft=0;It=n;qt=false;Rt=0;Ut=n};var J=function(){var r;var i;var o;var p;var d;var v;var m;var g;var y;var b;var w;var E;ot(u,[f,l,c,h].join(" "),function(e){var u=e.changedTouches[0];p=e.target;while(p.nodeType===3){p=p.parentNode}d=u.clientY;v=u.clientX;b=e.timeStamp;if(!O.test(p.tagName)){e.preventDefault()}switch(e.type){case f:if(r){r.blur()}yt.stopAnimateTo();r=p;i=m=d;o=v;y=b;break;case l:if(O.test(p.tagName)&&t.activeElement!==p){e.preventDefault()}g=d-m;E=b-w;yt.setScrollTop(Rt-g,true);m=d;w=b;break;default:case c:case h:var a=i-d;var S=o-v;var x=S*S+a*a;if(x<49){if(!O.test(r.tagName)){r.focus();var T=t.createEvent("MouseEvents");T.initMouseEvent("click",true,true,e.view,1,u.screenX,u.screenY,u.clientX,u.clientY,e.ctrlKey,e.altKey,e.shiftKey,e.metaKey,0,null);r.dispatchEvent(T)}return}r=n;var N=g/E;N=s.max(s.min(N,3),-3);var C=s.abs(N/Ct);var k=N*C+.5*Ct*C*C;var L=yt.getScrollTop()-k;var A=0;if(L>xt){A=(xt-L)/k;L=xt}else if(L<0){A=-L/k;L=0}C=C*(1-A);yt.animateTo(L+.5|0,{easing:"outCubic",duration:C});break}});e.scrollTo(0,0);u.style.overflow=a.style.overflow="hidden"};var K=function(){var e=u.clientHeight;var t=lt();var n;var r;var i;var o;var a;var f;var l;var c;var h;var p;var d;c=0;h=bt.length;for(;c<h;c++){n=bt[c];r=n.element;i=n.anchorTarget;o=n.keyFrames;a=0;f=o.length;for(;a<f;a++){l=o[a];p=l.offset;d=t[l.constant]||0;l.frame=p;if(l.isPercentage){p=p*e;l.frame=p}if(l.mode==="relative"){it(r);l.frame=yt.relativeToAbsolute(i,l.anchors[0],l.anchors[1])-p;it(r,true)}l.frame+=d;if(St){if(!l.isEnd&&l.frame>xt){xt=l.frame}}}}xt=s.max(xt,ct());c=0;h=bt.length;for(;c<h;c++){n=bt[c];o=n.keyFrames;a=0;f=o.length;for(;a<f;a++){l=o[a];d=t[l.constant]||0;if(l.isEnd){l.frame=xt-l.offset+d}}n.keyFrames.sort(gt)}};var Q=function(e,t){var n=0;var s=bt.length;for(;n<s;n++){var o=bt[n];var u=o.element;var a=o.smoothScrolling?e:t;var f=o.keyFrames;var l=f[0].frame;var c=f[f.length-1].frame;var h=a<l;var g=a>c;var y=f[h?0:f.length-1];var b;var w;if(h||g){if(h&&o.edge===-1||g&&o.edge===1){continue}pt(u,[h?d:m],[d,v,m]);o.edge=h?-1:1;switch(o.edgeStrategy){case"reset":it(u);continue;case"ease":a=y.frame;break;default:case"set":var E=y.props;for(b in E){if(i.call(E,b)){w=rt(E[b].value);r.setStyle(u,b,w)}}continue}}else{if(o.edge!==0){pt(u,[p,v],[d,m]);o.edge=0}}var S=0;var x=f.length-1;for(;S<x;S++){if(a>=f[S].frame&&a<=f[S+1].frame){var T=f[S];var N=f[S+1];for(b in T.props){if(i.call(T.props,b)){var C=(a-T.frame)/(N.frame-T.frame);C=T.props[b].easing(C);w=nt(T.props[b].value,N.props[b].value,C);w=rt(w);r.setStyle(u,b,w)}}break}}}};var G=function(){if(_t){_t=false;ft()}var e=yt.getScrollTop();var t;var i=mt();var s;if(Dt){if(i>=Dt.endTime){e=Dt.targetTop;t=Dt.done;Dt=n}else{s=Dt.easing((i-Dt.startTime)/Dt.duration);e=Dt.startTop+s*Dt.topDiff|0}yt.setScrollTop(e,true)}else if(!jt){var o=Bt.targetTop-e;if(o){Bt={startTop:Lt,topDiff:e-Lt,targetTop:e,startTime:At,endTime:At+Ht}}if(i<=Bt.endTime){s=V.sqrt((i-Bt.startTime)/Ht);e=Bt.startTop+s*Bt.topDiff|0}}if(qt&&wt){r.setStyle(wt,"transform","translate(0, "+ -Rt+"px) "+Ut)}if(jt||Lt!==e){kt=e>Lt?"down":e<Lt?"up":kt;jt=false;var u={curTop:e,lastTop:Lt,maxTop:xt,direction:kt};var a=Et.beforerender&&Et.beforerender.call(yt,u);if(a!==false){Q(e,yt.getScrollTop());Lt=e;if(Et.render){Et.render.call(yt,u)}}if(t){t.call(yt,false)}}At=i};var Y=function(e){var t=0;var n=e.keyFrames.length;for(;t<n;t++){var r=e.keyFrames[t];var i;var s;var o;var u={};var a;while((a=D.exec(r.props))!==null){o=a[1];s=a[2];i=o.match(P);if(i!==null){o=i[1];i=i[2]}else{i=E}s=s.indexOf("!")?Z(s):[s.slice(1)];u[o]={value:s,easing:V[i]}}r.props=u}};var Z=function(e){var t=[];I.lastIndex=0;e=e.replace(I,function(e){return e.replace(j,function(e){return e/255*100+"%"})});if(U){q.lastIndex=0;e=e.replace(q,function(e){return U+e})}e=e.replace(j,function(e){t.push(+e);return"{?}"});t.unshift(e);return t};var et=function(e){var t={};var n;var r;n=0;r=e.keyFrames.length;for(;n<r;n++){tt(e.keyFrames[n],t)}t={};n=e.keyFrames.length-1;for(;n>=0;n--){tt(e.keyFrames[n],t)}};var tt=function(e,t){var n;for(n in t){if(!i.call(e.props,n)){e.props[n]=t[n]}}for(n in e.props){t[n]=e.props[n]}};var nt=function(e,t,n){var r;var i=e.length;if(i!==t.length){throw"Can't interpolate between \""+e[0]+'" and "'+t[0]+'"'}var s=[e[0]];r=1;for(;r<i;r++){s[r]=e[r]+(t[r]-e[r])*n}return s};var rt=function(e){var t=1;F.lastIndex=0;return e[0].replace(F,function(){return e[t++]})};var it=function(e,t){e=[].concat(e);var n;var r;var i=0;var s=e.length;for(;i<s;i++){r=e[i];n=bt[r[A]];if(!n){continue}if(t){r.style.cssText=n.dirtyStyleAttr;pt(r,n.dirtyClassAttr)}else{n.dirtyStyleAttr=r.style.cssText;n.dirtyClassAttr=ht(r);r.style.cssText=n.styleAttr;pt(r,n.classAttr)}}};var st=function(){Ut="translateZ(0)";r.setStyle(wt,"transform",Ut);var e=o(wt);var t=e.getPropertyValue("transform");var n=e.getPropertyValue(U+"transform");var i=t&&t!=="none"||n&&n!=="none";if(!i){Ut=""}};r.setStyle=function(e,t,n){var r=e.style;t=t.replace(H,B).replace("-","");if(t==="zIndex"){if(isNaN(n)){r[t]=n}else{r[t]=""+(n|0)}}else if(t==="float"){r.styleFloat=r.cssFloat=n}else{try{if(R){r[R+t.slice(0,1).toUpperCase()+t.slice(1)]=n}r[t]=n}catch(i){}}};var ot=r.addEvent=function(t,n,r){var i=function(t){t=t||e.event;if(!t.target){t.target=t.srcElement}if(!t.preventDefault){t.preventDefault=function(){t.returnValue=false;t.defaultPrevented=true}}return r.call(this,t)};n=n.split(" ");var s;var o=0;var u=n.length;for(;o<u;o++){s=n[o];if(t.addEventListener){t.addEventListener(s,r,false)}else{t.attachEvent("on"+s,i)}zt.push({element:t,name:s,listener:r})}};var ut=r.removeEvent=function(e,t,n){t=t.split(" ");var r=0;var i=t.length;for(;r<i;r++){if(e.removeEventListener){e.removeEventListener(t[r],n,false)}else{e.detachEvent("on"+t[r],n)}}};var at=function(){var e;var t=0;var n=zt.length;for(;t<n;t++){e=zt[t];ut(e.element,e.name,e.listener)}zt=[]};var ft=function(){var e=yt.getScrollTop();xt=0;if(St&&!qt){a.style.height="auto"}K();if(St&&!qt){a.style.height=xt+u.clientHeight+"px"}if(qt){yt.setScrollTop(s.min(yt.getScrollTop(),xt))}else{yt.setScrollTop(e,true)}jt=true};var lt=function(){var e=u.clientHeight;var t={};var n;var r;for(n in Nt){r=Nt[n];if(typeof r==="function"){r=r.call(yt)}else if(/p$/.test(r)){r=r.slice(0,-1)/100*e}t[n]=r}return t};var ct=function(){var e=wt&&wt.offsetHeight||0;var t=s.max(e,a.scrollHeight,a.offsetHeight,u.scrollHeight,u.offsetHeight,u.clientHeight);return t-u.clientHeight};var ht=function(t){var n="className";if(e.SVGElement&&t instanceof e.SVGElement){t=t[n];n="baseVal"}return t[n]};var pt=function(t,r,i){var s="className";if(e.SVGElement&&t instanceof e.SVGElement){t=t[s];s="baseVal"}if(i===n){t[s]=r;return}var o=t[s];var u=0;var a=i.length;for(;u<a;u++){o=vt(o).replace(vt(i[u])," ")}o=dt(o);var f=0;var l=r.length;for(;f<l;f++){if(vt(o).indexOf(vt(r[f]))===-1){o+=" "+r[f]}}t[s]=dt(o)};var dt=function(e){return e.replace(M,"")};var vt=function(e){return" "+e+" "};var mt=Date.now||function(){return+(new Date)};var gt=function(e,t){return e.frame-t.frame};var yt;var bt;var wt;var Et;var St;var xt=0;var Tt=1;var Nt;var Ct;var kt="down";var Lt=-1;var At=mt();var Ot=0;var Mt=0;var _t=false;var Dt;var Pt;var Ht;var Bt;var jt;var Ft=0;var It;var qt=false;var Rt=0;var Ut;var zt=[];var Wt;if(typeof define==="function"&&define.amd){define("skrollr",function(){return r})}else{e.skrollr=r}})(window,document)