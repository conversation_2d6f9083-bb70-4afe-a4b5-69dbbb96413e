<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_testimonial' );
function everest_testimonial() {
Block::make( __( 'Xbees Testimonial' ) )
	->add_fields( array(
        Field::make( 'text', 'title', __( 'Title' ) ),
        Field::make( 'text', 'subtext', __( 'Subtitle' ) ),
        ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $title = !empty($fields['title']) ? $fields['title'] : 'Our Values';
        $subtext = !empty($fields['subtext']) ? $fields['subtext'] : 'A Strategic Partnership, Where Innovation Meets Guidance';
        // WP_Query arguments
        $args = array(
            'post_type' => 'everest-testimonial',
            'posts_per_page' => 6, // -1 to retrieve all posts, you can adjust this number as needed
            'post_status' => 'publish', // Retrieve only published posts
            // You can add more parameters as needed, like 'orderby', 'order', 'category', 'tag', etc.
        );
        // The Query
        $query = new WP_Query( $args );
      ?>
<!--Start Testimonial Style1 Area-->
<section class="testimonial-style1-area">
    <div class="container">
        <div class="row">
            <div class="col-xl-12">
                <div class="testimonial-style1__title">
                    <div class="sec-title">
                        <h2><?php echo $title; ?></h2>
                        <div class="sub-title">
                            <p><?php echo $subtext; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="testimonial-style1-content">
                    <div class="testimonial-carousel owl-theme owl-carousel">
                    <?php 
                      // The Loop
                        if ( $query->have_posts() ) {
                            while ( $query->have_posts() ) {
                                $query->the_post(); 
                                $client_name = carbon_get_post_meta(get_the_ID(), 'client-name');
                                
                    ?>
                        <!--Start Single Testimonial Style1-->
                        <div class="single-testimonial-style1 slide">
                            <div class="border-box"></div>
                            <div class="top-box">
                                <div class="quote-iocn-box">
                                    <span class="icon-quote"></span>
                                </div>
                                <div class="inner">
                                    <div class="img-box">
                                        <?php the_post_thumbnail('full', array('class' => 'w-100')); ?>
                                    </div>
                                    <div class="rating-box">
                                        <ul>
                                            <li>
                                                <span class="icon-favourite">
                                                    <span class="path1"></span><span class="path2"></span>
                                                </span>
                                            </li>
                                            <li>
                                                <span class="icon-favourite">
                                                    <span class="path1"></span><span class="path2"></span>
                                                </span>
                                            </li>
                                            <li>
                                                <span class="icon-favourite">
                                                    <span class="path1"></span><span class="path2"></span>
                                                </span>
                                            </li>
                                            <li>
                                                <span class="icon-favourite">
                                                    <span class="path1"></span><span class="path2"></span>
                                                </span>
                                            </li>
                                            <li>
                                                <span class="icon-favourite">
                                                    <span class="path1"></span><span class="path2"></span>
                                                </span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="text">
                                    <?php the_content( ); ?>
                                </div>
                            </div>
                            <div class="client-name">
                                <h3><?php the_title(); ?></h3>
                            </div>
                        </div>
                        <!--End Single Testimonial Style1-->

                    <?php } ?>
                    <?php
                    } else {
                        // no posts found
                        echo 'No testimonial found.';
                    }
                    
                    // Restore original post data
                    wp_reset_postdata(); 
                    ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--End Testimonial Style1 Area-->
<?php
	} );
}