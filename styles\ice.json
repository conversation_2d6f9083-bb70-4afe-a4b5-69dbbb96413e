{"$schema": "https://schemas.wp.org/trunk/theme.json", "version": 2, "title": "Ice", "settings": {"color": {"gradients": [{"slug": "gradient-1", "gradient": "linear-gradient(to bottom, #cbd9e1 0%, #EBEBEF 100%)", "name": "Vertical azure to ice"}, {"slug": "gradient-2", "gradient": "linear-gradient(to bottom, #466577 0%, #EBEBEF 100%)", "name": "Vertical slate to ice"}, {"slug": "gradient-3", "gradient": "linear-gradient(to bottom, #37505d 0%, #EBEBEF 100%)", "name": "Vertical ocean to ice"}, {"slug": "gradient-4", "gradient": "linear-gradient(to bottom, #1C2930 0%, #EBEBEF 100%)", "name": "Vertical ink to ice"}, {"slug": "gradient-5", "gradient": "linear-gradient(to bottom, #37505d 0%, #466577 100%)", "name": "Vertical ocean to slate"}, {"slug": "gradient-6", "gradient": "linear-gradient(to bottom, #1C2930 0%, #37505d 100%)", "name": "Vertical ink to ocean"}, {"slug": "gradient-7", "gradient": "linear-gradient(to bottom, #EBEBEF 50%, #cbd9e1 50%)", "name": "Vertical hard ice to azure"}, {"slug": "gradient-8", "gradient": "linear-gradient(to bottom, #466577 50%, #EBEBEF 50%)", "name": "Vertical hard slate to ice"}, {"slug": "gradient-9", "gradient": "linear-gradient(to bottom, #37505d 50%, #EBEBEF 50%)", "name": "Vertical hard ocean to ice"}, {"slug": "gradient-10", "gradient": "linear-gradient(to bottom, #1C2930 50%, #EBEBEF 50%)", "name": "Vertical hard ink to ice"}, {"slug": "gradient-11", "gradient": "linear-gradient(to bottom, #37505d 50%, #466577 50%)", "name": "Vertical hard ocean to slate"}, {"slug": "gradient-12", "gradient": "linear-gradient(to bottom, #1C2930 50%, #37505d 50%)", "name": "Vertical hard ink to ocean"}], "palette": [{"color": "#EBEBEF", "name": "Base", "slug": "base"}, {"color": "#DCE0E6", "name": "Base / Two", "slug": "base-2"}, {"color": "#1C2930", "name": "Contrast", "slug": "contrast"}, {"color": "#37505d", "name": "Contrast / Two", "slug": "contrast-2"}, {"color": "#96A5B2", "name": "Contrast / Three", "slug": "contrast-3"}]}, "typography": {"fontFamilies": [{"fontFace": [{"fontFamily": "Inter", "fontStretch": "normal", "fontStyle": "normal", "fontWeight": "300 900", "src": ["file:./assets/fonts/inter/Inter-VariableFont_slnt,wght.woff2"]}], "fontFamily": "\"Inter\", sans-serif", "name": "Inter", "slug": "heading"}, {"fontFace": [{"fontFamily": "<PERSON><PERSON>", "fontStyle": "normal", "fontWeight": "100 900", "src": ["file:./assets/fonts/jost/Jost-VariableFont_wght.woff2"]}, {"fontFamily": "<PERSON><PERSON>", "fontStyle": "italic", "fontWeight": "100 900", "src": ["file:./assets/fonts/jost/Jost-Italic-VariableFont_wght.woff2"]}], "fontFamily": "\"Jost\", sans-serif", "name": "<PERSON><PERSON>", "slug": "body"}, {"fontFamily": "-apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif", "name": "System Sans-serif", "slug": "system-sans-serif"}, {"fontFamily": "Iowan Old Style, Apple Garamond, Baskerville, Times New Roman, Droid Serif, Times, Source Serif Pro, serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol", "name": "System Serif", "slug": "system-serif"}], "fontSizes": [{"fluid": false, "name": "Small", "size": "1rem", "slug": "small"}, {"fluid": false, "name": "Medium", "size": "1.2rem", "slug": "medium"}, {"fluid": {"min": "1.5rem", "max": "2rem"}, "name": "Large", "size": "2rem", "slug": "large"}, {"fluid": {"min": "2rem", "max": "2.65rem"}, "name": "Extra Large", "size": "2.65rem", "slug": "x-large"}, {"fluid": {"min": "2.65rem", "max": "3.5rem"}, "name": "Extra Extra Large", "size": "3.5rem", "slug": "xx-large"}]}}, "styles": {"blocks": {"core/button": {"variations": {"outline": {"spacing": {"padding": {"bottom": "calc(1rem - 1px)", "left": "calc(2.2rem - 1px)", "right": "calc(2.2rem - 1px)", "top": "calc(1rem - 1px)"}}, "border": {"width": "1px"}}}}, "core/pullquote": {"typography": {"fontSize": "var(--wp--preset--font-size--large)", "fontStyle": "normal", "fontWeight": "normal", "lineHeight": "1.2"}}, "core/quote": {"typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "var(--wp--preset--font-size--large)", "fontStyle": "normal"}, "variations": {"plain": {"typography": {"fontStyle": "normal", "fontWeight": "400"}}}}, "core/site-title": {"typography": {"fontWeight": "400"}}}, "elements": {"button": {"border": {"radius": "4px", "color": "var(--wp--preset--color--contrast-2)"}, "color": {"background": "var(--wp--preset--color--contrast-2)", "text": "var(--wp--preset--color--white)"}, "spacing": {"padding": {"bottom": "1rem", "left": "2.2rem", "right": "2.2rem", "top": "1rem"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "0.75rem", "fontStyle": "normal", "textTransform": "uppercase", "letterSpacing": "0.1rem"}, ":hover": {"color": {"background": "var(--wp--preset--color--contrast)"}, "border": {"color": "var(--wp--preset--color--contrast)"}}}, "heading": {"typography": {"fontWeight": "normal", "letterSpacing": "0"}}}}}