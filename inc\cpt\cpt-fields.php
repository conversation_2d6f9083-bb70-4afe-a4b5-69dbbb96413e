<?php 
use Carbon_Fields\Container;
use Carbon_Fields\Field;

add_action( 'carbon_fields_register_fields', 'courses_post_meta' );
function courses_post_meta(){
    // Display container on Books CPT
    Container::make( 'post_meta', __( 'Courses Option' ) )
        ->where( 'post_type', '=', 'everest-courses' )
        ->add_tab( __( 'Description' ), array( 
            Field::make( 'rich_text', 'description', __( 'Course Description' ) ),

        ) ) 
        ->add_tab( __( 'Course excerpt' ), array( 
                Field::make( 'rich_text', 'excerpt', __( 'Course excerpt' ) ),
        ) );
        // ) );
        // ->add_tab( __( 'Course Overview' ), array( 
        //     Field::make( 'rich_text', 'overview', __( 'Course Overview' ) ),
        // ) )
        // ->add_tab( __( 'Course Faq\'s' ), array( 
        //     Field::make( 'complex', 'faq', __( 'Course Faq\'s' ) )
        //     ->add_fields( array(
        //         Field::make( 'text', 'title', __( 'Faq Title' ) ),
        //         Field::make( 'textarea', 'content', __( 'faq content' ) ),
        //      ) )
        // ) );

    Container::make( 'term_meta', __( 'Courses Category Data' ) )
        ->where( 'term_taxonomy', '=', 'courses_category' )
        ->add_fields( array( 
            Field::make( 'image', 'cat_image', __( 'Courses Category Image' ) )
                ->set_value_type( 'url' )
         ) );    

}

add_action( 'carbon_fields_register_fields', 'everest_eventes_post_meta' );
function everest_eventes_post_meta(){
    // Display container on Books CPT
    Container::make( 'post_meta', __( 'Event Details' ) )
        ->where( 'post_type', '=', 'everest-eventes' )
        ->add_fields( array( 
            Field::make( 'date', 'date', __( 'Eventes date' ) ),
            Field::make( 'date_time', 'start_time', __( 'Eventes start time' ) )
            ->set_attribute( 'placeholder', 'Date and time of event start' )
            ->set_storage_format( 'H:i a' ),
            Field::make( 'date_time', 'end_time', __( 'Eventes end time' ) )
            ->set_attribute( 'placeholder', 'Date and time of event end' )
            ->set_storage_format( 'H:i a' ),
            Field::make( 'text', 'location', __( 'Eventes location' ) ),
            Field::make( 'text', 'organizer', __( 'Eventes Organizer' ) ),
    ) );
}