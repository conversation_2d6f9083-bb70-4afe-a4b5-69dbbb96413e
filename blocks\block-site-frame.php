<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_site_frame' );
function everest_site_frame() {
Block::make( __( 'Xbees Site Frame' ) )
	->add_tab( __( 'Header' ), array(
        
        Field::make( 'select', 'header_style', __( 'Header Style' ) )
        ->set_options( array(
            '1' => __( 'Header style 1' ),
            '2' => __( 'Header style 2' ),
            // '3' => __( 'Header style 3' ),
        ) ),
        Field::make( 'image', 'logo', __( 'Site Logo' ) )
        ->set_value_type( 'url' ),
        Field::make( 'image', 'logo_fixed', __( 'Site Logo fixed' ) )
        ->set_value_type( 'url' ),
        Field::make( 'image', 'logo_mobile', __( 'Site Logo Mobile' ) )
        ->set_value_type( 'url' ),
		Field::make( 'text', 'email', __( 'Email' ) ),
		Field::make( 'text', 'adress', __( 'Adress' ) ),
		Field::make( 'text', 'btn-text', __( 'Button Text' ) ),
		Field::make( 'text', 'btn-url', __( 'Button Url' ) ),
        Field::make( 'text', 'facebook', __( 'Facebook Url' ) )
        ->set_default_value( '#' ),
        Field::make( 'text', 'youtube', __( 'Youtube Url' ) )
        ->set_default_value( '#' ),
        Field::make( 'text', 'linkedin', __( 'linkedin Url' ) )
        ->set_default_value( '#' ),
        Field::make( 'text', 'instagram', __( 'instagram Url' ) )
        ->set_default_value( 'https://www.instagram.com/everestbusinessschool?igsh=eGluajExaTZya3N1' ),
        Field::make( 'text', 'tiktok', __( 'tiktok Url' ) )
        ->set_default_value( 'https://www.tiktok.com/@everestbusinessschool' ),
        Field::make( 'text', 'whatsapp', __( 'whatsapp Url' ) )
        ->set_default_value( '#' ),
        
        ) )
    ->add_tab( __( 'Footer' ), array(

        // Field::make( 'text', 'slogantitle', __( 'book consulting Title' ) ),
        // Field::make( 'text', 'sloganbtn_title', __( 'consulting Button Title' ) ),
        // Field::make( 'text', 'sloganbtn_url', __( 'consulting Button Url' ) ),

        Field::make( 'image', 'logo_footer', __( 'Site Footer Logo' ) )
        ->set_value_type( 'url' ),
		Field::make( 'text', 'company_title', __( 'Our Campus Title' ) ),
        Field::make( 'textarea', 'company_content', __( 'Our Campus Content' ) ),
        // Field::make( 'text', 'company_map', __( 'Campus Map Url' ) ),
        // Field::make( 'text', 'company_consulturl', __( 'Request for Consult Url' ) ),
        Field::make( 'text', 'footer_menu', __( 'Footer Menu 1 title' ) ),
        Field::make( 'text', 'footer_menu2', __( 'Footer Menu 2 title' ) ),
        Field::make( 'text', 'footer_adress', __( 'Footer Adress' ) ),
        Field::make( 'complex', 'phone_footer', __( 'Pone Number' ) )
        ->add_fields( array(
                Field::make( 'text', 'phone', __( 'Pone Number' ) ),
        )),
        // Field::make( 'text', 'footer_phone', __( 'Get in touch Phone' ) ),
        // Field::make( 'text', 'footer_email', __( 'Get in touch Email' ) ),
        Field::make( 'textarea', 'copyright', __( 'Copyright' ) ),
    ) )
    // ->add_tab( __( 'Style' ), array(
    //     Field::make( 'text', 'base_color', __( 'Notification Email' ) ),
    //     Field::make( 'text', 'two_color', __( 'Phone Number' ) ),
    // ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    ->set_inner_blocks( true )
	->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $email = !empty($fields['email']) ? $fields['email'] : ' <EMAIL>';
        $adress = !empty($fields['adress']) ? $fields['adress'] : ' 7 Bell Yard, London';
        $btn_text = !empty($fields['btn-text']) ? $fields['btn-text'] : 'Get A Quote';
        $btn_url = !empty($fields['btn-url']) ? $fields['btn-url'] : '#';
        $phone = !empty($fields['phone']) ? $fields['phone'] : '(+2) 0120 7777 289';
        $logo = !empty($fields['logo']) ? $fields['logo'] : get_stylesheet_directory_uri() . '/assets/images/resources/logo-light.png';
        $logo_fixed = !empty($fields['logo_fixed']) ? $fields['logo_fixed'] : get_stylesheet_directory_uri() . '/assets/images/resources/logo-dark.png';
        $logo_mobile = !empty($fields['logo_mobile']) ? $fields['logo_mobile'] : get_stylesheet_directory_uri() . '/assets/images/resources/logo-light.png';
        $logo_footer = !empty($fields['logo_footer']) ? $fields['logo_footer'] : get_stylesheet_directory_uri() . '/assets/images/resources/logo-light.png';
        $company_title = !empty($fields['company_title']) ? $fields['company_title'] : 'Our Campus';
        $company_content = !empty($fields['company_content']) ? $fields['company_content'] : 'Trouble that are bound to ensue equal blame belongs to those all fail their duty we like best every pleasure is to welcomed.';
        $company_map = !empty($fields['company_map']) ? $fields['company_map'] : '#';
        $company_ConsultUrl = !empty($fields['company_consulturl']) ? $fields['company_consulturl'] : '#';
        $footer_menu = !empty($fields['footer_menu']) ? $fields['footer_menu'] : 'Executives Programme';
        $footer_menu2 = !empty($fields['footer_menu2']) ? $fields['footer_menu2'] : 'SMEs';
        $footer_adress = !empty($fields['footer_adress']) ? $fields['footer_adress'] : '7 Bell Yard, London';
        $footer_phone = !empty($fields['footer_phone']) ? $fields['footer_phone'] : '(+2) 0120 7777 289';
        $footer_email = !empty($fields['footer_email']) ? $fields['footer_email'] : '<EMAIL>';
        $copyright = !empty($fields['copyright']) ? $fields['copyright'] : 'Copyright &copy; 2024 <a href="/">Everest Business Consulting Ltd</a> . developed by <a href="https://xbees.net/" target="_blank">Xbees.</a> ';


        $slogantitle = !empty($fields['slogantitle']) ? $fields['slogantitle'] : '<span>Register!..</span>  let Let’s talk to joiun our comming courses';
        $sloganbtn_title = !empty($fields['sloganbtn_title']) ? $fields['sloganbtn_title'] : 'Book Free Consultation';
        $sloganbtn_url = !empty($fields['sloganbtn_url']) ? $fields['sloganbtn_url'] : '#';
        
       ?>
<div class="boxed_wrapper ltr">

    <?php if(  ! is_blockEditor() ) : ?>
    <!-- preloader -->
    <div class="loader-wrap">
        <div class="preloader">
            <div id="handle-preloader" class="handle-preloader">
                <div class="animation-preloader">
                    <div class="spinner"></div>
                    <div class="txt-loading">
                        <span data-text-preloader="e" class="letters-loading">
                            e
                        </span>
                        <span data-text-preloader="v" class="letters-loading">
                            v
                        </span>
                        <span data-text-preloader="e" class="letters-loading">
                            e
                        </span>
                        <span data-text-preloader="r" class="letters-loading">
                            r
                        </span>
                        <span data-text-preloader="e" class="letters-loading">
                            e
                        </span>
                        <span data-text-preloader="s" class="letters-loading">
                            s
                        </span>
                        <span data-text-preloader="t" class="letters-loading">
                            t
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- preloader end -->
    <?php endif; ?>

    <?php 
       switch ($fields['header_style']) {
        case '1':
            include __DIR__. '/block-header/header-1.php';
            break;
        case '2':
            include __DIR__. '/block-header/header-2.php';
            break;
        case '3':
            include __DIR__. '/block-header/header-3.php';
            break;
        default:
            include __DIR__. '/block-header/header-1.php';
            break;
       }

       if( ( is_page() && ! is_front_page()) || is_single() || is_archive() ) {
            everest_breadcrumb();
       }

       echo $inner_blocks;
    ?>
    <div class="bottom-parallax-">
        <!--Start footer area -->
        <footer class="footer-area">
            <!--Start Footer-->
            <div class="footer">
                    <div class="row text-right-rtl">

                        <!--Start single footer widget-->
                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 bg-1">
                            <div class="single-footer-widget footer-left">
                                <div class="footer-logo-style1">
                                    <a href="<?php echo get_site_url(); ?>">
                                        <img src="<?php echo $logo_footer; ?>" alt="Awesome Logo" title="">
                                    </a>
                                </div>
                                <div class="single-footer-widget-style5  mt-3">
                                        <div class="footer-social-link-style5">
                                            <ul class="clearfix">
                                                <li>Follow Us:</li>
                                                <li>
                                                    <a href="<?php echo $fields['facebook'] ?>"><i class="fa-brands fa-facebook"></i></a>
                                                </li>
                    
                                                <li>
                                                    <a href="<?php echo $fields['linkedin'] ?>"><i class="fa-brands fa-linkedin"></i></a>
                                                </li>
                                                <li>
                                                    <a href="<?php echo $fields['youtube'] ?>"><i class="fa-brands fa-youtube"></i></a>
                                                </li>
                                                <li>
                                                    <a href="<?php echo $fields['instagram'] ?>"><i class="fa-brands fa-instagram"></i></a>
                                                </li>
                                                <li>
                                                    <a href="<?php echo $fields['twitter'] ?? 'https://twitter.com/Everestbusines3'; ?>"><i class="fa-brands fa-twitter"></i></a>
                                                </li>
                                                <li>
                                                    <a href="<?php echo $fields['tiktok'] ?? 'https://www.tiktok.com/@everestbusinessschool'; ?>">
                                                        <i class="fa-brands fa-tiktok"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="<?php echo $fields['whatsapp'] ?? 'https://wa.me/+201207777289'; ?>">
                                                    <i class="fa-brands fa-whatsapp"></i>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                <div class="our-company-info">
                                    <ul>
                                        <li>
                                            <div class="icon">
                                                <span class="icon-map"></span>
                                            </div>
                                            <div class="text">
                                                <a href="#">Adress : <?php echo $footer_adress; ?></a>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="icon">
                                                <span class="icon-phone"></span>
                                                
                                            </div>
                                            <div class="text">Phone Number :</div>
                                            <?php if ( isset( $fields['phone_footer'] ) && is_array( $fields['phone_footer'] ) ) { 
                                                foreach ( $fields['phone_footer'] as $phone_field ) {
                                                    $phone = $phone_field['phone'];
                                                    $sanitized_phone = preg_replace( '/[^0-9\+]/', '', $phone );
                                                ?>
                                            <div class="text">
                                            <a class="ml-3" href="tel:<?php echo esc_attr( $sanitized_phone ); ?>">
                                             <?php echo esc_html( $phone ); ?> </a>
                                            </div>
                                            <?php } }  ?>
                                        </li>
                                    </ul>
                                    <div class="copyright mt-3">
                                        <p><?php echo $copyright; ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--End single footer widget-->
                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 bg-2">
                            <div class="row">
                                <!--Start single footer widget-->
                                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-5">
                                    <div class="single-footer-widget single-footer-widget--link-box marbtm50">
                                        <div class="title">
                                            <h3>Executive Programmes</h3>
                                        </div>
                                        <div class="footer-widget-links">
                                            <?php 
                                            if ( has_nav_menu( 'footer-1' ) ) {
                                                wp_nav_menu( array(
                                                    'theme_location' => 'footer-1',
                                                    'menu_class'     => '',
                                                ) );
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <!--End single footer widget-->
        
                                <!--Start single footer widget-->
                                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-5">
                                    <div class="single-footer-widget single-footer-widget--link-box">
                                        <div class="title">
                                            <h3>Sustainability Programmes</h3>
                                        </div>
                                        <div class="footer-widget-links">
                                            <?php 
                                            if ( has_nav_menu( 'footer-2' ) ) {
                                                wp_nav_menu( array(
                                                    'theme_location' => 'footer-2',
                                                    'menu_class'     => '',
                                                ) );
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <!--End single footer widget-->
        
                                <!--Start single footer widget-->
                                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-5">
                                    <div class="single-footer-widget pdtop50">
                                        <div class="title">
                                            <h3>Risk Management Programmes</h3>
                                        </div>
                                        <div class="footer-widget-links">
                                            <?php 
                                            if ( has_nav_menu( 'footer-3' ) ) {
                                                wp_nav_menu( array(
                                                    'theme_location' => 'footer-3',
                                                    'menu_class'     => '',
                                                ) );
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <!--End single footer widget-->
                                <!--Start single footer widget-->
                                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-5">
                                    <div class="single-footer-widget pdtop50">
                                        <div class="title">
                                            <h3>Medical Programmes</h3>
                                        </div>
                                        <div class="footer-widget-links">
                                            <?php 
                                            if ( has_nav_menu( 'footer-4' ) ) {
                                                wp_nav_menu( array(
                                                    'theme_location' => 'footer-4',
                                                    'menu_class'     => '',
                                                ) );
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <!--End single footer widget-->
                            </div>
                        </div>
                    </div>
            </div>
            <!--End Footer-->
        </footer>
        <!--End footer area-->
    </div>


    <button class="scroll-top scroll-to-target" data-target="html">
        <span class="icon-top"></span>
    </button>
</div>
<?php
	} );
}