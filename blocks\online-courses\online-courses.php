<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_courses' );
function everest_block_courses() {
Block::make( __( 'Xbees Courses' ) )
	->add_fields( array(
        Field::make( 'select', 'course_style', __( 'Courses Style' ) )
        ->set_options( array(
            '1' => __( 'style 1' ),
            '2' => __( 'style 2' ),
            '3' => __( 'style 3' ),
        ) ),
        Field::make( 'text', 'title', __( 'Title' ) ),
        Field::make( 'text', 'subtext', __( 'Subtitle' ) ),
        Field::make('association', 'selected_categories', __('Select Categories'))
                ->set_types(array(
                    array(
                        'type' => 'term',
                        'taxonomy' => 'courses_category',
                    ),
                    array(
                        'type' => 'post',
                        'post_type' => 'page',
                    ),
                ))
        ))
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $title = !empty($fields['title']) ? $fields['title'] : 'Most Advanced<br> <span>Courses Classes</span> In Online';
        $subtext = !empty($fields['subtext']) ? $fields['subtext'] : '<span>Online Courses</span> Built for Everyone';
        $image = !empty($fields['image']) ? $fields['image'] : 'https://st.ourhtmldemo.com/new/educamb/assets/images/resources/teaching-img.jpg';
        
        $selected_categories = $fields['selected_categories'];
            switch ($fields['course_style']) {
                case '1':
                    include 'cat-course-1.php';
                    break;
                case '2':
                    include 'cat-course-2.php';
                    break;
                case '3':
                    include 'cat-course-3.php';
                    break;
                default:
                    include 'cat-course-1.php';
                    break;
               }

	} );
}