/* ==========================================================================
   Field
   ========================================================================== */

.cf-field,
.cf-field__head,
.cf-field__body {
	box-sizing: border-box;
	flex: 1 1 100%;
}

.cf-field {
	@media (max-width: 1024px) {
		flex-basis: 100% !important;
	}

	// Show only on block preview iframe
	&.cf-block-preview {
		display: none;
	}
}

.cf-field__body {
	.cf-rich-text & {
		box-sizing: content-box;
	}

	.cf-complex--tabbed-vertical > & {
		display: flex;
		align-items: flex-start;
	}
}

.cf-field__label {
	display: block;

	.cf-html &,
	.cf-separator &,
	.cf-block-preview & {
		display: none;
	}
}

.cf-field__asterisk {
	color: $wp-color-accent-red;
}

.cf-field__error {
	display: block;
	margin-top: $size-base;
	color: $wp-color-accent-red;
}
