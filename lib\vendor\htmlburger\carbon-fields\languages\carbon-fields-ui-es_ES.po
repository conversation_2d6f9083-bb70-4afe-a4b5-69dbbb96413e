msgid ""
msgstr ""
"Content-Type: text/plain; charset=UTF-8\n"
"X-Generator: Poedit 2.4.1\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON> <jorovi<PERSON>@gmail.com>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Transfer-Encoding: 8bit\n"
"Last-Translator: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Language: es\n"

#: packages/blocks/components/block-edit/index.js:214
msgid "Show preview"
msgstr "Mostrar vista previa"

#: packages/blocks/components/block-edit/index.js:215
msgid "Hide preview"
msgstr "Ocultar vista previa"

#: packages/blocks/components/block-edit/index.js:286
msgid "Fields"
msgstr "Campos"

#: packages/blocks/components/not-supported-field/index.js:14
msgid "Field of type '%s' is not supported in Gutenberg."
msgstr "El campo de tipo '%s' no se admite en Gutenberg."

#: packages/blocks/components/server-side-render/index.js:129
msgid "Error loading block: %s"
msgstr "Error cargando bloque: %s"

#: packages/blocks/components/server-side-render/index.js:135
msgid "No results found."
msgstr "No se encontraron resultados."

#: packages/blocks/fields/datetime/index.js:59
#: packages/core/fields/date/index.js:15
#: packages/metaboxes/fields/datetime/index.js:9
msgid "Select Date"
msgstr "Seleccione Fecha"

#: packages/blocks/fields/file/index.js:16
#: packages/metaboxes/fields/file/index.js:11
msgid "Use File"
msgstr "Usar Archivo"

#: packages/blocks/fields/file/index.js:17
#: packages/metaboxes/fields/file/index.js:12
msgid "Select File"
msgstr "Seleccionar Archivo"

#: packages/blocks/fields/image/index.js:11
#: packages/metaboxes/fields/image/index.js:11
msgid "Use Image"
msgstr "Usar Imagen"

#: packages/blocks/fields/image/index.js:12
#: packages/metaboxes/fields/image/index.js:12
msgid "Select Image"
msgstr "Seleccionar Imagen"

#: packages/blocks/fields/media-gallery/index.js:16
#: packages/metaboxes/fields/media-gallery/index.js:16
#, fuzzy
msgid "Use Attachments"
msgstr "Usar Attachment"

#: packages/blocks/fields/media-gallery/index.js:17
#: packages/metaboxes/fields/media-gallery/index.js:17
#, fuzzy
msgid "Select Attachments"
msgstr "Seleccionar Attachments"

#: packages/core/components/no-options/index.js:14
msgid "No options."
msgstr "No hay opciones."

#: packages/core/components/search-input/index.js:30
msgid "Search..."
msgstr "Buscar..."

#: packages/core/fields/association/index.js:168
msgid "Maximum number of items reached (%s items)"
msgstr "Máximo número de ítems alcanzado (%s ítems)"

#: packages/core/fields/association/index.js:266
msgid "Showing %1$d of %2$d results"
msgstr "Mostrando  %1$d de %2$d resultados"

#: packages/core/fields/association/index.js:456
msgid "An error occurred while trying to fetch association options."
msgstr "Un error ocurrió mientras se intentaba cargar las opciones asociadas."

#: packages/core/fields/association/index.js:513
#: packages/core/fields/complex/index.js:428
#: packages/core/hocs/with-validation/required.js:20
msgid "This field is required."
msgstr "Este campo es obligatorio."

#: packages/core/fields/association/index.js:517
#, fuzzy
msgid "Minimum number of items not reached (%s items)"
msgstr "No se ha alcanzado el máximo número de ítems aun (%s ítems)"

#: packages/core/fields/color/index.js:92
msgid "Select a color"
msgstr "Selecciona un color"

#: packages/core/fields/complex/group.js:154
msgid "Duplicate"
msgstr "Duplicar"

#: packages/core/fields/complex/group.js:163
msgid "Remove"
msgstr "Eliminar"

#: packages/core/fields/complex/group.js:172
msgid "Collapse"
msgstr "Callapsar"

#: packages/core/fields/complex/index.js:146
#, fuzzy
msgid "Couldn't create the label of group - %s"
msgstr "No se pudo crear  la etiqueta del grupo - %s"

#: packages/core/fields/complex/index.js:344
msgid "There are no entries yet."
msgstr "No hay entradas aun."

#: packages/core/fields/complex/index.js:401
msgid "Expand All"
msgstr "Expandir todos"

#: packages/core/fields/complex/index.js:401
msgid "Collapse All"
msgstr "Colapsar todos"

#: packages/core/fields/complex/index.js:435
msgid "Minimum number of rows not reached (%1$d %2$s)"
msgstr "No se alcanzó el numero mínimo de filas (%1$d %2$s)"

#: packages/core/fields/complex/index.js:82
msgid "Add %s"
msgstr "Agregar %s"

#: packages/core/fields/map/index.js:165
msgid "The address could not be found."
msgstr "No se pudo encontrar la dirección."

#: packages/core/fields/map/index.js:167
msgid "Geocode was not successful for the following reason: "
msgstr "Geocode no fue exitoso por las siguientes razones: "

#: packages/core/fields/map/index.js:185
msgid "Error alert"
msgstr "Alerta de error"

#: packages/core/fields/oembed/index.js:188
msgid "An error occurred while trying to fetch oembed preview."
msgstr "Un error ocurrió mientras se cargaba la previsualización del oembed."

#: packages/core/fields/oembed/index.js:203
msgid "Not Found"
msgstr "No se encontraron resultados"

#: packages/core/fields/rich-text/index.js:103
msgid "Text"
msgstr "Texto"

#: packages/core/fields/rich-text/index.js:99
msgid "Visual"
msgstr ""

#: packages/core/fields/sidebar/index.js:110
msgid "Please enter the name of the new sidebar:"
msgstr "Por favor, ingrese el nombre del nuevo sidebar:"

#: packages/core/fields/sidebar/index.js:128
msgid "An error occurred while trying to create the sidebar."
msgstr "Un error ocurrió mientras se creaba el sidebar."

#: packages/core/fields/sidebar/index.js:58
msgid "Please choose"
msgstr "Por favor seleccione"

#: packages/core/fields/time/index.js:16
msgid "Select Time"
msgstr "Seleccione Fecha"

#: packages/core/hocs/with-conditional-logic/index.js:69
msgid "An unknown field is used in condition - \"%s\""
msgstr ""

#: packages/core/registry/index.js:40
msgid "%1$s type must be a string."
msgstr ""

#: packages/core/registry/index.js:46
msgid "%1$s %2$s is already registered."
msgstr ""

#: packages/core/registry/index.js:54
msgid "The \"component\" param must be a function."
msgstr ""

#: packages/core/registry/index.js:79
msgid "The provided context isn't a valid one. Must be one of - %s ."
msgstr ""

#: packages/core/registry/index.js:89
msgid "%s %s isn't registered."
msgstr ""

#: packages/core/utils/api-fetch.js:19
msgid "An error occured."
msgstr "Ocurrió un error."

#: packages/core/utils/fetch-attachments-data.js:24
msgid "An error occurred while trying to fetch files data."
msgstr "Un error ocurrió mientras se cargaban los datos del archivo."

#: packages/metaboxes/containers/index.js:55
msgid "Could not find DOM element for container \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/conditions/base.js:52
msgid "Unsupported container condition comparison operator used - \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/handler/index.js:55
msgid "Unsupported container condition - \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/handler/index.js:73
msgid "Unsupported container condition relation used - \"%1$s\"."
msgstr ""
