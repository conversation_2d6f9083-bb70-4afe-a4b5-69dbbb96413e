!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=72)}([function(e,t){e.exports=lodash},function(e,t){e.exports=wp.i18n},function(e,t,n){var r=n(20);e.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=wp.hooks},function(e,t){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=wp.data},function(e,t){e.exports=cf.core},function(e,t){function n(t){return e.exports=n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=wp.element},function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(20);function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,r(o.key),o)}}e.exports=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(54);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(18).default,o=n(4);e.exports=function(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(){return e.exports=n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,n.apply(this,arguments)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=wp.components},function(e,t){e.exports=wp.editor},function(e,t){e.exports=wp.blocks},function(e,t){e.exports=cf.vendor.immer},function(e,t){function n(t){return e.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){e.exports={forEach:n(29),fromObs:n(30),fromIter:n(34),fromEvent:n(35),fromPromise:n(36),interval:n(37),map:n(38),scan:n(39),flatten:n(40),take:n(41),skip:n(42),filter:n(43),merge:n(44),concat:n(45),combine:n(46),share:n(47),pipe:n(48)}},function(e,t,n){var r=n(18).default,o=n(28);e.exports=function(e){var t=o(e,"string");return"symbol"===r(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=wp.compose},function(e,t,n){"use strict";function r(e){var t,n=e.Symbol;return"function"==typeof n?n.observable?t=n.observable:(t=n("observable"),n.observable=t):t="@@observable",t}n.d(t,"a",(function(){return r}))},function(e,t,n){var r=n(50),o=n(51),i=n(52),c=n(53);e.exports=function(e){return r(e)||o(e)||i(e)||c()},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=wp.date},function(e,t,n){var r;
/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var i=typeof r;if("string"===i||"number"===i)e.push(r);else if(Array.isArray(r)){if(r.length){var c=o.apply(null,r);c&&e.push(c)}}else if("object"===i){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){e.push(r.toString());continue}for(var l in r)n.call(r,l)&&r[l]&&e.push(l)}}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(r=function(){return o}.apply(t,[]))||(e.exports=r)}()},function(e,t){e.exports=wp.apiFetch},function(e,t,n){var r=n(18).default;e.exports=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=e=>t=>{let n;t(0,(t,r)=>{0===t&&(n=r),1===t&&e(r),1!==t&&0!==t||n(1)})}},function(e,t,n){const r=n(31).default;e.exports=e=>(t,n)=>{if(0!==t)return;let o;n(0,e=>{2===e&&o&&(o.unsubscribe?o.unsubscribe():o())}),e=e[r]?e[r]():e,o=e.subscribe({next:e=>n(1,e),error:e=>n(2,e),complete:()=>n(2)})}},function(e,t,n){"use strict";n.r(t),function(e,r){var o,i=n(23);o="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==e?e:r;var c=Object(i.a)(o);t.default=c}.call(this,n(32),n(33)(e))},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t){e.exports=function(e){if(!e.webpackPolyfill){var t=Object.create(e);t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),Object.defineProperty(t,"exports",{enumerable:!0}),t.webpackPolyfill=1}return t}},function(e,t){e.exports=e=>(t,n)=>{if(0!==t)return;const r="undefined"!=typeof Symbol&&e[Symbol.iterator]?e[Symbol.iterator]():e;let o,i=!1,c=!1,l=!1;n(0,e=>{l||(1===e?(c=!0,i||o&&o.done||function(){for(i=!0;c&&!l;){if(c=!1,o=r.next(),o.done){n(2);break}n(1,o.value)}i=!1}()):2===e&&(l=!0))})}},function(e,t,n){"use strict";n.r(t),t.default=(e,t,n)=>(r,o)=>{if(0!==r)return;let i=!1;const c=e=>{o(1,e)};if(o(0,r=>{if(2===r)if(i=!0,e.removeEventListener)e.removeEventListener(t,c,n);else{if(!e.removeListener)throw new Error("cannot remove listener from node. No method found.");e.removeListener(t,c)}}),!i)if(e.addEventListener)e.addEventListener(t,c,n);else{if(!e.addListener)throw new Error("cannot add listener to node. No method found.");e.addListener(t,c)}}},function(e,t,n){"use strict";n.r(t),t.default=e=>(t,n)=>{if(0!==t)return;let r=!1;e.then(e=>{r||(n(1,e),r||n(2))},(e=new Error)=>{r||n(2,e)}),n(0,e=>{2===e&&(r=!0)})}},function(e,t,n){"use strict";n.r(t),t.default=e=>(t,n)=>{if(0!==t)return;let r=0;const o=setInterval(()=>{n(1,r++)},e);n(0,e=>{2===e&&clearInterval(o)})}},function(e,t){e.exports=e=>t=>(n,r)=>{0===n&&t(0,(t,n)=>{r(t,1===t?e(n):n)})}},function(e,t){e.exports=function(e,t){let n=2===arguments.length;return r=>(o,i)=>{if(0!==o)return;let c=t;r(0,(t,r)=>{1===t?(c=n?e(c,r):(n=!0,r),i(1,c)):i(t,r)})}}},function(e,t,n){"use strict";n.r(t),t.default=e=>(t,n)=>{if(0!==t)return;let r,o;function i(e,t){1===e&&(o||r)(1,t),2===e&&(o&&o(2),r&&r(2))}e(0,(e,t)=>{if(0===e)r=t,n(0,i);else if(1===e){const e=t;o&&o(2),e(0,(e,t)=>{0===e?(o=t,o(1)):1===e?n(1,t):2===e&&t?(r&&r(2),n(2,t)):2===e&&(r?(o=void 0,r(1)):n(2))})}else 2===e&&t?(o&&o(2),n(2,t)):2===e&&(o?r=void 0:n(2))})}},function(e,t){e.exports=e=>t=>(n,r)=>{if(0!==n)return;let o,i,c=0;function l(t,n){2===t?(i=!0,o(t,n)):c<e&&o(t,n)}t(0,(t,n)=>{0===t?(o=n,r(0,l)):1===t?c<e&&(c++,r(t,n),c!==e||i||(i=!0,o(2),r(2))):r(t,n)})}},function(e,t){e.exports=e=>t=>(n,r)=>{if(0!==n)return;let o,i=0;t(0,(t,n)=>{0===t?(o=n,r(t,n)):1===t&&i<e?(i++,o(1)):r(t,n)})}},function(e,t){e.exports=e=>t=>(n,r)=>{if(0!==n)return;let o;t(0,(t,n)=>{0===t?(o=n,r(t,n)):1===t?e(n)?r(t,n):o(1):r(t,n)})}},function(e,t){e.exports=function(...e){return(t,n)=>{if(0!==t)return;const r=e.length,o=new Array(r);let i=0,c=0;const l=e=>{if(0!==e)for(let t=0;t<r;t++)o[t]&&o[t](e)};for(let t=0;t<r;t++)e[t](0,(e,a)=>{0===e?(o[t]=a,1==++i&&n(0,l)):2===e?(o[t]=void 0,++c===r&&n(2)):n(e,a)})}}},function(e,t,n){"use strict";n.r(t);const r={};t.default=(...e)=>(t,n)=>{if(0!==t)return;const o=e.length;if(0===o)return n(0,()=>{}),void n(2);let i,c=0,l=r;const a=(e,t)=>{1===e&&(l=t),i(e,t)};!function t(){c!==o?e[c](0,(e,o)=>{0===e?(i=o,0===c?n(0,a):l!==r&&i(1,l)):2===e&&o?n(2,o):2===e?(c++,t()):n(e,o)}):n(2)}()}},function(e,t){const n={};e.exports=(...e)=>(t,r)=>{if(0!==t)return;const o=e.length;if(0===o)return r(0,()=>{}),r(1,[]),void r(2);let i=o,c=o,l=o;const a=new Array(o),u=new Array(o),s=(e,t)=>{if(0!==e)for(let n=0;n<o;n++)u[n](e,t)};e.forEach((e,t)=>{a[t]=n,e(0,(e,f)=>{if(0===e)u[t]=f,0==--i&&r(0,s);else if(1===e){const e=c?a[t]===n?--c:c:0;if(a[t]=f,0===e){const e=new Array(o);for(let t=0;t<o;++t)e[t]=a[t];r(1,e)}}else 2===e?0==--l&&r(2):r(e,f)})})}},function(e,t,n){"use strict";n.r(t),t.default=e=>{let t,n=[];return function(r,o){if(0!==r)return;n.push(o);const i=(e,r)=>{if(2===e){const e=n.indexOf(o);e>-1&&n.splice(e,1),n.length||t(2)}else t(e,r)};1!==n.length?o(0,i):e(0,(e,r)=>{if(0===e)t=r,o(0,i);else for(let t of n.slice(0))t(e,r);2===e&&(n=[])})}}},function(e,t){e.exports=function(...e){let t=e[0];for(let n=1,r=e.length;n<r;n++)t=e[n](t);return t}},function(e,t,n){},function(e,t,n){var r=n(21);e.exports=function(e){if(Array.isArray(e))return r(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(21);e.exports=function(e,t){if(e){if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t,r){return e.exports=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t,r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){"use strict";n.r(t);var r={};n.r(r),n.d(r,"setupContainerDefinitions",(function(){return K})),n.d(r,"setupFieldDefinitions",(function(){return $}));var o={};n.r(o),n.d(o,"getContainerDefinitionByBlockName",(function(){return W})),n.d(o,"getFieldDefinitionsByBlockName",(function(){return H}));var i=n(2),c=n.n(i),l=n(5),a=n(16),u=n(1),s=n(0),f=n(22),p=n(3),d=n(6),b=function(e,t){return e===t},h=n(19);function m(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Object(s.mapKeys)(e,(function(e,n){return"".concat(Object(s.repeat)("parent.",t)).concat(n)}))}function v(e){return Object(s.startsWith)(e,"cf-")}var y=Object(d.withConditionalLogic)((function(e){return Object(h.pipe)(Object(d.fromSelector)(Object(l.select)("core/block-editor").getBlock,e.blockId),function(e){return void 0===e&&(e=b),function(t){return function(n,r){if(0===n){var o,i,c=!1;t(0,(function(t,n){0===t&&(i=n),1===t?c&&e(o,n)?i(1):(c=!0,o=n,r(1,n)):r(t,n)}))}}}}(),Object(h.map)((function(e){var t;return null==e||null===(t=e.attributes)||void 0===t?void 0:t.data})))}),(function(e,t){var n={};if(Object(s.has)(t,e.field.base_name))n=m(Object(s.omit)(t,[e.field.base_name]));else{var r=e.id.split("__");r.shift();var o=r.shift();r.pop();var i=r.reduce((function(e,t){return v(t)?e:e+1}),0);n=m(n=Object(s.omit)(t,[o]),i+1);for(var c=o;r.length>0;){var l=r.shift(),a=v(l),u=!a;if(a){var f=Object(s.findIndex)(Object(s.get)(t,c),["_id",l]);c="".concat(c,".").concat(f);var p=Object(s.get)(t,c);p=m(p=Object(s.omit)(p,["_id","_type",e.field.base_name]),i),Object(s.assign)(n,p)}u&&(c="".concat(c,".").concat(l),i--)}}return n}));n(49),Object(p.addFilter)("carbon-fields.association.block","carbon-fields/blocks",Object(d.withProps)((function(e){return{hierarchyResolver:function(){var t=Object(l.select)("core/block-editor").getBlock(e.blockId),n=e.id.split("__");n.shift();for(var r=n.shift(),o="data.".concat(r);n.length>0;){var i=n.shift();if(0===i.indexOf("cf-")){var c=Object(s.get)(t.attributes,"".concat(o)),a=Object(s.find)(c,["_id",i]),u=c.indexOf(a);o="".concat(o,".").concat(u),r="".concat(r,"[").concat(u,"]:").concat(a._type,"/")}else o="".concat(o,".").concat(i),r="".concat(r).concat(i)}return r}}})));var O=n(24),g=n.n(O),_=n(9),j=n.n(_),w=n(10),x=n.n(w),k=n(4),E=n.n(k),S=n(11),P=n.n(S),I=n(12),F=n.n(I),C=n(7),G=n.n(C),T=n(17),M=n.n(T),B=n(8),N=(n(55),n(56),Object(d.withFilters)("carbon-fields.field-wrapper.block")(d.Field));var R=function(e){P()(n,e);var t=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=G()(e);if(t){var o=G()(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return F()(this,n)}}(n);function n(){var e;j()(this,n);for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return e=t.call.apply(t,[this].concat(o)),c()(E()(e),"state",{collapsedGroups:e.props.value.reduce((function(t,n){var r=n._id,o=n._type;return Object(s.find)(e.props.field.groups,["name",o]).collapsed?t.concat(r):t}),[])}),c()(E()(e),"handleAddGroup",(function(t,n){var r=e.props,o=r.id,i=r.value,c=r.onChange,l={};l._id=Object(d.uniqueId)(),l._type=t.name,t.fields.reduce((function(e,t){return e[t.base_name]=t.default_value,e}),l),c(o,i.concat(l)),n(l)})),c()(E()(e),"handleCloneGroup",(function(t,n){var r=e.props,o=r.id,i=r.value,c=r.onChange,l=i.indexOf(t),a=Object(s.cloneDeep)(t);a._id=Object(d.uniqueId)(),c(o,M()(i,(function(e){e.splice(l+1,0,a)}))),n(a)})),c()(E()(e),"handleRemoveGroup",(function(t){var n=e.props,r=n.id,o=n.value,i=n.onChange,c=Object(s.findIndex)(o,["_id",t._id]);i(r,M()(o,(function(e){e.splice(c,1)}))),e.setState((function(e){var n=e.collapsedGroups;return{collapsedGroups:Object(s.without)(n,t._id)}}))})),c()(E()(e),"handleToggleGroup",(function(t){e.setState((function(e){var n=e.collapsedGroups;return{collapsedGroups:n=n.indexOf(t)>-1?Object(s.without)(n,t):[].concat(g()(n),[t])}}))})),c()(E()(e),"handleToggleAllGroups",(function(){var t=e.props.value;e.setState((function(e){var n=e.collapsedGroups;return{collapsedGroups:n=n.length!==t.length?t.map((function(e){return e._id})):[]}}))})),c()(E()(e),"handleGroupSetup",(function(t,n){var r=Object(s.get)(Object(s.find)(e.props.field.groups,["name",t._type]),"fields",[]),o=Object(s.omit)(t,["_id","_type"]);return Object(s.assign)({},n,{id:t._id,fields:r,collapsed:e.state.collapsedGroups.indexOf(t._id)>-1,context:"block",values:o})})),c()(E()(e),"handleGroupFieldSetup",(function(t,n,r){var o=e.props.blockId,i="".concat(e.props.id,"__").concat(r.id,"__").concat(t.base_name),c=Object(s.get)(r,"values.".concat(t.base_name));return[N,Object(s.assign)({},n,{key:i,id:i,name:t.base_name,containerId:e.props.containerId,blockId:o,field:t,value:c,onChange:e.handleGroupFieldChange})]})),c()(E()(e),"handleGroupFieldChange",(function(t,n){var r=e.props,o=r.id,i=r.value;(0,r.onChange)(o,M()(i,(function(e){var r=t.split("__"),o=r.pop(),i=Object(s.find)(e,["_id",r.pop()]);Object(s.set)(i,o,n)})))})),e}return x()(n,[{key:"getGroupValues",value:function(){return this.props.value.map((function(e){var t=Object(s.mapKeys)(Object(s.omit)(e,["_id","_type"]),(function(e,t){return t.replace(/\-/g,"_")}));return[e._type,t]}))}},{key:"render",value:function(){var e=this.handleGroupSetup,t=this.handleGroupFieldSetup,n=this.handleAddGroup,r=this.handleCloneGroup,o=this.handleRemoveGroup,i=this.handleToggleGroup,c=this.handleToggleAllGroups,l=this.props,a=l.value;return(0,l.children)({groupValues:this.getGroupValues(),allGroupsAreCollapsed:this.state.collapsedGroups.length===a.length,handleGroupSetup:e,handleGroupFieldSetup:t,handleAddGroup:n,handleCloneGroup:r,handleRemoveGroup:o,handleToggleGroup:i,handleToggleAllGroups:c})}}]),n}(B.Component);Object(p.addFilter)("carbon-fields.complex.block","carbon-fields/blocks",(function(e){return function(t){var n=t.id,r=t.name,o=t.value,i=t.error,c=t.field;return wp.element.createElement(R,t,(function(l){var a=l.groupValues,u=l.allGroupsAreCollapsed,s=l.handleGroupSetup,f=l.handleGroupFieldSetup,p=l.handleAddGroup,d=l.handleCloneGroup,b=l.handleRemoveGroup,h=l.handleToggleGroup,m=l.handleToggleAllGroups;return wp.element.createElement(e,{groupIdKey:"_id",groupFilterKey:"_type",id:n,name:r,value:o,error:i,field:c,groupValues:a,allGroupsAreCollapsed:u,onGroupSetup:s,onGroupFieldSetup:f,onAddGroup:p,onCloneGroup:d,onRemoveGroup:b,onToggleGroup:h,onToggleAllGroups:m,onChange:t.onChange})}))}}));var D=n(13),A=n.n(D),L=n(25);n(57);var U=function(e){P()(n,e);var t=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=G()(e);if(t){var o=G()(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return F()(this,n)}}(n);function n(){var e;j()(this,n);for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return e=t.call.apply(t,[this].concat(o)),c()(E()(e),"handleChange",(function(t,n){var r=e.props,o=r.id,i=r.onChange,c=r.value,l=r.field,a=Object(L.format)(l.storage_format,n);a!==c&&i(o,a)})),e}return x()(n,[{key:"render",value:function(){var e=this.handleChange;return(0,this.props.children)({handleChange:e})}}]),n}(B.Component);Object(p.addFilter)("carbon-fields.date_time.block","carbon-fields/blocks",(function(e){return function(t){return wp.element.createElement(U,t,(function(n){var r=n.handleChange;return wp.element.createElement(e,A()({buttonText:Object(u.__)("Select Date","carbon-fields-ui")},t,{onChange:r}))}))}})),Object(p.addFilter)("carbon-fields.date.block","carbon-fields/blocks",(function(e){return function(t){return wp.element.createElement(U,t,(function(n){var r=n.handleChange;return wp.element.createElement(e,A()({},t,{onChange:r}))}))}})),Object(p.addFilter)("carbon-fields.time.block","carbon-fields/blocks",(function(e){return function(t){return wp.element.createElement(U,t,(function(n){var r=n.handleChange;return wp.element.createElement(e,A()({},t,{onChange:r}))}))}})),n(58),Object(p.addFilter)("carbon-fields.file.block","carbon-fields/blocks",(function(e){return function(t){return wp.element.createElement(e,A()({buttonLabel:Object(u.__)("Select File","carbon-fields-ui"),mediaLibraryButtonLabel:Object(u.__)("Use File","carbon-fields-ui"),mediaLibraryTitle:Object(u.__)("Select File","carbon-fields-ui")},t))}}));var q=function(e){var t=e.type;return wp.element.createElement("em",null,Object(u.sprintf)(Object(u.__)("Field of type '%s' is not supported in Gutenberg.","carbon-fields-ui"),[t]))};Object(p.addFilter)("carbon-fields.footer_scripts.block","carbon-fields/blocks",(function(){return function(e){return wp.element.createElement(q,{type:e.field.type})}})),Object(p.addFilter)("carbon-fields.header_scripts.block","carbon-fields/blocks",(function(){return function(e){return wp.element.createElement(q,{type:e.field.type})}})),Object(p.addFilter)("carbon-fields.hidden.block","carbon-fields/blocks",(function(){return function(e){return wp.element.createElement(q,{type:e.field.type})}})),Object(p.addFilter)("carbon-fields.image.block","carbon-fields/blocks",(function(e){return function(t){return wp.element.createElement(e,A()({buttonLabel:Object(u.__)("Select Image","carbon-fields-ui"),mediaLibraryButtonLabel:Object(u.__)("Use Image","carbon-fields-ui"),mediaLibraryTitle:Object(u.__)("Select Image","carbon-fields-ui")},t))}})),n(59),n(60),n(61),Object(p.addFilter)("carbon-fields.media_gallery.block","carbon-fields/blocks",(function(e){return function(t){return wp.element.createElement(e,A()({buttonLabel:Object(u.__)("Select Attachments","carbon-fields-ui"),mediaLibraryButtonLabel:Object(u.__)("Use Attachments","carbon-fields-ui"),mediaLibraryTitle:Object(u.__)("Select Attachments","carbon-fields-ui")},t))}})),n(62),n(63),n(64),n(65),n(66),Object(p.addFilter)("carbon-fields.sidebar.block","carbon-fields/blocks",(function(){return function(e){return wp.element.createElement(q,{type:e.field.type})}})),n(67),n(68),n(69),n(70),Object(p.addFilter)("carbon-fields.field-edit.block","carbon-fields/blocks",Object(f.compose)(y,Object(l.withDispatch)((function(e){if(!Object(s.isUndefined)(window._wpLoadBlockEditor)){var t=e("core/editor");return{lockSaving:t.lockPostSaving,unlockSaving:t.unlockPostSaving}}return{}})),d.withValidation));var V=Object(l.combineReducers)({containerDefinitionsByBlockName:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SETUP_CONTAINER_DEFINITIONS":return t.payload.definitions;default:return e}},fieldDefinitionsByBlockName:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SETUP_FIELD_DEFINITIONS":return t.payload.definitions;default:return e}}});function K(e){return{type:"SETUP_CONTAINER_DEFINITIONS",payload:{definitions:e}}}function $(e){return{type:"SETUP_FIELD_DEFINITIONS",payload:{definitions:e}}}function W(e,t){return e.containerDefinitionsByBlockName[t.replace("carbon-fields/","")]||{}}function H(e,t){return e.fieldDefinitionsByBlockName[t.replace("carbon-fields/","")]||[]}Object(l.registerStore)("carbon-fields/blocks",{reducer:V,actions:r,selectors:o});var z=n(26),J=n.n(z),Q=n(14),X=n(15),Y=(n(71),n(27)),Z=n.n(Y);var ee=function(e){P()(n,e);var t=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=G()(e);if(t){var o=G()(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return F()(this,n)}}(n);function n(){var e;j()(this,n);for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return e=t.call.apply(t,[this].concat(o)),c()(E()(e),"state",{response:null}),e}return x()(n,[{key:"componentDidMount",value:function(){this.isStillMounted=!0,this.fetch(this.props),this.fetch=Object(s.debounce)(this.fetch,500)}},{key:"componentWillUnmount",value:function(){this.isStillMounted=!1}},{key:"componentDidUpdate",value:function(e){Object(s.isEqual)(e,this.props)||this.fetch(this.props)}},{key:"fetch",value:function(e){var t=this;if(this.isStillMounted){null!==this.state.response&&this.setState({response:null});var n=e.block,r=this.currentFetchRequest=Z()({method:"post",path:"/carbon-fields/v1/block-renderer",data:{name:n.name,content:Object(a.serialize)([n])}}).then((function(e){t.isStillMounted&&r===t.currentFetchRequest&&e&&e.rendered&&t.setState({response:e.rendered})})).catch((function(e){t.isStillMounted&&r===t.currentFetchRequest&&t.setState({response:{error:!0,errorMsg:e.message}})}))}}},{key:"render",value:function(){var e=this.state.response,t=this.props.className;return e?e.error?wp.element.createElement(Q.Placeholder,{className:t},Object(u.sprintf)(Object(u.__)("Error loading block: %s","carbon-fields-ui"),e.errorMsg)):e.length?wp.element.createElement(B.RawHTML,{key:"html",className:t},e):wp.element.createElement(Q.Placeholder,{className:t},Object(u.__)("No results found.","carbon-fields-ui")):wp.element.createElement(Q.Placeholder,{className:t},wp.element.createElement(Q.Spinner,null))}}]),n}(B.Component),te=Object(l.withSelect)((function(e,t){var n=t.clientId;return{block:(0,e("core/block-editor").getBlock)(n)}}))(ee);function ne(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function re(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ne(Object(n),!0).forEach((function(t){c()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ne(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var oe=function(e){P()(n,e);var t=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=G()(e);if(t){var o=G()(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return F()(this,n)}}(n);function n(){var e;j()(this,n);for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return e=t.call.apply(t,[this].concat(o)),c()(E()(e),"state",{mode:e.props.container.settings.mode,currentTab:e.props.supportsTabs?Object.keys(e.props.container.settings.tabs)[0]:null}),c()(E()(e),"handleFieldChange",(function(t,n){var r=e.props,o=r.attributes,i=r.setAttributes,l=t.replace(/^.+__(.+)?$/,"$1");i({data:re(re({},o.data),{},c()({},l,n))})})),c()(E()(e),"handleModeChange",(function(){e.setState({mode:e.isInEditMode?"preview":"edit"})})),c()(E()(e),"handleTabChange",(function(t){e.setState({currentTab:t})})),c()(E()(e),"renderField",(function(t,n){var r=e.props,o=r.clientId,i=r.container,c=r.attributes,l=Object(d.getFieldType)(t.type,"block");if(!l)return null;var a="cf-".concat(o,"__").concat(t.base_name),u=Object(s.get)(c.data,t.base_name,t.default_value);return wp.element.createElement(N,{key:n,id:a,field:t},wp.element.createElement(l,{id:a,containerId:i.id,blockId:o,value:u,field:t,name:t.base_name,onChange:e.handleFieldChange}))})),e}return x()(n,[{key:"isInEditMode",get:function(){return"edit"===this.state.mode}},{key:"isInPreviewMode",get:function(){return"preview"===this.state.mode}},{key:"renderTabbedFields",value:function(e){var t=this,n=this.props.fields;return Object(s.map)(e,(function(e,r){var o=Object(s.find)(n,["name",e]);return t.renderField(o,r)}))}},{key:"renderNonTabbedFields",value:function(){return wp.element.createElement("div",{className:"cf-block__fields"},this.props.fields.map(this.renderField))}},{key:"render",value:function(){var e=this,t=this.state.currentTab,n=this.props,r=n.clientId,o=n.container,i=n.supportsTabs,c=n.supportsPreview,l=n.supportsInnerBlocks&&this.isInEditMode&&wp.element.createElement("div",{className:"cf-block__inner-blocks"},wp.element.createElement(X.InnerBlocks,{template:o.settings.inner_blocks.template,templateLock:o.settings.inner_blocks.template_lock,allowedBlocks:o.settings.inner_blocks.allowed_blocks}));return wp.element.createElement(B.Fragment,null,"above"===o.settings.inner_blocks.position&&l,c&&wp.element.createElement(X.BlockControls,null,wp.element.createElement(Q.ToolbarGroup,{label:"Options",controls:[{icon:this.isInEditMode?"visibility":"hidden",title:this.isInEditMode?Object(u.__)("Show preview","carbon-fields-ui"):Object(u.__)("Hide preview","carbon-fields-ui"),onClick:this.handleModeChange}]})),this.isInEditMode&&i&&wp.element.createElement("div",{className:"cf-block__tabs"},wp.element.createElement("ul",{className:"cf-block__tabs-list"},Object(s.map)(o.settings.tabs,(function(n,r){var o=J()("cf-block__tabs-item",{"cf-block__tabs-item--current":r===t});return wp.element.createElement("li",{key:r,className:o,onClick:function(){return e.handleTabChange(r)}},r)})))),this.isInEditMode&&(i?Object(s.map)(o.settings.tabs,(function(n,r){return wp.element.createElement("div",{className:"cf-block__fields",key:r,hidden:r!==t},e.renderTabbedFields(n))})):this.renderNonTabbedFields()),this.isInPreviewMode&&wp.element.createElement("div",{className:"cf-block__preview"},wp.element.createElement(te,{clientId:r})),"below"===o.settings.inner_blocks.position&&l,this.isInPreviewMode&&wp.element.createElement(X.InspectorControls,null,i?Object(s.map)(o.settings.tabs,(function(t,n){return wp.element.createElement(Q.PanelBody,{key:n,title:n},wp.element.createElement("div",{className:"cf-block__fields"},e.renderTabbedFields(t)))})):wp.element.createElement(Q.PanelBody,{title:Object(u.__)("Fields","carbon-fields-ui")},this.renderNonTabbedFields())))}}]),n}(B.Component),ie=Object(l.withSelect)((function(e,t){var n=t.clientId,r=t.name,o=e("core/blocks").hasBlockSupport,i=e("core/block-editor").getBlockRootClientId,c=e("carbon-fields/blocks"),l=c.getContainerDefinitionByBlockName,a=c.getFieldDefinitionsByBlockName,u=i(n);return{container:l(r),fields:a(r),supportsTabs:o(r,"tabs"),supportsPreview:o(r,"preview")&&!u,supportsInnerBlocks:o(r,"innerBlocks")}}))(oe);var ce=function(e){P()(n,e);var t=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=G()(e);if(t){var o=G()(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return F()(this,n)}}(n);function n(){return j()(this,n),t.apply(this,arguments)}return x()(n,[{key:"render",value:function(){return null}}]),n}(B.Component);wp.hooks.addFilter("blocks.getSaveElement","carbon-fields/blocks",(function(e,t){return/^carbon\-fields\/.+$/.test(t.name)&&t.supports.innerBlocks?wp.element.createElement(X.InnerBlocks.Content,null):e}));var le=ce;function ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ue(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ae(Object(n),!0).forEach((function(t){c()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ae(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}Object(u.setLocaleData)(window.cf.config.locale,"carbon-fields-ui");var fe={},pe={};Object(s.get)(window.cf,"preloaded.blocks",[]).forEach((function(e){var t=Object(s.kebabCase)(e.id).replace("carbon-fields-container-",""),n=function(e){return e.reduce((function(e,t){return ue(ue({},e),{},c()({},t.base_name,t.default_value))}),{})}(e.fields),r=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return Object(s.get)(e,"settings.".concat(t),n)};fe[t]=e,pe[t]=e.fields.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?se(Object(n),!0).forEach((function(t){c()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):se(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},e)})),Object(a.registerBlockType)("carbon-fields/".concat(t),{title:e.title,icon:r("icon"),parent:r("parent",[]),category:r("category.slug"),keywords:r("keywords",[]),description:r("description",""),attributes:{data:{type:"object",default:n}},supports:{tabs:Object(s.isPlainObject)(r("tabs")),preview:r("preview"),innerBlocks:r("inner_blocks.enabled"),alignWide:!1,anchor:!1,html:!1},edit:ie,save:le,example:!0})})),Object(l.dispatch)("carbon-fields/blocks").setupContainerDefinitions(fe),Object(l.dispatch)("carbon-fields/blocks").setupFieldDefinitions(pe)}]);