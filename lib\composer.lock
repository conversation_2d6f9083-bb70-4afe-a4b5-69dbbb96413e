{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "3ba3bd12ee866018da47052d4aa3294d", "packages": [{"name": "htmlburger/carbon-field-icon", "version": "v3.1.0", "source": {"type": "git", "url": "https://github.com/htmlburger/carbon-field-icon.git", "reference": "fe0341462e8c8681bb7ff20380974022578e15b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/htmlburger/carbon-field-icon/zipball/fe0341462e8c8681bb7ff20380974022578e15b0", "reference": "fe0341462e8c8681bb7ff20380974022578e15b0", "shasum": ""}, "require": {"htmlburger/carbon-fields": "^3.0"}, "type": "library", "autoload": {"files": ["core/bootstrap.php"], "psr-4": {"Carbon_Field_Icon\\": "core/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "authors": [{"name": "htmlBurger", "email": "<EMAIL>", "homepage": "https://htmlburger.com/", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Carbon Fields extension, that adds a Icon field type.", "keywords": ["carbon-field", "carbon-field-icon", "wordpress"], "support": {"issues": "https://github.com/htmlburger/carbon-field-icon/issues", "source": "https://github.com/htmlburger/carbon-field-icon/tree/v3.1.0"}, "time": "2019-05-07T12:38:53+00:00"}, {"name": "htmlburger/carbon-fields", "version": "v3.6.3", "source": {"type": "git", "url": "https://github.com/htmlburger/carbon-fields.git", "reference": "d913a5148cb9dc61ed239719c747f4ebb513003f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/htmlburger/carbon-fields/zipball/d913a5148cb9dc61ed239719c747f4ebb513003f", "reference": "d913a5148cb9dc61ed239719c747f4ebb513003f", "shasum": ""}, "require": {"php": ">=5.6.20"}, "require-dev": {"mockery/mockery": "1.3.6", "phpunit/phpunit": "7.5.20|9.6.3", "yoast/phpunit-polyfills": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Carbon_Fields\\": "core/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "htmlBurger", "email": "<EMAIL>", "homepage": "https://htmlburger.com/", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://plasmen.info/", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://marinatanasov.com/", "role": "Developer"}, {"name": "<PERSON><PERSON>", "homepage": "http://siyanpanayotov.com/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "stani<PERSON>.k.sto<PERSON><EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "homepage": "http://vilepixels.com/", "role": "Developer"}, {"name": "<PERSON><PERSON>", "homepage": "http://magadanski.com/", "role": "Developer"}, {"name": "German Velchev", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://errorfactory.com/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://alexanderpanayotov.com/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "WordPress developer-friendly custom fields for post types, taxonomy terms, users, comments, widgets, options and more.", "homepage": "http://carbonfields.net/", "support": {"docs": "http://carbonfields.net/docs/", "email": "<EMAIL>", "issues": "https://github.com/htmlburger/carbon-fields/issues", "source": "https://github.com/htmlburger/carbon-fields"}, "time": "2023-11-28T15:52:46+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.6.0"}