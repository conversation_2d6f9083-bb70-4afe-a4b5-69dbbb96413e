/* ==========================================================================
   Media Gallery
   ========================================================================== */

.cf-media-gallery__list {
	display: flex;
	flex-wrap: wrap;
	max-height: 400px;
	padding: 4px;
	margin: 0;
	overflow-y: auto;
	list-style: none outside none;

	&:empty {
		display: none;
	}
}

.cf-media-gallery__actions {
	padding: 8px;

	.cf-media-gallery__list:empty ~ & {
		border-top-width: 0;
	}
}

.cf-media-gallery__item {
	flex: 0 0 100%;
	min-width: 0;
	padding: 4px;
	margin: 0;
	box-sizing: border-box;

	@media (min-width: 320px) {
		flex-basis: 50%;
	}

	@media (min-width: 480px) {
		flex-basis: 33.3333%;
	}

	@media (min-width: 640px) {
		flex-basis: 25%;
	}

	@media (min-width: 768px) {
		flex-basis: 20%;
	}

	@media (min-width: 1280px) {
		flex-basis: 16.66667%;
	}

	@media (min-width: 1440px) {
		flex-basis: 12.5%;
	}

	@media (min-width: 1680px) {
		flex-basis: 10%;
	}
}

.cf-media-gallery__item-inner {
	position: relative;
	display: flex;
	flex-direction: column;
	height: 100%;
}

.cf-media-gallery__item-preview {
	position: relative;
	overflow: hidden;
	padding-top: 100%;
	flex: 1;
}

.cf-media-gallery__item-thumb {
	position: absolute;
	top: 50%;
	left: 50%;
	min-width: 100%;
	min-height: 100%;
	max-width: 150%;
	transform: translate(-50%, -50%);
	pointer-events: none;
}

.cf-media-gallery__item-icon {
	position: absolute;
	top: 50%;
	left: 50%;
	max-width: 100%;
	max-height: 50%;
	transform: translate(-50%, -50%);
}

.cf-media-gallery__item-name {
	display: block;
	padding: 4px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	cursor: move;
}

.cf-media-gallery__item-remove {
	position: absolute;
	top: 4px;
	right: 4px;
	padding: 0;
	border: 0;
	outline: none;
	background-color: transparent;
	cursor: pointer;

	&::before {
		border-radius: 50%;
		background-color: $wp-color-ultra-dark-gray;
		color: $color-white;
		transition: color $transition-base;
	}

	&:hover::before {
		color: $wp-color-gray-light-800;
	}
}


