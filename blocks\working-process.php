<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_working_process' );
function everest_block_working_process() {
Block::make( __( 'Xbees Working Process' ) )
	->add_fields( array(
        Field::make( 'text', 'title', __( 'Title' ) ),
        Field::make( 'text', 'subtext', __( 'Subtitle' ) ),
        Field::make( 'complex', 'list', __( 'Service List' ) )
            ->add_fields( array(
                Field::make( 'text', 'icon', __( 'Icon Class' ) ),
                Field::make( 'text', 'subtext', __( 'Icon Class' ) ),
                Field::make( 'text', 'title', __( 'Icon Class' ) ),

            ) )
            ->set_default_value( array(
                array(
                    'icon' => 'icon-coder-1',
                    'subtext' => 'Step 01',
                    'title' => 'Complete Business Plans',

                ),
                array(
                    'icon' => 'icon-reading-1',
                    'subtext' => 'Step 02',
                    'title' => 'Feasibility Study',

                ),
                array(
                    'icon' => 'icon-credit-card-1',
                    'subtext' => 'Step 03',
                    'title' => 'Growth Plans',

                ),
                array(
                    'icon' => 'icon-online-store-1',
                    'subtext' => 'Step 04',
                    'title' => 'Pitch Deck',

                ),
                array(
                    'icon' => 'icon-diploma-1',
                    'subtext' => 'Step 05',
                    'title' => 'Operations Plans',

                ),
            ) ),

        ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $title = !empty($fields['title']) ? $fields['title'] : '<span>Your Gateway</span> to Success';
        $subtext = !empty($fields['subtext']) ? $fields['subtext'] : 'How It’s Work';
        $image = !empty($fields['image']) ? $fields['image'] : 'https://st.ourhtmldemo.com/new/educamb/assets/images/resources/teaching-img.jpg';

      ?>
        <!--Start Academy Working process Area-->
        <section class="academy-working-process-area">
            <div class="container">
                <div class="sec-title-style3 text-center">
                    <div class="sub-title">
                        <h5><?php echo $subtext; ?></h5>
                    </div>
                    <h2><?php echo $title; ?></h2>
                </div>
                <div class="row">
                    <div class="col-xl-12">
                        <div class="academy-working-process__content-outer">
                            <div class="dotted-line">
                                <img src="https://st.ourhtmldemo.com/new/educamb/assets/images/shape/dotted-line.png" alt="">
                            </div>
                            <ul class="academy-working-process__content">
                                <?php foreach ($fields['list'] as $key => $list) { ?>
                                <li>
                                    <div class="academy-working-process-single-box">
                                        <div class="icon">
                                            <span class="<?php echo $list['icon']; ?>"></span>
                                        </div>
                                        <div class="title">
                                            <h6><?php echo $list['subtext']; ?></h6>
                                            <h3><?php echo $list['title']; ?></h3>
                                        </div>
                                    </div>
                                </li>
                                <?php } ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--End Academy Working process Area-->
<?php
	} );
}