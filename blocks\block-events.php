<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_events' );
function everest_block_events() {
Block::make( __( 'Xbees Events' ) )
	->add_fields( array(
        Field::make( 'text', 'title', __( 'Title' ) ),
        Field::make( 'text', 'subtext', __( 'Subtitle' ) ),
        Field::make( 'image', 'image', __( 'Tab Image' ) )
        ->set_value_type( 'url' ),
        ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $title = !empty($fields['title']) ? $fields['title'] : 'What\'s Coming up?';
        $subtext = !empty($fields['subtext']) ? $fields['subtext'] : 'Business it will frequently occur that pleasures have to repudiated and accepted.';
        $image = !empty($fields['image']) ? $fields['image'] : 'https://st.ourhtmldemo.com/new/educamb/assets/images/resources/teaching-img.jpg';
        // WP_Query arguments
        $args = array(
            'post_type' => 'everest-eventes',
            'posts_per_page' => 3, // -1 to retrieve all posts, you can adjust this number as needed
            'post_status' => 'publish', // Retrieve only published posts
            // You can add more parameters as needed, like 'orderby', 'order', 'category', 'tag', etc.
        );
        // The Query
        $query = new WP_Query( $args );
      ?>
<!--Start Events Style1 Area-->
<section class="event-style1-area">
    <div class="container">
        <div class="sec-title text-center">
            <h2><?php echo $title; ?></h2>
            <div class="sub-title">
                <p><?php echo $subtext; ?></p>
            </div>
        </div>
        <div class="row">
            <div class="col-xl-6">
                <div class="event-style1-img-box">
                    <img src="<?php echo $image; ?>" alt="">
                </div>
            </div>

            <div class="col-xl-6">
                <div class="event-style1-content-box">
                    <ul>
                    <?php 
                      // The Loop
                        if ( $query->have_posts() ) {
                            while ( $query->have_posts() ) {
                                $query->the_post(); 
                                $date = carbon_get_post_meta(get_the_ID(), 'date');
                                $start_time = carbon_get_post_meta(get_the_ID(), 'start_time');
                                $end_time = carbon_get_post_meta(get_the_ID(), 'end_time');
                                $location = carbon_get_post_meta(get_the_ID(), 'location');
          
                    ?>
                        <li>
                            <div class="overlay-icon">
                                <span class="icon-play-button-1"></span>
                            </div>
                            <div class="single-event-box-style1">
                                <div class="date-box">
                                    <h2><?php echo date('d' , strtotime($date)); ?></h2>
                                    <p><?php echo date('M, Y' , strtotime($date)); ?></p>
                                </div>
                                <div class="title-box">
                                    <div class="event-time">
                                        <span class="icon-clock"></span>
                                        <p><?php echo $start_time; ?> - <?php echo $end_time; ?></p>
                                    </div>
                                    <h3>
                                        <a href="<?php the_permalink(); ?>">
                                            <?php the_title(); ?>
                                        </a>
                                    </h3>
                                </div>
                            </div>
                        </li>
                    <?php } ?>
                    <?php
                    } else {
                        // no posts found
                        echo 'No Eventes found.';
                    }
                    
                    // Restore original post data
                    wp_reset_postdata(); 
                    ?>
                    </ul>
                </div>
            </div>
        </div>
        <div class="btns-box text-center mt-5">
            <a class="btn-one" href="<?php echo get_post_type_archive_link( 'everest-eventes' ); ?>">
                <span class="txt">More Eventes</span>
            </a>
        </div>
    </div>
</section>
<!--End Events Style1 Area-->
<?php
	} );
}