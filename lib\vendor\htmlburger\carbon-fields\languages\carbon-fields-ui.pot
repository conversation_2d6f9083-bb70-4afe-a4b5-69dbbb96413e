msgid ""
msgstr ""
"Content-Type: text/plain; charset=utf-8\n"
"X-Generator: babel-plugin-makepot\n"

#: packages/blocks/components/block-edit/index.js:214
msgid "Show preview"
msgstr ""

#: packages/blocks/components/block-edit/index.js:215
msgid "Hide preview"
msgstr ""

#: packages/blocks/components/block-edit/index.js:286
msgid "Fields"
msgstr ""

#: packages/blocks/components/not-supported-field/index.js:14
msgid "Field of type '%s' is not supported in Gutenberg."
msgstr ""

#: packages/blocks/components/server-side-render/index.js:129
msgid "Error loading block: %s"
msgstr ""

#: packages/blocks/components/server-side-render/index.js:135
msgid "No results found."
msgstr ""

#: packages/blocks/fields/datetime/index.js:59
#: packages/core/fields/date/index.js:15
#: packages/metaboxes/fields/datetime/index.js:9
msgid "Select Date"
msgstr ""

#: packages/blocks/fields/file/index.js:16
#: packages/metaboxes/fields/file/index.js:11
msgid "Use File"
msgstr ""

#: packages/blocks/fields/file/index.js:17
#: packages/metaboxes/fields/file/index.js:12
msgid "Select File"
msgstr ""

#: packages/blocks/fields/image/index.js:11
#: packages/metaboxes/fields/image/index.js:11
msgid "Use Image"
msgstr ""

#: packages/blocks/fields/image/index.js:12
#: packages/metaboxes/fields/image/index.js:12
msgid "Select Image"
msgstr ""

#: packages/blocks/fields/media-gallery/index.js:16
#: packages/metaboxes/fields/media-gallery/index.js:16
msgid "Use Attachments"
msgstr ""

#: packages/blocks/fields/media-gallery/index.js:17
#: packages/metaboxes/fields/media-gallery/index.js:17
msgid "Select Attachments"
msgstr ""

#: packages/core/components/no-options/index.js:14
msgid "No options."
msgstr ""

#: packages/core/components/search-input/index.js:30
msgid "Search..."
msgstr ""

#: packages/core/fields/association/index.js:169
msgid "Maximum number of items reached (%s items)"
msgstr ""

#: packages/core/fields/association/index.js:269
msgid "Showing %1$d of %2$d results"
msgstr ""

#: packages/core/fields/association/index.js:283
msgid "Thumbnail"
msgstr ""

#: packages/core/fields/association/index.js:305
msgid "Edit"
msgstr ""

#: packages/core/fields/association/index.js:313
msgid "Add"
msgstr ""

#: packages/core/fields/association/index.js:365
#: packages/core/fields/complex/group.js:163
msgid "Remove"
msgstr ""

#: packages/core/fields/association/index.js:466
msgid "An error occurred while trying to fetch association options."
msgstr ""

#: packages/core/fields/association/index.js:519
#: packages/core/fields/complex/index.js:428
#: packages/core/hocs/with-validation/required.js:20
msgid "This field is required."
msgstr ""

#: packages/core/fields/association/index.js:523
msgid "Minimum number of items not reached (%s items)"
msgstr ""

#: packages/core/fields/color/index.js:106
msgid "Clear"
msgstr ""

#: packages/core/fields/color/index.js:92
msgid "Select a color"
msgstr ""

#: packages/core/fields/complex/group.js:154
msgid "Duplicate"
msgstr ""

#: packages/core/fields/complex/group.js:172
msgid "Collapse"
msgstr ""

#: packages/core/fields/complex/index.js:146
msgid "Couldn't create the label of group - %s"
msgstr ""

#: packages/core/fields/complex/index.js:344
msgid "There are no entries yet."
msgstr ""

#: packages/core/fields/complex/index.js:401
msgid "Expand All"
msgstr ""

#: packages/core/fields/complex/index.js:401
msgid "Collapse All"
msgstr ""

#: packages/core/fields/complex/index.js:435
msgid "Minimum number of rows not reached (%1$d %2$s)"
msgstr ""

#: packages/core/fields/complex/index.js:82
msgid "Add %s"
msgstr ""

#: packages/core/fields/map/index.js:165
msgid "The address could not be found."
msgstr ""

#: packages/core/fields/map/index.js:167
msgid "Geocode was not successful for the following reason: "
msgstr ""

#: packages/core/fields/map/index.js:185
msgid "Error alert"
msgstr ""

#: packages/core/fields/oembed/index.js:187
msgid "An error occurred while trying to fetch oembed preview."
msgstr ""

#: packages/core/fields/oembed/index.js:202
msgid "Not Found"
msgstr ""

#: packages/core/fields/rich-text/index.js:123
msgid "Visual"
msgstr ""

#: packages/core/fields/rich-text/index.js:127
msgid "Text"
msgstr ""

#: packages/core/fields/set/index.js:110
msgid "Show All Options"
msgstr ""

#: packages/core/fields/set/index.js:111
msgid "Show Less Options"
msgstr ""

#: packages/core/fields/sidebar/index.js:110
msgid "Please enter the name of the new sidebar:"
msgstr ""

#: packages/core/fields/sidebar/index.js:128
msgid "An error occurred while trying to create the sidebar."
msgstr ""

#: packages/core/fields/sidebar/index.js:58
msgid "Please choose"
msgstr ""

#: packages/core/fields/time/index.js:16
msgid "Select Time"
msgstr ""

#: packages/core/hocs/with-conditional-logic/index.js:87
msgid "An unknown field is used in condition - \"%s\""
msgstr ""

#: packages/core/registry/index.js:40
msgid "%1$s type must be a string."
msgstr ""

#: packages/core/registry/index.js:46
msgid "%1$s %2$s is already registered."
msgstr ""

#: packages/core/registry/index.js:54
msgid "The \"component\" param must be a function."
msgstr ""

#: packages/core/registry/index.js:79
msgid "The provided context isn't a valid one. Must be one of - %s ."
msgstr ""

#: packages/core/registry/index.js:89
msgid "%s %s isn't registered."
msgstr ""

#: packages/core/utils/api-fetch.js:19
msgid "An error occured."
msgstr ""

#: packages/core/utils/fetch-attachments-data.js:24
msgid "An error occurred while trying to fetch files data."
msgstr ""

#: packages/metaboxes/containers/index.js:54
msgid "Could not find DOM element for container \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/conditions/base.js:52
msgid "Unsupported container condition comparison operator used - \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/handler/index.js:56
msgid "Unsupported container condition - \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/handler/index.js:74
msgid "Unsupported container condition relation used - \"%1$s\"."
msgstr ""