msgid ""
msgstr ""
"Project-Id-Version: Carbon Fields\n"
"POT-Creation-Date: 2019-01-02 15:20+0200\n"
"PO-Revision-Date: 2020-11-05 15:38+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: de_DE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.4.1\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: languages\n"
"X-Poedit-SearchPathExcluded-2: node_modules\n"
"X-Poedit-SearchPathExcluded-3: tests\n"
"X-Poedit-SearchPathExcluded-4: tmp\n"
"X-Poedit-SearchPathExcluded-5: vendor\n"

#: core/Container/Block_Container.php:259
msgid "'render_callback' is required for the blocks."
msgstr ""

#: core/Container/Block_Container.php:263
msgid "'render_callback' must be a callable."
msgstr ""

#: core/Container/Container.php:712
msgid "General"
msgstr "Allgemein"

#: core/Container/Theme_Options_Container.php:207
msgid "Settings saved."
msgstr "Deine Einstellungen wurden gespeichert."

#: core/Field/Footer_Scripts_Field.php:20
msgid ""
"If you need to add scripts to your footer (like Google Analytics tracking "
"code), you should enter them in this box."
msgstr ""
"Wenn du Skripte in deinen Footer hinzufügen möchtest (z.B. Google Analytics "
"Tracking Code), solltest du sie in diese Box einfügen."

#: core/Field/Gravity_Form_Field.php:47
msgid "No form"
msgstr "Kein Formular"

#: core/Field/Header_Scripts_Field.php:20
msgid "If you need to add scripts to your header, you should enter them here."
msgstr ""
"Wenn du Skripte in deinen Header hinzufügen möchtest, solltest du sie hier "
"einfügen."

#: core/Field/Sidebar_Field.php:74
msgctxt "sidebar"
msgid "Add New"
msgstr "Erstellen"

#: core/Helper/Helper.php:612
msgid "F j, Y"
msgstr "j. F Y"

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:69
msgid "Please enter a name for the sidebar."
msgstr "Bitte gib einen Namen für die neue Seitenleiste an:"

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:72
msgid "Unknown action attempted."
msgstr "Es wurde versucht einen unbekannte Aktion auszuführen."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:102
msgid "Sidebar with the same ID is already registered."
msgstr "Eine Seitenleiste mit der selben ID ist bereits registriert."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:112
#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:142
msgid ""
"Failed to update option storing your custom sidebars. Please contact support."
msgstr ""
"Fehler beim Aktualisieren Ihrer benutzerdefinierten Sidebars. Kontaktiere "
"bitte den Support."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:137
msgid "Sidebar not found."
msgstr "Seitenleiste wurde nicht gefunden"

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:181
msgid "Add Sidebar"
msgstr "Seitenleiste hinzufügen"

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:182
msgid "Please enter the name of the new sidebar:"
msgstr "Bitte benenne die neue Seitenleiste:"

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:183
msgid "Are you sure you wish to remove this sidebar?"
msgstr "Möchest du diese Seitenleiste wirklich entfernen?"

#: core/REST_API/Router.php:341
msgid "No option names provided"
msgstr "Keine Optionsnamen verfügbar"

#: core/REST_API/Router.php:352
msgid "Theme Options updated."
msgstr "Theme-Optionen aktualisiert."

#: templates/Container/common/options-page.php:43
msgid "Actions"
msgstr "Aktionen"

#: templates/Container/common/options-page.php:52
msgid "Save Changes"
msgstr "Änderungen speichern"

#: templates/Container/widget.php:4
msgid "No options are available for this widget."
msgstr "Für dieses Widget sind keine Optionen verfügbar."
