/* ==========================================================================
   Field
   ========================================================================== */

.cf-field {
	.cf-container & {
		flex: 1 1 100%;
		padding: $size-base * 3;
		border-width: 0 1px 0 0;
		border-style: solid;
		border-color: $wp-color-gray-light-500;
	}

	.cf-container & + & {
		border-top-width: 1px;
	}
}

.cf-field__head {
	.term-php .cf-container__fields > .cf-field:not(.cf-field--has-width) > &,
	.cf-container-user-meta .cf-container__fields > .cf-field > & {
		position: absolute;
		left: 0;
		font-weight: 600;
	}
}

.cf-field__label {
	.cf-container & {
		padding-bottom: 6.5px;
		font-weight: 600;
		color: $wp-color-dark-gray;
	}

	.cf-container-term-meta &,
	.cf-container-user-meta & {
		margin: 0 !important;
	}
}

.cf-field__help {
	display: inline-block;
	color: #666;
	margin-top: 10px;
}
