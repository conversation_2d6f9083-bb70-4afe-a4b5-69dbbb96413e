{"name": "htmlburger/carbon-fields", "description": "WordPress developer-friendly custom fields for post types, taxonomy terms, users, comments, widgets, options and more.", "license": "GPL-2.0-only", "homepage": "http://carbonfields.net/", "authors": [{"name": "htmlBurger", "email": "<EMAIL>", "homepage": "https://htmlburger.com/", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://plasmen.info/", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://marinatanasov.com/", "role": "Developer"}, {"name": "<PERSON><PERSON>", "homepage": "http://siyanpanayotov.com/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "stani<PERSON>.k.sto<PERSON><EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "homepage": "http://vilepixels.com/", "role": "Developer"}, {"name": "<PERSON><PERSON>", "homepage": "http://magadanski.com/", "role": "Developer"}, {"name": "German Velchev", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://errorfactory.com/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://alexanderpanayotov.com/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "support": {"source": "https://github.com/htmlburger/carbon-fields", "issues": "https://github.com/htmlburger/carbon-fields/issues", "docs": "http://carbonfields.net/docs/", "email": "<EMAIL>"}, "autoload": {"psr-4": {"Carbon_Fields\\": "core/"}}, "require": {"php": ">=5.6.20"}, "require-dev": {"phpunit/phpunit": "7.5.20|9.6.3", "mockery/mockery": "1.3.6", "yoast/phpunit-polyfills": "^1.0"}}