{"name": "htmlburger/carbon-field-icon", "description": "Carbon Fields extension, that adds a Icon field type.", "keywords": ["wordpress", "carbon-field", "carbon-field-icon"], "type": "library", "license": "GPL-2.0+", "authors": [{"name": "htmlBurger", "email": "<EMAIL>", "homepage": "https://htmlburger.com/", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"htmlburger/carbon-fields": "^3.0"}, "autoload": {"files": ["core/bootstrap.php"], "psr-4": {"Carbon_Field_Icon\\": "core/"}}}