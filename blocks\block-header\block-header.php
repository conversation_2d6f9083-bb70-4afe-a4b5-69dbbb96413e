<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_header' );
function everest_block_header() {
Block::make( __( 'Xbees Header' ) )
	->add_fields( array(
        Field::make( 'select', 'header_style', __( 'Header Style' ) )
        ->set_options( array(
            '1' => __( 'Header style 1' ),
            '2' => __( 'Header style 2' ),
            '3' => __( 'Header style 3' ),
        ) ),
        Field::make( 'image', 'logo', __( 'Site Logo' ) )
        ->set_value_type( 'url' ),
		Field::make( 'text', 'email', __( 'Email' ) ),
		Field::make( 'text', 'adress', __( 'Adress' ) ),
		Field::make( 'text', 'btn-text', __( 'Button Text' ) ),
		Field::make( 'text', 'btn-url', __( 'Button Url' ) ),
		Field::make( 'text', 'phone', __( 'Pone Number' ) ),
        ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $email = !empty($fields['email']) ? $fields['email'] : ' <EMAIL>';
        $adress = !empty($fields['adress']) ? $fields['adress'] : ' 7 Bell Yard, London';
        $btn_text = !empty($fields['btn-text']) ? $fields['btn-text'] : 'Get A Quote';
        $btn_url = !empty($fields['btn-url']) ? $fields['btn-url'] : '#';
        $phone = !empty($fields['phone']) ? $fields['phone'] : '(+20)120 0433 432';
        $custom_logo_id = get_theme_mod( 'custom_logo' );
        $logo = !empty($fields['logo']) ? $fields['logo'] : get_stylesheet_directory_uri() . '/assets/images/resources/logo.png';
        ?>


    <?php if(  ! is_blockEditor() ) : ?>
    <!-- preloader -->
    <div class="loader-wrap">
        <div class="preloader">
            <div class="preloader-close">x</div>
            <div id="handle-preloader" class="handle-preloader">
                <div class="animation-preloader">
                    <div class="spinner"></div>
                    <div class="txt-loading">
                        <span data-text-preloader="e" class="letters-loading">
                            e
                        </span>
                        <span data-text-preloader="v" class="letters-loading">
                            v
                        </span>
                        <span data-text-preloader="e" class="letters-loading">
                            e
                        </span>
                        <span data-text-preloader="r" class="letters-loading">
                            r
                        </span>
                        <span data-text-preloader="e" class="letters-loading">
                            e
                        </span>
                        <span data-text-preloader="s" class="letters-loading">
                            s
                        </span>
                        <span data-text-preloader="t" class="letters-loading">
                            t
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- preloader end -->
    <?php endif; ?>

    <?php 
       switch ($fields['header_style']) {
        case '1':
            include 'header-1.php';
            break;
        case '2':
            include 'header-2.php';
            break;
        case '3':
            include 'header-3.php';
            break;
        default:
            include 'header-1.php';
            break;
       }
    ?>



    <?php
	} );
}