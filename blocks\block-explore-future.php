<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_explore_future' );
function everest_block_explore_future() {
Block::make( __( 'Xbees Explore Future' ) )
	->add_fields( array(
        Field::make( 'text', 'title', __( 'Title' ) ),
        Field::make( 'text', 'subtext', __( 'Subtitle' ) ),

        Field::make( 'complex', 'tab', __( 'Tab Options' ) )
            ->add_fields( array(
                Field::make( 'image', 'image', __( 'Tab Image' ) )
                ->set_value_type( 'url' ),
                Field::make( 'textarea', 'title', __( 'Title' ) ),
                Field::make( 'textarea', 'text', __( 'Text Content' ) ),
            ) )

        ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $title = !empty($fields['title']) ? $fields['title'] : 'Our Values';
        $subtext = !empty($fields['subtext']) ? $fields['subtext'] : 'A Strategic Partnership, Where Innovation Meets Guidance';

      ?>
<!--Start Explore Future Area-->
<section class="explore-future-area">
    <div class="container">
        <div class="row">
            <div class="col-xl-12">
                <div class="explore-future-tab">
                    <div class="row">

                        <div class="col-xl-4">
                            <div class="explore-future-tab__button">
                                <div class="sec-title">
                                    <h2><?php echo $title; ?></h2>
                                    <div class="sub-title">
                                        <p><?php echo $subtext; ?></p>
                                    </div>
                                </div>
                                <ul class="tab-btns clearfix">
                                    <?php foreach ((array) $fields['tab'] as $key => $value) {                                      
                                       if( $key == 0 ) {
                                        $active = ' active-btn';
                                       }else {
                                        $active ='';
                                       }
                                    ?>
                                    <li data-tab="#<?php echo str_replace([' ', '?'], '', $value['title']); ?>" class="tab-btn<?php echo $active; ?>">
                                        <h3><?php echo $value['title']; ?></h3>
                                        <div class="round-box"></div>
                                    </li>
                                    <?php } ?>
                                </ul>
                            </div>
                        </div>


                        <div class="col-xl-8">
                            <div class="pr-content">
                            <?php foreach ((array) $fields['tab'] as $key => $value) {                                      
                                       if( $key == 0 ) {
                                        $active = ' active-tab';
                                       }else {
                                        $active ='';
                                       }
                                    ?>
                                <!--Tab-->
                                <div class="pr-tab<?php echo $active; ?>" id="<?php echo str_replace([' ', '?'], '', $value['title']); ?>">
                                    <div class="explore-future-tab__content">
                                        <div class="img-holder">
                                            <img src="<?php echo $value['image'];  ?>" alt="">
                                        </div>
                                        <div class="text-holder">
                                            <h2><a href="#"><?php echo $value['title'];  ?></a></h2>
                                            <p><?php echo $value['text'];  ?></p>
                                        </div>
                                    </div>
                                </div>
                                <?php } ?>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--End Explore Future Area-->
<?php
	} );
}