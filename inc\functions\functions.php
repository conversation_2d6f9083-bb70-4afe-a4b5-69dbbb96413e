<?php 

/**
 * Enqueue the CSS files.
 *
 * @since 1.0.0
 *
 * @return void
 */
function everest_styles() {
	wp_enqueue_style(
		'everest-style',
		get_stylesheet_uri(),
		[],
		wp_get_theme()->get( 'Version' )
	);
	// ملفات CSS الرئيسية
	wp_enqueue_style( 'animate', get_template_directory_uri() . '/assets/css/animate.css', array(), '3.8.0', 'all' );
	wp_enqueue_style( 'aos', get_template_directory_uri() . '/assets/css/aos.css', array(), '1.5.0', 'all' );
	wp_enqueue_style( 'bootstrap', get_template_directory_uri() . '/assets/css/bootstrap.min.css', array(), '5.1.3', 'all' );
	wp_enqueue_style( 'style', get_template_directory_uri() . '/assets/css/style.css', array(), rand(), 'all' );
	wp_enqueue_style( 'custom-animate', get_template_directory_uri() . '/assets/css/custom-animate.css', array(), rand(), 'all' );
	wp_enqueue_style( 'fancybox', get_template_directory_uri() . '/assets/css/fancybox.min.css', array(), '3.5.7', 'all' );
	
	wp_enqueue_style( 'flaticon', get_template_directory_uri() . '/assets/css/flaticon.css', array(), '3.5.7', 'all' );
	wp_enqueue_style( 'font-awesome', get_template_directory_uri() . '/assets/css/font-awesome.min.css', array(), '3.5.7', 'all' );
	wp_enqueue_style( 'icomoon', get_template_directory_uri() . '/assets/css/icomoon.css', array(), '3.5.7', 'all' );
	wp_enqueue_style( 'jquery.bootstrap-touchspin', get_template_directory_uri() . '/assets/css/jquery.bootstrap-touchspin.css', array(), '3.5.7', 'all' );
	wp_enqueue_style( 'magnific-popup', get_template_directory_uri() . '/assets/css/magnific-popup.css', array(), '3.5.7', 'all' );
	wp_enqueue_style( 'nice-select', get_template_directory_uri() . '/assets/css/nice-select.css', array(), '3.5.7', 'all' );
	wp_enqueue_style( 'owl', get_template_directory_uri() . '/assets/css/owl.css', array(), '3.5.7', 'all' );
	wp_enqueue_style( 'rtl', get_template_directory_uri() . '/assets/css/rtl.css', array(), '3.5.7', 'all' );
	wp_enqueue_style( 'scrollbar', get_template_directory_uri() . '/assets/css/scrollbar.css', array(), '3.5.7', 'all' );
	wp_enqueue_style( 'swiper.min', get_template_directory_uri() . '/assets/css/swiper.min.css', array(), '3.5.7', 'all' );

	
	// ملفات CSS خاصة بوحدات محددة (مع تعديل المسارات حسب الموقع الفعلي)
	wp_enqueue_style( 'header-section', get_template_directory_uri() . '/assets/css/module-css/header-section.css', array(), rand(), 'all' );
	wp_enqueue_style( 'banner-section', get_template_directory_uri() . '/assets/css/module-css/banner-section.css', array(), rand(), 'all' );
	wp_enqueue_style( 'about-section', get_template_directory_uri() . '/assets/css/module-css/about-section.css', array(), rand(), 'all' );
	wp_enqueue_style( 'blog-section', get_template_directory_uri() . '/assets/css/module-css/blog-section.css', array(), rand(), 'all' );
	wp_enqueue_style( 'fact-counter-section', get_template_directory_uri() . '/assets/css/module-css/fact-counter-section.css', array(), rand(), 'all' );
	wp_enqueue_style( 'faq-section', get_template_directory_uri() . '/assets/css/module-css/faq-section.css', array(), rand(), 'all' );
	wp_enqueue_style( 'contact-page', get_template_directory_uri() . '/assets/css/module-css/contact-page.css', array(), rand(), 'all' );
	wp_enqueue_style( 'breadcrumb-section', get_template_directory_uri() . '/assets/css/module-css/breadcrumb-section.css', array(), rand(), 'all' );
	wp_enqueue_style( 'team-section', get_template_directory_uri() . '/assets/css/module-css/team-section.css', array(), rand(), 'all' );
	wp_enqueue_style( 'partner-section', get_template_directory_uri() . '/assets/css/module-css/partner-section.css', array(), rand(), 'all' );
	wp_enqueue_style( 'testimonial-section', get_template_directory_uri() . '/assets/css/module-css/testimonial-section.css', array(), rand(), 'all' );
	wp_enqueue_style( 'services-section', get_template_directory_uri() . '/assets/css/module-css/services-section.css', array(), rand(), 'all' );
	wp_enqueue_style( 'footer-section', get_template_directory_uri() . '/assets/css/module-css/footer-section.css', array(), rand(),'');  

	wp_enqueue_style( 'theme-color', get_template_directory_uri() . '/assets/css/theme-color.css', array(), rand(), 'all' );
	wp_enqueue_style( 'responsive', get_template_directory_uri() . '/assets/css/responsive.css', array(), rand(), 'all' );

	}
add_action( 'wp_enqueue_scripts', 'everest_styles' );
add_action( 'enqueue_block_editor_assets', 'everest_styles' );
add_action( 'enqueue_block_assets', 'everest_styles');
function everest_enqueue_scripts() {

	wp_register_script( 'aos', get_template_directory_uri() . '/assets/js/aos.js', array( 'jquery' ), '1.5.0', true );
	wp_enqueue_script( 'aos' );
  
	wp_register_script( 'appear', get_template_directory_uri() . '/assets/js/appear.js', array( 'jquery' ), rand(), true );
	wp_enqueue_script( 'appear' );
  
	wp_register_script( 'bootstrap', get_template_directory_uri() . '/assets/js/bootstrap.bundle.min.js', array( 'jquery' ), '5.1.3', true );
	wp_enqueue_script( 'bootstrap' );
  
	wp_register_script( 'isotope', get_template_directory_uri() . '/assets/js/isotope.js', array(), '3.0.6', true );
	wp_enqueue_script( 'isotope' );
  
	wp_register_script( 'jquery-bootstrap-touchspin', get_template_directory_uri() . '/assets/js/jquery.bootstrap-touchspin.js', array( 'jquery' ), '4.2.4', true );
	wp_enqueue_script( 'jquery-bootstrap-touchspin' );

	wp_register_script( 'fontawesome.min', 'https://kit.fontawesome.com/a1e9eaa4c4.js', array(), rand(), true );
	wp_enqueue_script( 'fontawesome.min' );
  
	// ... add entries for all other listed scripts
	wp_register_script( 'jquery-countTo', get_template_directory_uri() . '/assets/js/jquery.countTo.js', array( 'jquery' ), '1.0.7', true );
	wp_enqueue_script( 'jquery-countTo' );
  
	wp_register_script( 'jquery-easing', get_template_directory_uri() . '/assets/js/jquery.easing.min.js', array(), '1.3.4', true );
	wp_enqueue_script( 'jquery-easing' );
  
	wp_register_script( 'jquery-event-move', get_template_directory_uri() . '/assets/js/jquery.event.move.js', array( 'jquery' ), rand(), true );
	wp_enqueue_script( 'jquery-event-move' );
  
	// ملفات تعتمد على مكتبة jQuery
	wp_register_script( 'jquery-fancybox', get_template_directory_uri() . '/assets/js/jquery.fancybox.js', array( 'jquery' ), '3.5.7', true );
	wp_enqueue_script( 'jquery-fancybox' );

	wp_register_script( 'jquery-magnific-popup', get_template_directory_uri() . '/assets/js/jquery.magnific-popup.min.js', array( 'jquery' ), '1.1.0', true );
	wp_enqueue_script( 'jquery-magnific-popup' );

	wp_register_script( 'jquery-nice-select', get_template_directory_uri() . '/assets/js/jquery.nice-select.min.js', array( 'jquery' ), '1.1.0', true );
	wp_enqueue_script( 'jquery-nice-select' );


	wp_register_script( 'jquery-paroller', get_template_directory_uri() . '/assets/js/jquery.paroller.min.js', array(), '1.1.0', true );
	wp_enqueue_script( 'jquery-paroller' );

	wp_register_script( 'jquery-sidebar-content', get_template_directory_uri() . '/assets/js/jquery-sidebar-content.js', array(), rand(), true );
	wp_enqueue_script( 'jquery-sidebar-content' );

	wp_register_script( 'jquery-1color-switcher', get_template_directory_uri() . '/assets/js/jquery-1color-switcher.min.js', array(), '2.1.2', true );
	wp_enqueue_script( 'jquery-1color-switcher' );

	wp_register_script( 'parallax', get_template_directory_uri() . '/assets/js/parallax.min.js', array(), '1.1.3', true );
	wp_enqueue_script( 'parallax' );

	wp_register_script( 'skrollr', get_template_directory_uri() . '/assets/js/skrollr.min.js', array(), '0.6.30', true );
	wp_enqueue_script( 'skrollr' );

	wp_register_script( 'knob', get_template_directory_uri() . '/assets/js/knob.js', array(), '0.6.30', true );
	wp_enqueue_script( 'knob' );

	wp_register_script( 'map-script', get_template_directory_uri() . '/assets/js/map-script.js', array(), '0.6.30', true );
	wp_enqueue_script( 'map-script' );

	wp_register_script( 'owl', get_template_directory_uri() . '/assets/js/owl.js', array(), '0.6.30', true );
	wp_enqueue_script( 'owl' );

	wp_register_script( 'pagenav', get_template_directory_uri() . '/assets/js/pagenav.js', array(), '0.6.30', true );
	wp_enqueue_script( 'pagenav' );

	wp_register_script( 'scrollbar', get_template_directory_uri() . '/assets/js/scrollbar.js', array(), '0.6.30', true );
	wp_enqueue_script( 'scrollbar' );

	wp_register_script( 'swiper.min', get_template_directory_uri() . '/assets/js/swiper.min.js', array(), '0.6.30', true );
	wp_enqueue_script( 'swiper.min' );

	wp_register_script( 'tilt.jquery', get_template_directory_uri() . '/assets/js/tilt.jquery.js', array(), '0.6.30', true );
	wp_enqueue_script( 'tilt.jquery' );

	wp_register_script( 'TweenMax.min', get_template_directory_uri() . '/assets/js/TweenMax.min.js', array(), '0.6.30', true );
	// wp_enqueue_script( 'TweenMax.min' );

	wp_register_script( 'validation', get_template_directory_uri() . '/assets/js/validation.js', array(), '0.6.30', true );
	wp_enqueue_script( 'validation' );

	wp_register_script( 'wow', get_template_directory_uri() . '/assets/js/wow.js', array(), '0.6.30', true );
	wp_enqueue_script( 'wow' );

	// Enqueue custom script
	wp_register_script( 'custom', get_template_directory_uri() . '/assets/js/custom.js', array(  ), rand(), true );
	wp_enqueue_script( 'custom' );
  
  }
add_action( 'wp_enqueue_scripts', 'everest_enqueue_scripts' );
add_action( 'enqueue_block_editor_assets', 'everest_enqueue_scripts' );
add_action( 'enqueue_block_assets', 'everest_enqueue_scripts');
function everest_enqueue_google_fonts() {
	wp_enqueue_style( 'google-fonts', 'https://fonts.googleapis.com/css2?family=DM+Sans:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&family=Frank+Ruhl+Libre:wght@300;400;500;700;900&family=Averia+Serif+Libre:ital,wght@0,300;0,400;0,700;1,300;1,400;1,700&display=swap', array(), null, 'all' );
  }
add_action( 'wp_enqueue_scripts', 'everest_enqueue_google_fonts' );

add_action( 'init',function(){
    remove_action( 'wp_enqueue_scripts', 'wp_enqueue_global_styles' );
    remove_action( 'wp_footer', 'wp_enqueue_global_styles', 1 );
    remove_action( 'wp_body_open', 'wp_global_styles_render_svg_filters' );
  } );
/* -----------------------------------------  
-- menu setup ------------------------------ */
if ( ! function_exists( 'everest_setup_menu' ) ) :
	function everest_setup_menu(): void
    {
		register_nav_menus(
			array(
				'main-menu'   => esc_html__( 'Primary Menu', 'everest' ),
				'header-top-right'   => esc_html__( 'Header Top Right', 'everest' ),
				'footer-1'  => esc_html__( 'Footer Menu 1', 'everest' ),
				'footer-2'  => esc_html__( 'Footer Menu 2', 'everest' ),
				'footer-3'  => esc_html__( 'Footer Menu 3', 'everest' ),
				'footer-4'  => esc_html__( 'Footer Menu 4', 'everest' ),

			)
		);
	}
endif;

add_action( 'after_setup_theme', 'everest_setup_menu' );
add_action( 'customize_register', '__return_true' );

function everest_custom_nav_link_attributes($attrs, $item, $args, $depth) {
    $attrs['hx-boost'] = 'true';
    $attrs['hx-boost-history'] = esc_attr($item->url );
    return $attrs;
}
add_filter('nav_menu_link_attributes', 'everest_custom_nav_link_attributes', 10, 4);

function is_blockEditor(){
    $admin_url = isset( $_SERVER['HTTP_REFERER'] ) && ! empty( $_SERVER['HTTP_REFERER'] ) ? $_SERVER['HTTP_REFERER'] : null;
	if ( ! empty( $admin_url ) ) {
		$parsed_url = parse_url( $admin_url );
		if ( isset( $parsed_url['query']) && ! empty( $parsed_url['query'] ) ) {
			return true;
		} else {
			return false;
		}
	}
	return false;
}

add_action('everest_social_link', 'everest_social_link');
function everest_social_link($fields){
	?>
		<div class="social-link-box-style1">
			<div class="icon">
				<span class="icon-share"></span>
			</div>
			<ul class="clearfix">
				<li>
					<a href="<?php echo $fields['facebook'] ?>"><i class="icon-facebook-app-symbol"></i></a>
				</li>
				<li>
					<a href="<?php echo $fields['twitter'] ?>"><i class="icon-twitter-1"></i></a>
				</li>
				<li>
					<a href="<?php echo $fields['linkedin'] ?>"><i class="icon-linkedin"></i></a>
				</li>
				<li>
					<a href="<?php echo $fields['youtube'] ?>"><i class="icon-youtube"></i></a>
				</li>
			</ul>
		</div>

	<?php
}

add_action('everest_header_menu', 'everest_header_menu', 10, 2);
function everest_header_menu($menu, $style){

	if( !empty( $menu ) ) {
		switch ($menu) {
			case 'header-top-right':
				if ( has_nav_menu( $menu ) ) {
					wp_nav_menu( array(
						'theme_location' => $menu,
						'menu_class'     => '',
					) );
				}
				break;
			case 'main-menu':
				everest_render_menu( $menu, $style );
				break;	
			
			default:
				break;
		}
	}
}

// Intented to use with locations, like 'primary'
// everest_render_menu("primary");

#add in your theme functions.php file

function everest_render_menu($theme_location, $style) {
    if (($theme_location) && ($locations = get_nav_menu_locations()) && isset($locations[$theme_location])) {
        $menu = get_term($locations[$theme_location], 'nav_menu');
        $menu_items = wp_get_nav_menu_items($menu->term_id);

        $menu_list = '<nav class="main-menu style1 navbar-expand-md navbar-light">' . "\n";
        $menu_list .= '<div class="collapse navbar-collapse show clearfix" id="navbarSupportedContent">' . "\n";
        $menu_list .= '<ul class="navigation clearfix">' . "\n";

        $menu_list .= everest_generate_menu_items($menu_items, 0);

        $menu_list .= '</ul>' . "\n";
        $menu_list .= '</div><!-- /#navbarSupportedContent -->' . "\n";
        $menu_list .= '</nav>' . "\n";
    } else {
        $menu_list = '<!-- no menu defined in location "' . $theme_location . '" -->';
    }

    echo $menu_list;
}

function everest_generate_menu_items($menu_items, $parent_id) {
    $menu_list = '';
    $sub_menu = false;
    
    foreach ($menu_items as $menu_item) {
        if ($menu_item->menu_item_parent == $parent_id) {
            $sub_menu_items = everest_generate_menu_items($menu_items, $menu_item->ID);

            $menu_list .= '<li' . ($sub_menu_items ? ' class="dropdown"' : '') . '>' . "\n";
            $menu_list .= '<a href="' . $menu_item->url . '">' . $menu_item->title . '</a>' . "\n";

            if ($sub_menu_items) {
                $menu_list .= '<ul>' . "\n";
                $menu_list .= $sub_menu_items;
                $menu_list .= '</ul>' . "\n";
            }

            $menu_list .= '</li>' . "\n";
            $sub_menu = true;
        }
    }

    return $menu_list;
}


function everest_breadcrumb(){
	$bg_image = carbon_get_post_meta(get_the_ID(), 'page_background');
	$thumbnail_url = get_the_post_thumbnail_url( get_the_ID(), 'full' ); 
	if( is_archive() ) {
		$thumbnail_url = carbon_get_term_meta( get_queried_object_id(), 'cat_image' );
		if( empty( $thumbnail_url ) ) {
			$thumbnail_url = get_stylesheet_directory_uri() . '/assets/images/resources/bunner.jpg';
		}
		$title    = get_the_archive_title();
		$subtitle = get_the_archive_description();
	} else {
		if( empty( $thumbnail_url ) ) {
			$thumbnail_url = get_stylesheet_directory_uri() . '/assets/images/resources/bunner.jpg';
		}
		$title = get_the_title();
	}
	$image = !empty($bg_image) ? $bg_image  : $thumbnail_url;
?>
	<!--Start breadcrumb area paroller-->
	<section class="breadcrumb-area">
		<div class="breadcrumb-area-bg" style="background-image: url(<?php echo $image; ?>);">
		</div>
		<div class="container">
			<div class="row">
				<div class="col-xl-12">
					<div class="inner-content">
						<div class="title" data-aos="fade-up" data-aos-easing="linear" data-aos-duration="1500">
							<h2><?php echo $title; ?></h2>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!--End breadcrumb area-->
<?php
}

add_filter('get_the_archive_title', function ($title) {
    if (is_category()) {
        $title = single_cat_title('', false);
    } elseif (is_tag()) {
        $title = single_tag_title('', false);
    } elseif (is_author()) {
        $title = '<span class="vcard">' . get_the_author() . '</span>';
    } elseif (is_tax()) { //for custom post types
        $title = sprintf(__('%1$s'), single_term_title('', false));
    } elseif (is_post_type_archive()) {
        $title = post_type_archive_title('', false);
    }
    return $title;
});
 
function everest_courses_related($course_id, $title, $subtitle){
    $posts_per_page = is_archive() ? -1 : 4;
	$related_args = array(
		'post_type' => 'everest-courses',
		'posts_per_page' => $posts_per_page, // Change to desired number of related courses
		'tax_query' => array(
			array(
				'taxonomy' => 'courses_category',
				'field' => 'term_id',
				'terms' => [$course_id],
				'operator' => 'IN',
			),
		),
		'post__not_in' => array( $course_id ), // Exclude the current course
	);
	
	$related_query = new WP_Query( $related_args );
	?>
        <!--Start Related Courses Area-->
		<?php if ( $related_query->have_posts() ) : ?>
        <section class="related-courses-area">
            <div class="container">
				<?php if($title) : ?>
                <div class="inner-title text-center">
                    <h2 class="mb-4"><?php echo $title; ?></h2>
					<span><?php echo strip_tags($subtitle); ?></span>
                </div>
				<?php endif; ?>
                <div class="row">
                 <?php 
					while ( $related_query->have_posts() ) : $related_query->the_post();
						// Display related course info here
						$terms = get_the_terms( get_the_ID(), 'courses_category' );
						$cat = $cat_link ='';
						if( !empty( $terms ) ) {
							$cat = $terms[0]->name;
							$cat_link = get_term_link( $terms[0]->term_id, 'courses_category');
						}
						$excerpt = wpautop(carbon_get_the_post_meta('excerpt'));
						$trimmedExcerpt = wp_trim_words($excerpt, 25, '');
				 ?>
                    <!--Start Single Online Courses Style2-->
                    <div class="col-xl-4 col-lg-6 col-md-6 h-100">
					<div class="single-instructors-style1">
                            <div class="img-holder">
                                <div class="inner2">
									<a href="<?php the_permalink(); ?>">
										<?php the_post_thumbnail('full'); ?>
									</a>
                                </div>
                            </div>
                            <div class="text-holder">
                                <h3>
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h3>
                                <div class="instructors-info">
									   <?php echo $excerpt; ?>
                                </div>
                            </div>
							<div>
								<a class="btn-one" href="<?php the_permalink(); ?>">
									<span class="txt">LEARN MORE</span>
								</a>
							</div>
                        </div>
                    </div>
                    <!--End Single Online Courses Style2-->
				<?php  endwhile; ?>
                </div>
            </div>
        </section>
        <!--End Related Courses Area-->
        <?php endif;
			wp_reset_postdata(); ?>   
	<?php
}


/**
 * Filters the list of allowed block types in the block editor.
 *
 * This function restricts the available block types to Heading, List, Image, and Paragraph only.
 *
 * @param array|bool $allowed_block_types Array of block type slugs, or boolean to enable/disable all.
 * @param object     $block_editor_context The current block editor context.
 *
 * @return array The array of allowed block types.
 */
function everest_allowed_block_types( $allowed_block_types, $block_editor_context ) {

	$allowed_block_types = array(
		'core/heading',   
		'core/paragraph',
		'carbon-fields/xbees-site-frame' ,
		'carbon-fields/xbees-slider ',
		'carbon-fields/xbees-essentials', 
		'carbon-fields/xbees-explore-future' ,
		'carbon-fields/xbees-fact-counter', 
		'carbon-fields/xbees-call-us ',
		'carbon-fields/xbees-testimonial' ,
		'carbon-fields/xbees-partner ',
		'carbon-fields/xbees-book-consultation', 
		'carbon-fields/xbees-courses', 
		'carbon-fields/xbees-working-process' ,
		'carbon-fields/xbees-testimonial-2', 
		'carbon-fields/xbees-slogan', 
		'carbon-fields/xbees-why-us', 
		'carbon-fields/xbees-faq' ,
		'carbon-fields/xbees-post-content' ,
		'carbon-fields/xbees-statements', 
		'carbon-fields/xbees-single-course', 
		'carbon-fields/xbees-archive-course', 
		'carbon-fields/xbees-single-evente', 
		'carbon-fields/xbees-post-archive' ,
		'carbon-fields/xbees-contact',   
		'carbon-fields/xbees-button',
	);

	return $allowed_block_types;
}
add_filter( 'allowed_block_types_all', 'everest_allowed_block_types', 10, 2 );


function everest_add_file_types_to_uploads($file_types){
	$new_filetypes = array();
	$new_filetypes['svg'] = 'image/svg+xml';
	$file_types = array_merge($file_types, $new_filetypes );
	return $file_types;
	}
add_filter('upload_mimes', 'everest_add_file_types_to_uploads');