<?php 

use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_archive_evente' );
function everest_archive_evente() {
Block::make( __( 'Xbees post Archive' ) )
	->add_fields( array() )
    // ->set_mode( 'preview' ) 
	// ->set_inner_blocks( true )
    // ->set_inner_blocks_template( array(
	// 	array( 'carbon-fields/col' ),
	// 	array( 'carbon-fields/col' ),
	// ) )
	->set_render_callback( function ($field, $atts, $inner_blocks_content, $id) {
  
        ?>
        <section class="events-page-three">
            <div class="container">
            <?php if (have_posts()) :
                echo '<div class="row">'; 
                    while (have_posts()) : the_post(); 
                    $date = carbon_get_post_meta(get_the_ID(), 'date');
                    $start_time = carbon_get_post_meta(get_the_ID(), 'start_time');
                    $end_time = carbon_get_post_meta(get_the_ID(), 'end_time');
                    $location = !empty(carbon_get_post_meta(get_the_ID(), 'location')) ? carbon_get_post_meta(get_the_ID(), 'location') : 'EVEREST, UK';
                    $organizer = !empty(carbon_get_post_meta(get_the_ID(), 'organizer')) ? carbon_get_post_meta(get_the_ID(), 'organizer') : 'Everest';
                    $phone = !empty(carbon_get_post_meta(get_the_ID(), 'phone')) ? carbon_get_post_meta(get_the_ID(), 'phone') : '+201200433432 +201019111669';
                    $email = !empty(carbon_get_post_meta(get_the_ID(), 'email')) ? carbon_get_post_meta(get_the_ID(), 'email') : '<EMAIL>';
                
                    if ( is_post_type_archive( 'everest-eventes' ) ) {
                        include('block-content/evente-archive.php');
                      } else {
                        include('block-content/evente.php');
                      }
                    endwhile;
                echo '</div>';     
                else : 
            ?>
            <p>No posts found.</p>
            <?php endif; ?>
            </div>
        </section>
        <?php
	} );
}