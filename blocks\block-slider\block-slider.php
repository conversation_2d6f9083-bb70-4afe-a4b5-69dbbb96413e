<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_slider' );
function everest_block_slider() {
Block::make( __( 'Xbees slider' ) )
	->add_fields( array(
        Field::make( 'select', 'slider_style', __( 'slider Style' ) )
        ->set_options( array(
            '1' => __( 'slider style 1' ),
            '2' => __( 'slider style 2' ),
            // '3' => __( 'slider style 3' ),
        ) ),
        Field::make( 'textarea', 'sidenote', __( 'Sidenote Content' ) ),
        Field::make( 'complex', 'slider', __( 'Slider Options' ) )
            ->add_fields( array(
                Field::make( 'image', 'slider-bg', __( 'Slider Background Image' ) )
                ->set_value_type( 'url' ),
                Field::make( 'textarea', 'big_title', __( 'Big Title' ) ),
                // Field::make( 'text', 'btn-text', __( 'Button 1 Text' ) ),
		        // Field::make( 'text', 'btn-url', __( 'Button 1 Url' ) ),
                // Field::make( 'text', 'btn-text-2', __( 'Button 2 Text' ) ),
		        // Field::make( 'text', 'btn-url-2', __( 'Button 2 Url' ) ),
                // Field::make( 'text', 'ytp-url', __( 'Youtube viedo Url [style 1]' ) ),
                // Field::make( 'text', 'ytp-text', __( 'Youtube title [style 1]' ) ),
            ) )
        ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
        switch ($fields['slider_style']) {
            case '1':
                include 'slider-1.php';
                break;
            case '2':
                include 'slider-2.php';
                break;
            case '3':
                include 'slider-3.php';
                break;
            default:
                include 'slider-1.php';
                break;
        }

	} );
}