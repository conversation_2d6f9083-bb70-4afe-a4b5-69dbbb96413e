msgid ""
msgstr ""
"Project-Id-Version: Carbon Fields\n"
"POT-Creation-Date: 2019-01-02 15:20+0200\n"
"PO-Revision-Date: 2020-11-05 15:38+0200\n"
"Last-Translator: <PERSON><PERSON> <ni<PERSON><PERSON>@convey.dk>\n"
"Language-Team: \n"
"Language: da_DK\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.4.1\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: languages\n"
"X-Poedit-SearchPathExcluded-2: node_modules\n"
"X-Poedit-SearchPathExcluded-3: tests\n"
"X-Poedit-SearchPathExcluded-4: tmp\n"
"X-Poedit-SearchPathExcluded-5: vendor\n"

#: core/Container/Block_Container.php:259
msgid "'render_callback' is required for the blocks."
msgstr ""

#: core/Container/Block_Container.php:263
msgid "'render_callback' must be a callable."
msgstr ""

#: core/Container/Container.php:712
msgid "General"
msgstr "Generelt"

#: core/Container/Theme_Options_Container.php:207
msgid "Settings saved."
msgstr "Indstillinger gemt."

#: core/Field/Footer_Scripts_Field.php:20
msgid ""
"If you need to add scripts to your footer (like Google Analytics tracking "
"code), you should enter them in this box."
msgstr ""
"Hvis du har brug at tilføje scripts til din sidefod (ligesom Google "
"Analytics sporingskode), skal du angive dem i denne boks."

#: core/Field/Gravity_Form_Field.php:47
msgid "No form"
msgstr "Ingen formular"

#: core/Field/Header_Scripts_Field.php:20
msgid "If you need to add scripts to your header, you should enter them here."
msgstr ""
"Hvis du har brug at tilføje scripts til din header, skal du indtaste dem her."

#: core/Field/Sidebar_Field.php:74
msgctxt "sidebar"
msgid "Add New"
msgstr "Tilføj Ny"

#: core/Helper/Helper.php:612
msgid "F j, Y"
msgstr "F j, Y"

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:69
msgid "Please enter a name for the sidebar."
msgstr "Indtast venligst et navn til sidebar."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:72
msgid "Unknown action attempted."
msgstr "Ukendt handling forsøgt."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:102
msgid "Sidebar with the same ID is already registered."
msgstr "Sidebar med samme ID er allerede registreret."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:112
#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:142
msgid ""
"Failed to update option storing your custom sidebars. Please contact support."
msgstr ""
"Kunne ikke opdatere mulighed for opbevaring af din brugerdefinerede sidebar. "
"Kontakt venligst support."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:137
msgid "Sidebar not found."
msgstr "Sidebaren findes ikke."

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:181
msgid "Add Sidebar"
msgstr "Tilføj Sidebar"

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:182
msgid "Please enter the name of the new sidebar:"
msgstr "Indtast venligst et navn til sidebaren:"

#: core/Libraries/Sidebar_Manager/Sidebar_Manager.php:183
msgid "Are you sure you wish to remove this sidebar?"
msgstr "Er du sikker på, at du vil fjerne denne sidebar?"

#: core/REST_API/Router.php:341
msgid "No option names provided"
msgstr "Navn for indstillinger mangles"

#: core/REST_API/Router.php:352
msgid "Theme Options updated."
msgstr "Tema indstillinger opdateret."

#: templates/Container/common/options-page.php:43
msgid "Actions"
msgstr "Handlinger"

#: templates/Container/common/options-page.php:52
msgid "Save Changes"
msgstr "Gem ændringer"

#: templates/Container/widget.php:4
msgid "No options are available for this widget."
msgstr "Der er ingen indstillinger for denne widget."
