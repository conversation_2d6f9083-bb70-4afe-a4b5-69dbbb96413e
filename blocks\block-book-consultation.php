<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_book_consultation' );
function everest_book_consultation() {
Block::make( __( 'Xbees Book Consultation' ) )
	->add_fields( array(
        Field::make( 'text', 'title', __( 'Title' ) ),
        Field::make( 'text', 'btn_title', __( 'Button Title' ) ),
        Field::make( 'text', 'btn_url', __( 'Button Url' ) ),

        ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $title = !empty($fields['title']) ? $fields['title'] : '<span>Register!..</span> to study in Next Academic Year 2023.';
        $btn_title = !empty($fields['btn_title']) ? $fields['btn_title'] : 'Book Free Consultation';
        $btn_url = !empty($fields['btn_url']) ? $fields['btn_url'] : '#';
       
      ?>
        <!--Start Slogan Style1 Area-->
        <section class="slogan-style1-area">
            <div class="auto-container">
                <div class="slogan-style1">
                    <div class="container">
                        <div class="slogan-style1__inner">
                            <div class="button-box">
                                <a class="btn-one" href="<?php echo $btn_url; ?>"><span class="txt"><?php echo $btn_title; ?></span></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--End Slogan Style1 Area-->
<?php
	} );
}