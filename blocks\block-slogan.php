<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_slogan' );
function everest_block_slogan() {
Block::make( __( 'Xbees Slogan' ) )
	->add_fields( array(
        Field::make( 'text', 'title', __( 'box 1 Title' ) ),
        Field::make( 'text', 'subtext', __( 'box 1 Subtitle' ) ),
        Field::make( 'image', 'image', __( 'box 1 Background Image' ) )
        ->set_value_type( 'url' ),
        Field::make( 'text', 'btnurl', __( 'box 1 Buton Url' ) ),
        Field::make( 'text', 'btntext', __( 'box 1 Buton Url' ) ),

        Field::make( 'text', 'title2', __( 'box 2 Title' ) ),
        Field::make( 'text', 'subtext2', __( 'box 2 Subtitle' ) ),
        Field::make( 'image', 'image2', __( 'box 2 Background Image' ) )
        ->set_value_type( 'url' ),
        Field::make( 'text', 'btnurl2', __( 'box 2 Buton Url' ) ),
        Field::make( 'text', 'btntext2', __( 'box 2 Buton text' ) ),


    ) )

    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $title = !empty($fields['title']) ? $fields['title'] : 'Change Your<br> Life Through Education';
        $subtext = !empty($fields['subtext']) ? $fields['subtext'] : 'Think Education';
        $image = !empty($fields['image']) ? $fields['image'] : 'https://st.ourhtmldemo.com/new/educamb/assets/images/quiz-learning/quiz-learning-slogan-one-bg.jpg';
        $btnurl = !empty($fields['btnurl']) ? $fields['btnurl'] : '/contact';
        $btntext = !empty($fields['btntext']) ? $fields['btntext'] : 'Explore Now';
       
        $title2 = !empty($fields['title2']) ? $fields['title2'] : 'Chat With<br> Our Profesionals Directly';
        $subtext2 = !empty($fields['subtext2']) ? $fields['subtext2'] : 'Need Help?';
        $image2 = !empty($fields['image2']) ? $fields['image2'] : 'https://st.ourhtmldemo.com/new/educamb/assets/images/quiz-learning/quiz-learning-slogan-one-bg-2.jpg';
        $btnurl2 = !empty($fields['btnurl2']) ? $fields['btnurl2'] : '/contact';
        $btntext2 = !empty($fields['btntext2']) ? $fields['btntext2'] : 'Try It Now';

     
     ?>
<!--Start Quiz Learning  Slogan Area-->
<section class="quiz-learning-slogan-area">
    <div class="auto-container">
        <div class="row">

            <div class="col-xl-6">
                <div class="quiz-learning-slogan-content-one">
                    <div class="quiz-learning-slogan-content-one__bg"
                        style="background-image: url(<?php echo $image; ?>);">
                    </div>
                    <div class="quiz-learning-slogan-content-one__inner">
                        <div class="sec-title-style3">
                            <div class="sub-title">
                                <h5><?php echo $subtext; ?></h5>
                            </div>
                            <h2><?php echo $title; ?></h2>
                        </div>
                        <div class="btns-box">
                            <a class="btn-one btn-one--style4" href="<?php echo $btnurl; ?>">
                                <span class="txt">
                                <?php echo $btntext; ?>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-6">
                <div class="quiz-learning-slogan-content-one quiz-learning-slogan-content-one--style2">
                    <div class="quiz-learning-slogan-content-one__bg"
                        style="background-image: url(<?php echo $image2; ?>);">
                    </div>
                    <div class="quiz-learning-slogan-content-one__inner">
                        <div class="sec-title-style3">
                            <div class="sub-title">
                                <h5><?php echo $subtext2; ?></h5>
                            </div>
                            <h2><?php echo $title2; ?></h2>
                        </div>
                        <div class="btns-box">
                            <a class="btn-one btn-one--style4" href="<?php echo $btnurl2; ?>">
                                <span class="txt">
                                <?php echo $btntext2; ?>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</section>
<!--End Quiz Learning Slogan Area-->
<?php
	} );
}