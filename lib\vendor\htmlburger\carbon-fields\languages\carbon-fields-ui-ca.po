msgid ""
msgstr ""
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ca_ES\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.4.1\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: packages/blocks/components/block-edit/index.js:152
msgid "Show preview"
msgstr "Mostrar previsualització"

#: packages/blocks/components/block-edit/index.js:153
msgid "Hide preview"
msgstr "Ocultar previsualització"

#: packages/blocks/components/block-edit/index.js:169
msgid "Fields"
msgstr "Camps"

#: packages/blocks/components/not-supported-field/index.js:14
msgid "Field of type '%s' is not supported in Gutenberg."
msgstr "El camp de tipus '%s' no és suportat pel Gutenberg."

#: packages/blocks/fields/datetime/index.js:14
#: packages/core/fields/date/index.js:15
#: packages/metaboxes/fields/datetime/index.js:9
msgid "Select Date"
msgstr "Seleccionar Data"

#: packages/blocks/fields/file/index.js:16
#: packages/metaboxes/fields/file/index.js:11
msgid "Use File"
msgstr "Utilitzar Fitxer"

#: packages/blocks/fields/file/index.js:17
#: packages/metaboxes/fields/file/index.js:12
msgid "Select File"
msgstr "Seleccionar Fitxer"

#: packages/blocks/fields/image/index.js:11
#: packages/metaboxes/fields/image/index.js:11
msgid "Use Image"
msgstr "Utilitzar Imatge"

#: packages/blocks/fields/image/index.js:12
#: packages/metaboxes/fields/image/index.js:12
msgid "Select Image"
msgstr "Seleccionar Imatge"

#: packages/blocks/fields/media-gallery/index.js:16
#: packages/metaboxes/fields/media-gallery/index.js:16
msgid "Use Attachments"
msgstr "Utilitzar Adjunts"

#: packages/blocks/fields/media-gallery/index.js:17
#: packages/metaboxes/fields/media-gallery/index.js:17
msgid "Select Attachments"
msgstr "Seleccionar Adjunts"

#: packages/core/components/no-options/index.js:14
msgid "No options."
msgstr "Sense opcions."

#: packages/core/components/search-input/index.js:30
msgid "Search..."
msgstr "Cercar..."

#: packages/core/fields/association/index.js:113
msgid "Maximum number of items reached (%s items)"
msgstr "S'ha arribat al nombre màxim d'ítems (%s ítems)"

#: packages/core/fields/association/index.js:204
msgid "Showing %1$d of %2$d results"
msgstr "Mostrant %1$d de %2$d resultats"

#: packages/core/fields/association/index.js:380
msgid "An error occurred while trying to fetch association options."
msgstr "S'ha produït un error intentant obtenir les opcions de l'associació."

#: packages/core/fields/association/index.js:430
#: packages/core/fields/complex/index.js:394
#: packages/core/hocs/with-validation/required.js:20
msgid "This field is required."
msgstr "Aquest camp és obligatori."

#: packages/core/fields/association/index.js:434
msgid "Minimum number of items not reached (%s items)"
msgstr "No s'ha arribat al nombre d'ítems mínim (%s ítems)"

#: packages/core/fields/color/index.js:86
msgid "Select a color"
msgstr "Seleccionar un color"

#: packages/core/fields/complex/group.js:150
msgid "Duplicate"
msgstr "Duplicar"

#: packages/core/fields/complex/group.js:159
msgid "Remove"
msgstr "Eliminar"

#: packages/core/fields/complex/group.js:168
msgid "Collapse"
msgstr "Plegar"

#: packages/core/fields/complex/index.js:145
msgid "Couldn't create the label of group - %s"
msgstr "No s'ha pogut crear l'etiqueta del grup - %s"

#: packages/core/fields/complex/index.js:367
msgid "Expand All"
msgstr "Desplegar Tot"

#: packages/core/fields/complex/index.js:367
msgid "Collapse All"
msgstr "Plegar Tot"

#: packages/core/fields/complex/index.js:401
msgid "Minimum number of rows not reached (%1$d %2$s)"
msgstr "No s'ha arribat al nombre d'ítems mínim (%s ítems)"

#: packages/core/fields/complex/index.js:81
msgid "Add %s"
msgstr "Afegeix %s"

#: packages/core/fields/map/index.js:165
msgid "The address could not be found."
msgstr "No s'ha trobat l'adreça."

#: packages/core/fields/map/index.js:167
msgid "Geocode was not successful for the following reason: "
msgstr "El Geocode no ha funcionat correctament degut al següent motiu: "

#: packages/core/fields/map/index.js:185
msgid "Error alert"
msgstr "Alerta d'error"

#: packages/core/fields/oembed/index.js:188
msgid "An error occurred while trying to fetch oembed preview."
msgstr "S'ha produït un error intentant obtenir la previsualització de l'oembed."

#: packages/core/fields/oembed/index.js:203
msgid "Not Found"
msgstr "No s'ha trobat"

#: packages/core/fields/rich-text/index.js:100
msgid "Text"
msgstr "Text"

#: packages/core/fields/rich-text/index.js:96
msgid "Visual"
msgstr "Visual"

#: packages/core/fields/sidebar/index.js:110
msgid "Please enter the name of the new sidebar:"
msgstr "Introduïu el nom de la nova sidebar:"

#: packages/core/fields/sidebar/index.js:128
msgid "An error occurred while trying to create the sidebar."
msgstr "S'ha produït un error intentant crear la sidebar."

#: packages/core/fields/sidebar/index.js:58
msgid "Please choose"
msgstr "Escolliu"

#: packages/core/fields/time/index.js:16
msgid "Select Time"
msgstr "Seleccionar Hora"

#: packages/core/hocs/with-conditional-logic/index.js:62
msgid "An unknown field is used in condition - \"%s\""
msgstr "S'està utilitzant un camp desconegut a la condició - \"%s\""

#: packages/core/registry/index.js:40
msgid "%1$s type must be a string."
msgstr "El tipus de %1$s ha de ser una cadena (string)."

#: packages/core/registry/index.js:46
msgid "%1$s %2$s is already registered."
msgstr "%1$s %2$s ja està registrat."

#: packages/core/registry/index.js:54
msgid "The \"component\" param must be a function."
msgstr "El paràmetre \"component\" ha de ser una funció."

#: packages/core/registry/index.js:79
msgid "The provided context isn't a valid one. Must be one of - %s ."
msgstr "El context proporcionat no és vàlid. Ha de ser un de - %s."

#: packages/core/registry/index.js:89
msgid "%s %s isn't registered."
msgstr "%s %s no està registrat."

#: packages/core/utils/api-fetch.js:19
msgid "An error occured."
msgstr "S'ha produït un error."

#: packages/core/utils/fetch-attachments-data.js:23
msgid "An error occurred while trying to fetch files data."
msgstr "S'ha produït un error intentant obtenir les dades dels fitxers."

#: packages/metaboxes/containers/index.js:52
msgid "Could not find DOM element for container \"%1$s\"."
msgstr "No s'ha pogut trobat l'element DOM pel contenidor \"%1$s\"."

#: packages/metaboxes/monitors/conditional-display/conditions/base.js:52
msgid "Unsupported container condition comparison operator used - \"%1$s\"."
msgstr "S'està utilitzant un operador de comparació de condició de contenidor no suportat - \"%1$s\"."

#: packages/metaboxes/monitors/conditional-display/handler/index.js:55
msgid "Unsupported container condition - \"%1$s\"."
msgstr "Condició de contenidor no suportada - \"%1$s\"."

#: packages/metaboxes/monitors/conditional-display/handler/index.js:73
msgid "Unsupported container condition relation used - \"%1$s\"."
msgstr "S'està utilitzant una relació de condició de contenidor no suportada - \"%1$s\"."
