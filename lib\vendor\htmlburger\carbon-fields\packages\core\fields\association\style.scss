/* ==========================================================================
   Association
   ========================================================================== */

.container-carbon_fields_container_word_settings {
	min-width: 0;
	max-width: 100%;
	width: 100%;
}

.cf-container .cf-field {
	max-width: 100%;
}

.cf-association__bar {
	position: relative;
	z-index: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-direction: row;
	border-color: $wp-color-gray-light-500;
	border-style: solid;
	border-width: 1px;

	.cf-search-input {
		flex: 1 1 auto;
	}

	.cf-search-input__inner {
		border: 0;
		box-shadow: none;

		&:focus {
			border-color: none;
			box-shadow: none;
			outline: none;
		}
	}

	&:focus-within {
		border-color: #5b9dd9;
		box-shadow: 0 0 2px rgba( 30, 140, 190, 0.8 );
		outline: 2px solid transparent;
	}
}

.cf-association__counter {
	font-size: 12px;
	color: $wp-color-dark-gray;
	pointer-events: none;
	margin-right: 10px;
	margin-left: 5px;
}

.cf-association__spinner {
	float: none;
	margin: 0;
	margin-left: 5px;
}

.cf-association__cols {
	background: #fff;
	position: relative;
	z-index: 0;
	display: flex;
	border-width: 0 1px 1px;
	border-style: solid;
	border-color: $wp-color-gray-light-500;

	&::before {
		position: absolute;
		top: 0;
		left: 50%;
		width: 1px;
		height: 100%;
		background-color: $wp-color-gray-light-500;
		content: '';
	}
}

.cf-association__col {
	width: 50%;
	max-height: 160px;
	overflow-y: auto;

	&.ui-sortable .cf-association__option-title {
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.edit-post-sidebar .cf-association__cols & {
		width: 100%;

		&:first-child {
			border-bottom: 3px solid $wp-color-dark-gray;
		}
	}
}

.cf-association__option {
	display: flex;
	align-items: center;
	padding: 4px 8px;
	height: 32px;
	box-sizing: border-box;

	&--selected {
		background-color: $wp-color-gray-light-100;
	}

	& + & {
		border-top: 1px solid $wp-color-gray-light-500;
	}

	&.ui-sortable-helper {
		border-top: 0;
		background-color: $wp-color-gray-light-100;
	}
}

.cf-association__option-thumb {
	flex: none;
	display: block;
	width: 24px;
	height: 24px;
	margin-right: 8px;
}

.cf-association__option-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex: 1;
	min-width: 0;
	margin-right: 8px;
}

.cf-association__option-title {
	flex: 1;
	position: relative;
	margin-right: $size-base;

	.cf-association__option--selected & {
		color: $wp-color-dark-silver-gray;
	}
}

.cf-association__option-title-inner {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	font-size: $wp-font-size;
	line-height: $wp-line-height;
	color: $wp-color-base-gray;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	transform: translateY(-50%);
}

.cf-association__option-type {
	font-size: 9px;
	line-height: 1;
	text-transform: uppercase;
	color: $wp-color-dark-silver-gray;

	.edit-post-sidebar .cf-association__col & {
		display: none;
	}
}

.cf-association__option-actions {
	display: flex;
	align-items: center;
	justify-content: space-between;

	button {
		margin-left: 8px;
	}
}

.cf-association__option-sort {
	margin-right: 4px;
	color: $wp-color-dark-silver-gray;
	cursor: move;
}

.cf-association__option-action {
	padding: 0;
	border: 0;
	outline: none;
	color: $wp-color-dark-silver-gray;
	transition: color $transition-base;
	cursor: pointer;
	background: transparent;

	&:focus {
		color: $wp-color-dark-silver-gray;
		box-shadow: none;
	}

	&:hover {
		color: $wp-color-dark-gray;
	}
}
