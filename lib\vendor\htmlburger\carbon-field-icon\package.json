{"name": "carbon-field-icon", "version": "3.1.0", "scripts": {"start": "npm run development -- --watch", "build": "npm run development && npm run production", "development": "cross-env NODE_ENV=development webpack --mode development", "production": "cross-env NODE_ENV=production webpack --mode production"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-syntax-async-generators": "^7.0.0", "@babel/plugin-transform-runtime": "^7.1.0", "@babel/preset-env": "^7.1.0", "@babel/preset-react": "^7.0.0", "@wordpress/babel-plugin-makepot": "^2.1.2", "@wordpress/browserslist-config": "^2.2.2", "autoprefixer": "^9.3.1", "babel-loader": "^8.0.2", "babel-plugin-module-resolver": "^3.1.1", "cross-env": "^5.2.0", "css-loader": "^1.0.0", "mini-css-extract-plugin": "^0.4.4", "node-sass": "^4.9.4", "optimize-css-assets-webpack-plugin": "^5.0.1", "postcss-loader": "^3.0.0", "postcss-preset-env": "^6.2.0", "sass-loader": "^7.1.0", "sass-resources-loader": "^2.0.0", "terser-webpack-plugin": "^1.1.0", "webpack": "^4.27.1", "webpack-cli": "^3.1.1"}, "dependencies": {"@babel/runtime": "^7.1.5", "@wordpress/compose": "^3.0.0", "@wordpress/data": "^4.0.1", "@wordpress/element": "^2.1.8", "@wordpress/hooks": "^2.0.3", "@wordpress/i18n": "^3.1.0", "classnames": "^2.2.6", "lodash": "^4.17.11", "object-path-immutable": "^0.5.1", "react": "^16.6.3", "react-dom": "^16.6.3", "refract-callbag": "^4.1.2", "reselect": "^3.0.0"}}