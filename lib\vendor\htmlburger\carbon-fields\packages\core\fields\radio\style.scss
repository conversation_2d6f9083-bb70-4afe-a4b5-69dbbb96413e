/* ==========================================================================
   Radio
   ========================================================================== */

.cf-radio__list {
	margin: 0;

	.cf-radio-image & {
		display: flex;
		flex-wrap: wrap;
	}
}

.cf-radio__list-item {
	&:last-child {
		margin-bottom: 0;
	}

	.cf-radio-image & {
		flex: 0 0 20%;
		position: relative;
		padding: 4px;
	}
}

.cf-radio__label {
	.cf-container-term-meta & {
		display: inline;
	}

	.cf-radio-image & {
		display: inline-block;
	}
}

.cf-radio__input {
	.cf-field & {
		margin-top: 0;
	}

	.cf-radio-image & {
		position: absolute;
		z-index: -1;
		top: 50%;
		left: 50%;
		width: 0;
		height: 0;
		margin: 0;
		outline: 0;
		opacity: 0;
	}

	&[type="checkbox"] {
		border-radius: 50%;
	}

	&[type="checkbox"]:checked:before {
		content: "";
		background-color: var(--wp-admin-theme-color, #3582c4);
		border-radius: 50%;
		width: 0.5rem;
		height: 0.5rem;
		margin: 0.1875rem;
		line-height: 1.14285714;
	}
}

.cf-radio-image__image {
	display: block;
	max-width: 100%;
	padding: 5px;
	box-sizing: border-box;
}
