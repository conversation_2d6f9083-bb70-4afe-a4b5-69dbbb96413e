/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Association
   ========================================================================== */
.container-carbon_fields_container_word_settings {
  min-width: 0;
  max-width: 100%;
  width: 100%;
}

.cf-container .cf-field {
  max-width: 100%;
}

.cf-association__bar {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  border-color: #e2e4e7;
  border-style: solid;
  border-width: 1px;
}
.cf-association__bar .cf-search-input {
  flex: 1 1 auto;
}
.cf-association__bar .cf-search-input__inner {
  border: 0;
  box-shadow: none;
}
.cf-association__bar .cf-search-input__inner:focus {
  border-color: none;
  box-shadow: none;
  outline: none;
}
.cf-association__bar[focus-within] {
  border-color: #5b9dd9;
  box-shadow: 0 0 2px rgba(30, 140, 190, 0.8);
  outline: 2px solid transparent;
}
.cf-association__bar:focus-within {
  border-color: #5b9dd9;
  box-shadow: 0 0 2px rgba(30, 140, 190, 0.8);
  outline: 2px solid transparent;
}

.cf-association__counter {
  font-size: 12px;
  color: #23282d;
  pointer-events: none;
  margin-right: 10px;
  margin-left: 5px;
}

.cf-association__spinner {
  float: none;
  margin: 0;
  margin-left: 5px;
}

.cf-association__cols {
  background: #fff;
  position: relative;
  z-index: 0;
  display: flex;
  border-width: 0 1px 1px;
  border-style: solid;
  border-color: #e2e4e7;
}
.cf-association__cols::before {
  position: absolute;
  top: 0;
  left: 50%;
  width: 1px;
  height: 100%;
  background-color: #e2e4e7;
  content: "";
}

.cf-association__col {
  width: 50%;
  max-height: 160px;
  overflow-y: auto;
}
.cf-association__col.ui-sortable .cf-association__option-title {
  white-space: nowrap;
  text-overflow: ellipsis;
}
.edit-post-sidebar .cf-association__cols .cf-association__col {
  width: 100%;
}
.edit-post-sidebar .cf-association__cols .cf-association__col:first-child {
  border-bottom: 3px solid #23282d;
}

.cf-association__option {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  height: 32px;
  box-sizing: border-box;
}
.cf-association__option--selected {
  background-color: #fbfbfc;
}
.cf-association__option + .cf-association__option {
  border-top: 1px solid #e2e4e7;
}
.cf-association__option.ui-sortable-helper {
  border-top: 0;
  background-color: #fbfbfc;
}

.cf-association__option-thumb {
  flex: none;
  display: block;
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.cf-association__option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  min-width: 0;
  margin-right: 8px;
}

.cf-association__option-title {
  flex: 1;
  position: relative;
  margin-right: 4px;
}
.cf-association__option--selected .cf-association__option-title {
  color: #82878c;
}

.cf-association__option-title-inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  font-size: 13px;
  line-height: 1.4;
  color: #32373c;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  transform: translateY(-50%);
}

.cf-association__option-type {
  font-size: 9px;
  line-height: 1;
  text-transform: uppercase;
  color: #82878c;
}
.edit-post-sidebar .cf-association__col .cf-association__option-type {
  display: none;
}

.cf-association__option-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.cf-association__option-actions button {
  margin-left: 8px;
}

.cf-association__option-sort {
  margin-right: 4px;
  color: #82878c;
  cursor: move;
}

.cf-association__option-action {
  padding: 0;
  border: 0;
  outline: none;
  color: #82878c;
  transition: color 0.1s linear;
  cursor: pointer;
  background: transparent;
}
.cf-association__option-action:focus {
  color: #82878c;
  box-shadow: none;
}
.cf-association__option-action:hover {
  color: #23282d;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Search Input
   ========================================================================== */
.cf-search-input {
  position: relative;
}
.cf-search-input::before {
  position: absolute;
  top: 50%;
  left: 9px;
  margin-top: -10px;
}
.postbox .cf-search-input::before {
  color: #32373c;
}
.wp-block .cf-search-input::before {
  color: #555d66;
}

.cf-search-input__inner {
  display: block;
  width: 100%;
  margin: 0;
}
.cf-container .cf-search-input__inner, .block-editor .cf-field .cf-search-input__inner {
  padding-left: 35px;
}
.cf-container .cf-search-input__inner {
  padding-top: 8px;
  padding-bottom: 8px;
  border-color: #e2e4e7;
}
.block-editor .cf-container .cf-search-input__inner, .wp-block .cf-field .cf-search-input__inner, .edit-post-sidebar .cf-block__fields .cf-search-input__inner {
  border-radius: 0;
  border: 0;
}
.block-editor .cf-container .cf-search-input__inner:focus, .wp-block .cf-field .cf-search-input__inner:focus, .edit-post-sidebar .cf-block__fields .cf-search-input__inner:focus {
  box-shadow: none;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Checkbox
   ========================================================================== */
.cf-field .cf-checkbox__input {
  margin-top: 0;
}

.cf-checkbox__label {
  font-size: 13px;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Color
   ========================================================================== */
.cf-color__inner {
  display: flex;
  align-items: center;
}

.cf-color__toggle {
  position: relative;
  overflow: hidden;
}

.cf-color__toggle-text {
  margin-left: 27px;
}

.cf-color__preview {
  position: absolute;
  top: 0;
  left: 0;
  width: 26px;
  height: 100%;
  border-right: 1px solid #ccc;
}
.cf-color__toggle:hover .cf-color__preview, .cf-color__toggle:active .cf-color__preview {
  border-color: #999;
}

.cf-color .cf-color__reset {
  margin-left: 5px;
  text-decoration: none;
}
.cf-color .cf-color__reset:focus {
  box-shadow: none;
}

.cf-color__picker {
  position: absolute;
  z-index: 9999;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Complex
   ========================================================================== */
.cf-complex__groups {
  flex: 1;
  position: relative;
}
.cf-complex--tabbed-vertical > .cf-complex__groups {
  flex: 0 0 80%;
}

.cf-complex__group {
  box-sizing: border-box;
}
.cf-complex--grid .cf-complex__group {
  position: relative;
  margin-bottom: 12px;
}
.cf-complex--grid .cf-complex__group:last-child {
  margin-bottom: 0;
}

.cf-complex__group-placeholder {
  position: relative;
}
.cf-complex__group-placeholder:not(:last-child) {
  margin-bottom: 12px;
}
.cf-complex__group-placeholder::before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px dashed #e2e4e7;
  box-sizing: border-box;
  content: "";
}

/**
 * Head
 */
.cf-complex__group-head {
  position: relative;
  display: flex;
  border: 1px solid #e2e4e7;
  border-bottom: 0;
  background-color: #fbfbfc;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 13px;
  font-weight: 600;
  line-height: 1.4;
  color: #23282d;
  cursor: move;
  transition: border-color 0.1s linear;
}
.cf-complex__group-head:hover {
  border-color: #82878c;
}

.cf-complex__group-index {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 42px;
  border-right: 1px solid #e2e4e7;
}

.cf-complex__group-title {
  display: flex;
  align-items: center;
  padding: 0 12px;
}

/**
 * Body
 */
.cf-complex__group-body {
  display: flex;
  flex-wrap: wrap;
  border-width: 1px 0 1px 1px;
  border-style: solid;
  border-color: #e2e4e7;
  background-color: #fff;
}
.cf-complex__group-body[hidden] {
  display: none;
}

/**
 * Actions
 */
.cf-complex__actions {
  display: flex;
  align-items: center;
  margin-top: 12px;
}

.cf-complex__actions .cf-complex__toggler {
  margin-left: auto;
}

/**
 * Inserter
 */
.cf-complex__inserter {
  position: relative;
  display: inline-block;
}
.cf-complex__tabs .cf-complex__inserter {
  height: 36px;
}
.cf-complex__tabs--tabbed-horizontal .cf-complex__inserter {
  width: 36px;
  align-self: flex-end;
  margin-bottom: 4px;
}
.cf-complex__tabs--tabbed-vertical .cf-complex__inserter {
  display: block;
}

.cf-complex__tabs .cf-complex__inserter-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border: 1px solid #e2e4e7;
  border-radius: 0;
  margin: 0;
  background-color: #fbfbfc;
  box-shadow: none;
  font-size: 18px;
  line-height: 1;
  transition: background-color 0.1s linear;
}
.cf-complex__tabs .cf-complex__inserter-button:focus, .cf-complex__tabs .cf-complex__inserter-button:hover, .cf-complex__tabs .cf-complex__inserter-button:active {
  border-color: #e2e4e7;
  background-color: #fff;
}
.cf-complex__tabs .cf-complex__inserter-button:focus {
  box-shadow: none;
}
.cf-complex__tabs .cf-complex__inserter-button:active {
  box-shadow: none;
  transform: none;
}

.cf-complex__inserter-menu {
  position: absolute;
  top: 50%;
  left: 100%;
  min-width: 180px;
  margin: 0;
  transform: translate(10px, -50%);
  z-index: 1;
}

.cf-complex__inserter-item {
  padding: 8px 12px;
  margin: 0;
  cursor: pointer;
  transition: color 0.1s linear;
}

/**
 * Group - Actions
 */
.cf-complex__group-actions {
  display: flex;
  align-items: center;
}
.cf-complex__group-actions--grid {
  position: absolute;
  top: 12px;
  right: 12px;
}
.cf-complex__group-actions--tabbed {
  justify-content: flex-end;
  padding: 8px 12px;
  border-width: 0 1px 1px;
  border-style: solid;
  border-color: #e2e4e7;
  background-color: #fbfbfc;
}

.cf-complex__group-action {
  display: inline-flex;
  padding: 0;
  border: 0;
  margin-left: 12px;
  outline: none;
  background-color: transparent;
  color: #82878c;
  cursor: pointer;
  transition: color 0.1s linear;
}
.cf-complex__group-action:first-child {
  margin-left: 0;
}
.cf-complex__group-action:hover {
  color: #23282d;
}

.cf-complex__group-action-text {
  display: none;
}

/**
 * Tabs
 */
.cf-complex__tabs {
  position: relative;
  z-index: 1;
}
.cf-complex__tabs--tabbed-horizontal {
  display: inline-flex;
  margin-bottom: -5px;
}
.cf-complex__tabs--tabbed-vertical {
  flex: 0 0 20%;
  margin-right: -1px;
}

.cf-complex__tabs-list {
  margin: 0;
}
.cf-complex__tabs--tabbed-horizontal .cf-complex__tabs-list {
  display: flex;
  flex-wrap: wrap;
}
.cf-complex__tabs--tabbed-vertical .cf-complex__tabs-list {
  margin-bottom: -1px;
}

.cf-complex__tabs-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e2e4e7;
  margin: 0;
  background-color: #fbfbfc;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.1s linear, border-color 0.1s linear;
}
.cf-complex__tabs-item:hover {
  background-color: #fff;
}
.cf-complex__tabs-item--tabbed-horizontal {
  margin: 0 4px 4px 0;
}
.cf-complex__tabs-item--tabbed-vertical ~ .cf-complex__tabs-item--tabbed-vertical {
  border-top-width: 0;
}
.cf-complex__tabs-item--current {
  background-color: #fff;
}
.cf-complex__tabs-item--tabbed-horizontal.cf-complex__tabs-item--current {
  border-bottom-color: #fff !important;
}
.cf-complex__tabs-item--tabbed-vertical.cf-complex__tabs-item--current {
  border-right-color: #fff !important;
}

/**
 * Placeholder
 */
.cf-complex__placeholder-label.cf-complex__placeholder-label {
  margin: 8px 0 12px;
}
.cf-container-term-meta .cf-complex__placeholder-label {
  font-style: normal;
  color: inherit;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
.flatpickr-calendar {
  background: transparent;
  opacity: 0;
  display: none;
  text-align: center;
  visibility: hidden;
  padding: 0;
  -webkit-animation: none;
  animation: none;
  direction: ltr;
  border: 0;
  font-size: 14px;
  line-height: 24px;
  border-radius: 5px;
  position: absolute;
  width: 307.875px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  background: #fff;
  -webkit-box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08);
  box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08);
}

.flatpickr-calendar.open, .flatpickr-calendar.inline {
  opacity: 1;
  max-height: 640px;
  visibility: visible;
}

.flatpickr-calendar.open {
  display: inline-block;
  z-index: 99999;
}

.flatpickr-calendar.animate.open {
  -webkit-animation: fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);
  animation: fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);
}

.flatpickr-calendar.inline {
  display: block;
  position: relative;
  top: 2px;
}

.flatpickr-calendar.static {
  position: absolute;
  top: calc(100% + 2px);
}

.flatpickr-calendar.static.open {
  z-index: 999;
  display: block;
}

.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7) {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1) {
  -webkit-box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
  box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
}

.flatpickr-calendar .hasWeeks .dayContainer, .flatpickr-calendar .hasTime .dayContainer {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.flatpickr-calendar .hasWeeks .dayContainer {
  border-left: 0;
}

.flatpickr-calendar.hasTime .flatpickr-time {
  height: 40px;
  border-top: 1px solid #e6e6e6;
}

.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
  height: auto;
}

.flatpickr-calendar:before, .flatpickr-calendar:after {
  position: absolute;
  display: block;
  pointer-events: none;
  border: solid transparent;
  content: "";
  height: 0;
  width: 0;
  left: 22px;
}

.flatpickr-calendar.rightMost:before, .flatpickr-calendar.arrowRight:before, .flatpickr-calendar.rightMost:after, .flatpickr-calendar.arrowRight:after {
  left: auto;
  right: 22px;
}

.flatpickr-calendar.arrowCenter:before, .flatpickr-calendar.arrowCenter:after {
  left: 50%;
  right: 50%;
}

.flatpickr-calendar:before {
  border-width: 5px;
  margin: 0 -5px;
}

.flatpickr-calendar:after {
  border-width: 4px;
  margin: 0 -4px;
}

.flatpickr-calendar.arrowTop:before, .flatpickr-calendar.arrowTop:after {
  bottom: 100%;
}

.flatpickr-calendar.arrowTop:before {
  border-bottom-color: #e6e6e6;
}

.flatpickr-calendar.arrowTop:after {
  border-bottom-color: #fff;
}

.flatpickr-calendar.arrowBottom:before, .flatpickr-calendar.arrowBottom:after {
  top: 100%;
}

.flatpickr-calendar.arrowBottom:before {
  border-top-color: #e6e6e6;
}

.flatpickr-calendar.arrowBottom:after {
  border-top-color: #fff;
}

.flatpickr-calendar:focus {
  outline: 0;
}

.flatpickr-wrapper {
  position: relative;
  display: inline-block;
}

.flatpickr-months {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.flatpickr-months .flatpickr-month {
  background: transparent;
  color: rgba(0, 0, 0, 0.9);
  fill: rgba(0, 0, 0, 0.9);
  height: 34px;
  line-height: 1;
  text-align: center;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  overflow: hidden;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.flatpickr-months .flatpickr-prev-month, .flatpickr-months .flatpickr-next-month {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  text-decoration: none;
  cursor: pointer;
  position: absolute;
  top: 0;
  height: 34px;
  padding: 10px;
  z-index: 3;
  color: rgba(0, 0, 0, 0.9);
  fill: rgba(0, 0, 0, 0.9);
}

.flatpickr-months .flatpickr-prev-month.flatpickr-disabled, .flatpickr-months .flatpickr-next-month.flatpickr-disabled {
  display: none;
}

.flatpickr-months .flatpickr-prev-month i, .flatpickr-months .flatpickr-next-month i {
  position: relative;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month, .flatpickr-months .flatpickr-next-month.flatpickr-prev-month { /*
/*rtl:begin:ignore*/
  left: 0;
} /*
/*rtl:begin:ignore*/
/*
      /*rtl:end:ignore*/
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month, .flatpickr-months .flatpickr-next-month.flatpickr-next-month { /*
/*rtl:begin:ignore*/
  right: 0;
} /*
/*rtl:begin:ignore*/
/*
      /*rtl:end:ignore*/
.flatpickr-months .flatpickr-prev-month:hover, .flatpickr-months .flatpickr-next-month:hover {
  color: #959ea9;
}

.flatpickr-months .flatpickr-prev-month:hover svg, .flatpickr-months .flatpickr-next-month:hover svg {
  fill: #f64747;
}

.flatpickr-months .flatpickr-prev-month svg, .flatpickr-months .flatpickr-next-month svg {
  width: 14px;
  height: 14px;
}

.flatpickr-months .flatpickr-prev-month svg path, .flatpickr-months .flatpickr-next-month svg path {
  -webkit-transition: fill 0.1s;
  transition: fill 0.1s;
  fill: inherit;
}

.numInputWrapper {
  position: relative;
  height: auto;
}

.numInputWrapper input, .numInputWrapper span {
  display: inline-block;
}

.numInputWrapper input {
  width: 100%;
}

.numInputWrapper input::-ms-clear {
  display: none;
}

.numInputWrapper input::-webkit-outer-spin-button, .numInputWrapper input::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none;
}

.numInputWrapper span {
  position: absolute;
  right: 0;
  width: 14px;
  padding: 0 4px 0 2px;
  height: 50%;
  line-height: 50%;
  opacity: 0;
  cursor: pointer;
  border: 1px solid rgba(57, 57, 57, 0.15);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.numInputWrapper span:hover {
  background: rgba(0, 0, 0, 0.1);
}

.numInputWrapper span:active {
  background: rgba(0, 0, 0, 0.2);
}

.numInputWrapper span:after {
  display: block;
  content: "";
  position: absolute;
}

.numInputWrapper span.arrowUp {
  top: 0;
  border-bottom: 0;
}

.numInputWrapper span.arrowUp:after {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid rgba(57, 57, 57, 0.6);
  top: 26%;
}

.numInputWrapper span.arrowDown {
  top: 50%;
}

.numInputWrapper span.arrowDown:after {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(57, 57, 57, 0.6);
  top: 40%;
}

.numInputWrapper span svg {
  width: inherit;
  height: auto;
}

.numInputWrapper span svg path {
  fill: rgba(0, 0, 0, 0.5);
}

.numInputWrapper:hover {
  background: rgba(0, 0, 0, 0.05);
}

.numInputWrapper:hover span {
  opacity: 1;
}

.flatpickr-current-month {
  font-size: 135%;
  line-height: inherit;
  font-weight: 300;
  color: inherit;
  position: absolute;
  width: 75%;
  left: 12.5%;
  padding: 7.48px 0 0 0;
  line-height: 1;
  height: 34px;
  display: inline-block;
  text-align: center;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.flatpickr-current-month span.cur-month {
  font-family: inherit;
  font-weight: 700;
  color: inherit;
  display: inline-block;
  margin-left: 0.5ch;
  padding: 0;
}

.flatpickr-current-month span.cur-month:hover {
  background: rgba(0, 0, 0, 0.05);
}

.flatpickr-current-month .numInputWrapper {
  width: 6ch;
  width: 7ch\0 ;
  display: inline-block;
}

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
  border-bottom-color: rgba(0, 0, 0, 0.9);
}

.flatpickr-current-month .numInputWrapper span.arrowDown:after {
  border-top-color: rgba(0, 0, 0, 0.9);
}

.flatpickr-current-month input.cur-year {
  background: transparent;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: inherit;
  cursor: text;
  padding: 0 0 0 0.5ch;
  margin: 0;
  display: inline-block;
  font-size: inherit;
  font-family: inherit;
  font-weight: 300;
  line-height: inherit;
  height: auto;
  border: 0;
  border-radius: 0;
  vertical-align: baseline;
  vertical-align: initial;
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

.flatpickr-current-month input.cur-year:focus {
  outline: 0;
}

.flatpickr-current-month input.cur-year[disabled], .flatpickr-current-month input.cur-year[disabled]:hover {
  font-size: 100%;
  color: rgba(0, 0, 0, 0.5);
  background: transparent;
  pointer-events: none;
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
  appearance: menulist;
  background: transparent;
  border: none;
  border-radius: 0;
  box-sizing: border-box;
  color: inherit;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  font-weight: 300;
  height: auto;
  line-height: inherit;
  margin: -1px 0 0 0;
  outline: none;
  padding: 0 0 0 0.5ch;
  position: relative;
  vertical-align: baseline;
  vertical-align: initial;
  -webkit-box-sizing: border-box;
  -webkit-appearance: menulist;
  -moz-appearance: menulist;
  width: auto;
}

.flatpickr-current-month .flatpickr-monthDropdown-months:focus, .flatpickr-current-month .flatpickr-monthDropdown-months:active {
  outline: none;
}

.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
  background: rgba(0, 0, 0, 0.05);
}

.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
  background-color: transparent;
  outline: none;
  padding: 0;
}

.flatpickr-weekdays {
  background: transparent;
  text-align: center;
  overflow: hidden;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  height: 28px;
}

.flatpickr-weekdays .flatpickr-weekdaycontainer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

span.flatpickr-weekday {
  cursor: default;
  font-size: 90%;
  background: transparent;
  color: rgba(0, 0, 0, 0.54);
  line-height: 1;
  margin: 0;
  text-align: center;
  display: block;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-weight: bolder;
}

.dayContainer, .flatpickr-weeks {
  padding: 1px 0 0 0;
}

.flatpickr-days {
  position: relative;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 307.875px;
}

.flatpickr-days:focus {
  outline: 0;
}

.dayContainer {
  padding: 0;
  outline: 0;
  text-align: left;
  width: 307.875px;
  min-width: 307.875px;
  max-width: 307.875px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: inline-block;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -ms-flex-pack: justify;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  opacity: 1;
}

.dayContainer + .dayContainer {
  -webkit-box-shadow: -1px 0 0 #e6e6e6;
  box-shadow: -1px 0 0 #e6e6e6;
}

.flatpickr-day {
  background: none;
  border: 1px solid transparent;
  border-radius: 150px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #393939;
  cursor: pointer;
  font-weight: 400;
  width: 14.2857143%;
  -webkit-flex-basis: 14.2857143%;
  -ms-flex-preferred-size: 14.2857143%;
  flex-basis: 14.2857143%;
  max-width: 39px;
  height: 39px;
  line-height: 39px;
  margin: 0;
  display: inline-block;
  position: relative;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
}

.flatpickr-day.inRange, .flatpickr-day.prevMonthDay.inRange, .flatpickr-day.nextMonthDay.inRange, .flatpickr-day.today.inRange, .flatpickr-day.prevMonthDay.today.inRange, .flatpickr-day.nextMonthDay.today.inRange, .flatpickr-day:hover, .flatpickr-day.prevMonthDay:hover, .flatpickr-day.nextMonthDay:hover, .flatpickr-day:focus, .flatpickr-day.prevMonthDay:focus, .flatpickr-day.nextMonthDay:focus {
  cursor: pointer;
  outline: 0;
  background: #e6e6e6;
  border-color: #e6e6e6;
}

.flatpickr-day.today {
  border-color: #959ea9;
}

.flatpickr-day.today:hover, .flatpickr-day.today:focus {
  border-color: #959ea9;
  background: #959ea9;
  color: #fff;
}

.flatpickr-day.selected, .flatpickr-day.startRange, .flatpickr-day.endRange, .flatpickr-day.selected.inRange, .flatpickr-day.startRange.inRange, .flatpickr-day.endRange.inRange, .flatpickr-day.selected:focus, .flatpickr-day.startRange:focus, .flatpickr-day.endRange:focus, .flatpickr-day.selected:hover, .flatpickr-day.startRange:hover, .flatpickr-day.endRange:hover, .flatpickr-day.selected.prevMonthDay, .flatpickr-day.startRange.prevMonthDay, .flatpickr-day.endRange.prevMonthDay, .flatpickr-day.selected.nextMonthDay, .flatpickr-day.startRange.nextMonthDay, .flatpickr-day.endRange.nextMonthDay {
  background: #569ff7;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #fff;
  border-color: #569ff7;
}

.flatpickr-day.selected.startRange, .flatpickr-day.startRange.startRange, .flatpickr-day.endRange.startRange {
  border-radius: 50px 0 0 50px;
}

.flatpickr-day.selected.endRange, .flatpickr-day.startRange.endRange, .flatpickr-day.endRange.endRange {
  border-radius: 0 50px 50px 0;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)), .flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)), .flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
  -webkit-box-shadow: -10px 0 0 #569ff7;
  box-shadow: -10px 0 0 #569ff7;
}

.flatpickr-day.selected.startRange.endRange, .flatpickr-day.startRange.startRange.endRange, .flatpickr-day.endRange.startRange.endRange {
  border-radius: 50px;
}

.flatpickr-day.inRange {
  border-radius: 0;
  -webkit-box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
  box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
}

.flatpickr-day.flatpickr-disabled, .flatpickr-day.flatpickr-disabled:hover, .flatpickr-day.prevMonthDay, .flatpickr-day.nextMonthDay, .flatpickr-day.notAllowed, .flatpickr-day.notAllowed.prevMonthDay, .flatpickr-day.notAllowed.nextMonthDay {
  color: rgba(57, 57, 57, 0.3);
  background: transparent;
  border-color: transparent;
  cursor: default;
}

.flatpickr-day.flatpickr-disabled, .flatpickr-day.flatpickr-disabled:hover {
  cursor: not-allowed;
  color: rgba(57, 57, 57, 0.1);
}

.flatpickr-day.week.selected {
  border-radius: 0;
  -webkit-box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7;
  box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7;
}

.flatpickr-day.hidden {
  visibility: hidden;
}

.rangeMode .flatpickr-day {
  margin-top: 1px;
}

.flatpickr-weekwrapper {
  float: left;
}

.flatpickr-weekwrapper .flatpickr-weeks {
  padding: 0 12px;
  -webkit-box-shadow: 1px 0 0 #e6e6e6;
  box-shadow: 1px 0 0 #e6e6e6;
}

.flatpickr-weekwrapper .flatpickr-weekday {
  float: none;
  width: 100%;
  line-height: 28px;
}

.flatpickr-weekwrapper span.flatpickr-day, .flatpickr-weekwrapper span.flatpickr-day:hover {
  display: block;
  width: 100%;
  max-width: none;
  color: rgba(57, 57, 57, 0.3);
  background: transparent;
  cursor: default;
  border: none;
}

.flatpickr-innerContainer {
  display: block;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
}

.flatpickr-rContainer {
  display: inline-block;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.flatpickr-time {
  text-align: center;
  outline: 0;
  display: block;
  height: 0;
  line-height: 40px;
  max-height: 40px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.flatpickr-time:after {
  content: "";
  display: table;
  clear: both;
}

.flatpickr-time .numInputWrapper {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  width: 40%;
  height: 40px;
  float: left;
}

.flatpickr-time .numInputWrapper span.arrowUp:after {
  border-bottom-color: #393939;
}

.flatpickr-time .numInputWrapper span.arrowDown:after {
  border-top-color: #393939;
}

.flatpickr-time.hasSeconds .numInputWrapper {
  width: 26%;
}

.flatpickr-time.time24hr .numInputWrapper {
  width: 49%;
}

.flatpickr-time input {
  background: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 0;
  border-radius: 0;
  text-align: center;
  margin: 0;
  padding: 0;
  height: inherit;
  line-height: inherit;
  color: #393939;
  font-size: 14px;
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

.flatpickr-time input.flatpickr-hour {
  font-weight: bold;
}

.flatpickr-time input.flatpickr-minute, .flatpickr-time input.flatpickr-second {
  font-weight: 400;
}

.flatpickr-time input:focus {
  outline: 0;
  border: 0;
}

.flatpickr-time .flatpickr-time-separator, .flatpickr-time .flatpickr-am-pm {
  height: inherit;
  float: left;
  line-height: inherit;
  color: #393939;
  font-weight: bold;
  width: 2%;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.flatpickr-time .flatpickr-am-pm {
  outline: 0;
  width: 18%;
  cursor: pointer;
  text-align: center;
  font-weight: 400;
}

.flatpickr-time input:hover, .flatpickr-time .flatpickr-am-pm:hover, .flatpickr-time input:focus, .flatpickr-time .flatpickr-am-pm:focus {
  background: #eee;
}

.flatpickr-input[readonly] {
  cursor: pointer;
}

@-webkit-keyframes fpFadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fpFadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   DateTime
   ========================================================================== */
.cf-datetime__inner {
  display: flex;
  align-items: stretch;
  flex-wrap: wrap;
  margin-top: -5px;
}
.cf-datetime__inner::before {
  display: none;
}

.cf-datetime__input {
  flex: 1;
  margin: 5px 6px 0 0;
}

.wp-core-ui .button.cf-datetime__button {
  margin-top: 5px;
}

.cf-datetime__button {
  flex: 0 0 auto;
}
.cf-field .cf-datetime__button {
  margin-bottom: 0;
  box-shadow: none;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   File
   ========================================================================== */
.cf-file__inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 130px;
  height: 130px;
  border: 1px dashed #b5bcc2;
  box-sizing: border-box;
}

.cf-file__content {
  position: absolute;
  top: -1px;
  right: -1px;
  bottom: -1px;
  left: -1px;
  border: 1px solid #b5bcc2;
}

.cf-file__preview {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 28px;
  width: 100%;
  overflow: hidden;
  background-color: #e2e4e7;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1) inset;
}

.cf-file__image {
  position: absolute;
  top: 50%;
  left: 50%;
  height: auto;
  max-width: 100%;
  transform: translate(-50%, -50%);
}

.cf-file__name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 5px;
  border-top: 1px solid #b5bcc2;
  overflow: hidden;
  background-color: #f3f4f5;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.cf-file__browse {
  position: relative;
}
.cf-file__content ~ .cf-file__browse {
  margin-bottom: 29px;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0.1s linear, opacity 0.1s linear;
}
.cf-file__inner:hover .cf-file__content ~ .cf-file__browse {
  visibility: visible;
  opacity: 1;
}

.cf-file__remove {
  position: absolute;
  top: 5px;
  right: 5px;
  padding: 0;
  border: 0;
  outline: none;
  background-color: transparent;
  cursor: pointer;
  transition: opacity 0.1s linear;
}
.cf-file__remove:hover {
  opacity: 0.8;
}
.cf-file__remove::before {
  border-radius: 50%;
  background-color: #191e23;
  color: #fff;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Map
   ========================================================================== */
.cf-map__search {
  position: relative;
  z-index: 1;
}

.cf-map__canvas {
  position: relative;
  z-index: 0;
  height: 300px;
  border-width: 0 1px 1px;
  border-style: solid;
  border-color: #e2e4e7;
  background-color: #f3f4f5;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Media Gallery
   ========================================================================== */
.cf-media-gallery__list {
  display: flex;
  flex-wrap: wrap;
  max-height: 400px;
  padding: 4px;
  margin: 0;
  overflow-y: auto;
  list-style: none outside none;
}
.cf-media-gallery__list:empty {
  display: none;
}

.cf-media-gallery__actions {
  padding: 8px;
}
.cf-media-gallery__list:empty ~ .cf-media-gallery__actions {
  border-top-width: 0;
}

.cf-media-gallery__item {
  flex: 0 0 100%;
  min-width: 0;
  padding: 4px;
  margin: 0;
  box-sizing: border-box;
}
@media (min-width: 320px) {
  .cf-media-gallery__item {
    flex-basis: 50%;
  }
}
@media (min-width: 480px) {
  .cf-media-gallery__item {
    flex-basis: 33.3333%;
  }
}
@media (min-width: 640px) {
  .cf-media-gallery__item {
    flex-basis: 25%;
  }
}
@media (min-width: 768px) {
  .cf-media-gallery__item {
    flex-basis: 20%;
  }
}
@media (min-width: 1280px) {
  .cf-media-gallery__item {
    flex-basis: 16.66667%;
  }
}
@media (min-width: 1440px) {
  .cf-media-gallery__item {
    flex-basis: 12.5%;
  }
}
@media (min-width: 1680px) {
  .cf-media-gallery__item {
    flex-basis: 10%;
  }
}

.cf-media-gallery__item-inner {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.cf-media-gallery__item-preview {
  position: relative;
  overflow: hidden;
  padding-top: 100%;
  flex: 1;
}

.cf-media-gallery__item-thumb {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  max-width: 150%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.cf-media-gallery__item-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  max-width: 100%;
  max-height: 50%;
  transform: translate(-50%, -50%);
}

.cf-media-gallery__item-name {
  display: block;
  padding: 4px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: move;
}

.cf-media-gallery__item-remove {
  position: absolute;
  top: 4px;
  right: 4px;
  padding: 0;
  border: 0;
  outline: none;
  background-color: transparent;
  cursor: pointer;
}
.cf-media-gallery__item-remove::before {
  border-radius: 50%;
  background-color: #191e23;
  color: #fff;
  transition: color 0.1s linear;
}
.cf-media-gallery__item-remove:hover::before {
  color: #b5bcc2;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Multiselect
   ========================================================================== */
.cf-multiselect__control {
  min-height: 0;
  border-color: #e2e4e7;
}
.cf-multiselect__control:hover {
  border-color: #e2e4e7;
}
.cf-multiselect__control--is-focused, .cf-multiselect__control--is-focused:hover {
  border-color: #00a0d2 !important;
  box-shadow: none;
}

.cf-multiselect__placeholder {
  color: #b5bcc2;
}

.cf-multiselect__value-container {
  padding-left: 4px;
  padding-right: 4px;
}

.cf-multiselect__multi-value {
  align-items: center;
  padding: 5px 3px;
  margin: 0;
  background-color: #e2e4e7;
}
.cf-multiselect__multi-value + .cf-multiselect__multi-value {
  margin-left: 5px;
}

.cf-multiselect__multi-value__label {
  padding-left: 3px;
  font-size: 13px;
  line-height: 1;
}

.cf-multiselect__multi-value__remove {
  padding: 0;
  margin-top: 1px;
  cursor: pointer;
}
.cf-multiselect__multi-value__remove:hover {
  background-color: transparent;
}

.cf-multiselect__input input[id],
.cf-multiselect__input input[id]:focus {
  box-shadow: none;
}

.cf-multiselect__menu {
  z-index: 9999;
}

.cf-multiselect__option {
  padding: 4px;
}
.cf-multiselect__option--is-focused {
  background-color: #00a0d2;
  color: #fff;
}

.cf-multiselect__indicator {
  padding: 5px;
  cursor: pointer;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   oEmbed
   ========================================================================== */
.cf-oembed__preview {
  padding: 12px;
  border-width: 0 1px 1px;
  border-style: solid;
  border-color: #e2e4e7;
}

.cf-oembed__frame {
  display: block;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Radio
   ========================================================================== */
.cf-radio__list {
  margin: 0;
}
.cf-radio-image .cf-radio__list {
  display: flex;
  flex-wrap: wrap;
}

.cf-radio__list-item:last-child {
  margin-bottom: 0;
}
.cf-radio-image .cf-radio__list-item {
  flex: 0 0 20%;
  position: relative;
  padding: 4px;
}

.cf-container-term-meta .cf-radio__label {
  display: inline;
}
.cf-radio-image .cf-radio__label {
  display: inline-block;
}

.cf-field .cf-radio__input {
  margin-top: 0;
}
.cf-radio-image .cf-radio__input {
  position: absolute;
  z-index: -1;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  margin: 0;
  outline: 0;
  opacity: 0;
}
.cf-radio__input[type=checkbox] {
  border-radius: 50%;
}
.cf-radio__input[type=checkbox]:checked:before {
  content: "";
  background-color: #3582c4;
  background-color: var(--wp-admin-theme-color, #3582c4);
  border-radius: 50%;
  width: 0.5rem;
  height: 0.5rem;
  margin: 0.1875rem;
  line-height: 1.14285714;
}

.cf-radio-image__image {
  display: block;
  max-width: 100%;
  padding: 5px;
  box-sizing: border-box;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Radio
   ========================================================================== */
.wp-block .cf-radio__list {
  list-style: none outside none;
}

.cf-radio__list-item {
  box-sizing: border-box;
}
.cf-container-term-meta .cf-radio__list-item {
  flex: 0 0 20%;
}
.cf-container-theme-options .cf-radio__list-item {
  flex: 0 0 10%;
}

.cf-radio-image__image {
  border: 1px solid #e2e4e7;
}
.cf-radio__input:focus ~ .cf-radio__label .cf-radio-image__image, .cf-radio__input:checked ~ .cf-radio__label .cf-radio-image__image {
  outline: 4px solid #00a0d2;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Select
   ========================================================================== */
.cf-select__input {
  display: block;
  width: 100%;
  margin: 0;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Separator
   ========================================================================== */
.cf-container-term-meta .cf-separator .cf-field__head {
  display: none;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Set
   ========================================================================== */
.cf-set__list {
  margin: 0;
}

.cf-set__list-item:last-child {
  margin-bottom: 0;
}

.cf-field .cf-set__input {
  margin-top: 0;
}

.cf-container-term-meta .cf-set__label {
  display: inline;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Text
   ========================================================================== */
.cf-text__input {
  display: block;
  width: 100% !important;
  margin: 0;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Textarea
   ========================================================================== */
.cf-textarea__input {
  display: block;
  width: 100% !important;
  resize: vertical;
}
/* ==========================================================================
   Colors
   ========================================================================== */
/* ==========================================================================
   WordPress Colors - https://make.wordpress.org/design/handbook/design-guide/foundations/colors/
   ========================================================================== */
/* ==========================================================================
   Gutenberg Colors - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_colors.scss
   ========================================================================== */
/* ==========================================================================
   Fonts
   ========================================================================== */
/* ==========================================================================
   Variables
   ========================================================================== */
/* ==========================================================================
   WordPress Variables
   ========================================================================== */
/* ==========================================================================
   Gutenberg Variables - https://github.com/WordPress/gutenberg/blob/master/assets/stylesheets/_variables.scss
   ========================================================================== */
/* ==========================================================================
   Field
   ========================================================================== */
.cf-field,
.cf-field__head,
.cf-field__body {
  box-sizing: border-box;
  flex: 1 1 100%;
}

@media (max-width: 1024px) {
  .cf-field {
    flex-basis: 100% !important;
  }
}
.cf-field.cf-block-preview {
  display: none;
}

.cf-rich-text .cf-field__body {
  box-sizing: content-box;
}
.cf-complex--tabbed-vertical > .cf-field__body {
  display: flex;
  align-items: flex-start;
}

.cf-field__label {
  display: block;
}
.cf-html .cf-field__label, .cf-separator .cf-field__label, .cf-block-preview .cf-field__label {
  display: none;
}

.cf-field__asterisk {
  color: #dc3232;
}

.cf-field__error {
  display: block;
  margin-top: 4px;
  color: #dc3232;
}
