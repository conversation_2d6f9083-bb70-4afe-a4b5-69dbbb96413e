# Carbon Fields [![Build Status](https://travis-ci.org/htmlburger/carbon-fields.svg?branch=master)](https://travis-ci.org/htmlburger/carbon-fields) [![Scrutinizer Code Quality](https://scrutinizer-ci.com/g/htmlburger/carbon-fields/badges/quality-score.png?b=master)](https://scrutinizer-ci.com/g/htmlburger/carbon-fields/?branch=master) [![Code Coverage](https://scrutinizer-ci.com/g/htmlburger/carbon-fields/badges/coverage.png?b=master)](https://scrutinizer-ci.com/g/htmlburger/carbon-fields/?branch=master) [![Gitter chat](https://badges.gitter.im/carbon-fields/Lobby.png)](https://gitter.im/carbon-fields/Lobby)

### About

[Carbon Fields](http://carbonfields.net/) - developer-oriented library for WordPress custom fields for all types of WordPress content. 

Carbon fields can be used as a composer package for easy creation of custom fields in the WordPress administration panel. 

Custom fields can be created for post types, taxonomy terms, users, comments, options, navigation menus and even widgets.

Supports PHP 5.6.20 or higher.

### Quickstart

See [Quickstart](https://github.com/htmlburger/carbon-fields-docs/tree/master/documentation/10-quickstart.md)

### Documentation & Other Resources

* [Website](http://carbonfields.net/)
* [Documentation (website)](https://docs.carbonfields.net/)
* [Documentation (GitHub)](https://github.com/htmlburger/carbon-fields-docs)
* [FAQ](http://carbonfields.net/faq/)
* [Support](http://carbonfields.net/support/)
* [GitHub Repository](https://github.com/htmlburger/carbon-fields)
