[{"name": "500px", "id": "500px", "unicode": "f26e", "styles": ["brands"], "search_terms": []}, {"name": "Accessible Icon", "id": "accessible-icon", "unicode": "f368", "styles": ["brands"], "search_terms": ["accessibility", "handicap", "person", "wheelchair", "wheelchair-alt"]}, {"name": "Accusoft", "id": "accusoft", "unicode": "f369", "styles": ["brands"], "search_terms": []}, {"name": "Acquisitions Incorporated", "id": "acquisitions-incorporated", "unicode": "f6af", "styles": ["brands"], "search_terms": ["Dungeons & Dragons", "d&d", "dnd", "fantasy", "game", "gaming", "tabletop"]}, {"name": "Ad", "id": "ad", "unicode": "f641", "styles": ["solid"], "search_terms": ["advertisement", "media", "newspaper", "promotion", "publicity"]}, {"name": "Address Book", "id": "address-book", "unicode": "f2b9", "styles": ["solid", "regular"], "search_terms": ["contact", "directory", "index", "little black book", "rolodex"]}, {"name": "Address Card", "id": "address-card", "unicode": "f2bb", "styles": ["solid", "regular"], "search_terms": ["about", "contact", "id", "identification", "postcard", "profile"]}, {"name": "adjust", "id": "adjust", "unicode": "f042", "styles": ["solid"], "search_terms": ["contrast", "dark", "light", "saturation"]}, {"name": "App.net", "id": "adn", "unicode": "f170", "styles": ["brands"], "search_terms": []}, {"name": "Adobe", "id": "adobe", "unicode": "f778", "styles": ["brands"], "search_terms": ["acrobat", "app", "design", "illustrator", "indesign", "photoshop"]}, {"name": "Adversal", "id": "adversal", "unicode": "f36a", "styles": ["brands"], "search_terms": []}, {"name": "affiliatetheme", "id": "affiliatetheme", "unicode": "f36b", "styles": ["brands"], "search_terms": []}, {"name": "Air Freshener", "id": "air-freshener", "unicode": "f5d0", "styles": ["solid"], "search_terms": ["car", "deodorize", "fresh", "pine", "scent"]}, {"name": "Airbnb", "id": "airbnb", "unicode": "f834", "styles": ["brands"], "search_terms": []}, {"name": "Algolia", "id": "algolia", "unicode": "f36c", "styles": ["brands"], "search_terms": []}, {"name": "align-center", "id": "align-center", "unicode": "f037", "styles": ["solid"], "search_terms": ["format", "middle", "paragraph", "text"]}, {"name": "align-justify", "id": "align-justify", "unicode": "f039", "styles": ["solid"], "search_terms": ["format", "paragraph", "text"]}, {"name": "align-left", "id": "align-left", "unicode": "f036", "styles": ["solid"], "search_terms": ["format", "paragraph", "text"]}, {"name": "align-right", "id": "align-right", "unicode": "f038", "styles": ["solid"], "search_terms": ["format", "paragraph", "text"]}, {"name": "Alipay", "id": "alipay", "unicode": "f642", "styles": ["brands"], "search_terms": []}, {"name": "Allergies", "id": "allergies", "unicode": "f461", "styles": ["solid"], "search_terms": ["allergy", "freckles", "hand", "hives", "pox", "skin", "spots"]}, {"name": "Amazon", "id": "amazon", "unicode": "f270", "styles": ["brands"], "search_terms": []}, {"name": "Amazon Pay", "id": "amazon-pay", "unicode": "f42c", "styles": ["brands"], "search_terms": []}, {"name": "ambulance", "id": "ambulance", "unicode": "f0f9", "styles": ["solid"], "search_terms": ["emergency", "emt", "er", "help", "hospital", "support", "vehicle"]}, {"name": "American Sign Language Interpreting", "id": "american-sign-language-interpreting", "unicode": "f2a3", "styles": ["solid"], "search_terms": ["asl", "deaf", "finger", "hand", "interpret", "speak"]}, {"name": "Amilia", "id": "amilia", "unicode": "f36d", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "anchor", "unicode": "f13d", "styles": ["solid"], "search_terms": ["berth", "boat", "dock", "embed", "link", "maritime", "moor", "secure"]}, {"name": "Android", "id": "android", "unicode": "f17b", "styles": ["brands"], "search_terms": ["robot"]}, {"name": "AngelList", "id": "angellist", "unicode": "f209", "styles": ["brands"], "search_terms": []}, {"name": "Angle Double Down", "id": "angle-double-down", "unicode": "f103", "styles": ["solid"], "search_terms": ["arrows", "caret", "download", "expand"]}, {"name": "<PERSON>le Double Left", "id": "angle-double-left", "unicode": "f100", "styles": ["solid"], "search_terms": ["arrows", "back", "caret", "laquo", "previous", "quote"]}, {"name": "Angle Double Right", "id": "angle-double-right", "unicode": "f101", "styles": ["solid"], "search_terms": ["arrows", "caret", "forward", "more", "next", "quote", "raquo"]}, {"name": "Angle Double Up", "id": "angle-double-up", "unicode": "f102", "styles": ["solid"], "search_terms": ["arrows", "caret", "collapse", "upload"]}, {"name": "angle-down", "id": "angle-down", "unicode": "f107", "styles": ["solid"], "search_terms": ["arrow", "caret", "download", "expand"]}, {"name": "angle-left", "id": "angle-left", "unicode": "f104", "styles": ["solid"], "search_terms": ["arrow", "back", "caret", "less", "previous"]}, {"name": "angle-right", "id": "angle-right", "unicode": "f105", "styles": ["solid"], "search_terms": ["arrow", "care", "forward", "more", "next"]}, {"name": "angle-up", "id": "angle-up", "unicode": "f106", "styles": ["solid"], "search_terms": ["arrow", "caret", "collapse", "upload"]}, {"name": "Angry Face", "id": "angry", "unicode": "f556", "styles": ["solid", "regular"], "search_terms": ["disapprove", "emoticon", "face", "mad", "upset"]}, {"name": "Angry Creative", "id": "angrycreative", "unicode": "f36e", "styles": ["brands"], "search_terms": []}, {"name": "Angular", "id": "angular", "unicode": "f420", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "ankh", "unicode": "f644", "styles": ["solid"], "search_terms": ["amulet", "copper", "coptic christianity", "copts", "crux ansata", "egypt", "venus"]}, {"name": "App Store", "id": "app-store", "unicode": "f36f", "styles": ["brands"], "search_terms": []}, {"name": "iOS App Store", "id": "app-store-ios", "unicode": "f370", "styles": ["brands"], "search_terms": []}, {"name": "Apper Systems AB", "id": "apper", "unicode": "f371", "styles": ["brands"], "search_terms": []}, {"name": "Apple", "id": "apple", "unicode": "f179", "styles": ["brands"], "search_terms": ["fruit", "ios", "mac", "operating system", "os", "osx"]}, {"name": "Fruit Apple", "id": "apple-alt", "unicode": "f5d1", "styles": ["solid"], "search_terms": ["fall", "fruit", "fuji", "macintosh", "orchard", "seasonal", "vegan"]}, {"name": "Apple Pay", "id": "apple-pay", "unicode": "f415", "styles": ["brands"], "search_terms": []}, {"name": "Archive", "id": "archive", "unicode": "f187", "styles": ["solid"], "search_terms": ["box", "package", "save", "storage"]}, {"name": "Archway", "id": "archway", "unicode": "f557", "styles": ["solid"], "search_terms": ["arc", "monument", "road", "street", "tunnel"]}, {"name": "Alternate Arrow Circle Down", "id": "arrow-alt-circle-down", "unicode": "f358", "styles": ["solid", "regular"], "search_terms": ["arrow-circle-o-down", "download"]}, {"name": "Alternate Arrow Circle Left", "id": "arrow-alt-circle-left", "unicode": "f359", "styles": ["solid", "regular"], "search_terms": ["arrow-circle-o-left", "back", "previous"]}, {"name": "Alternate Arrow Circle Right", "id": "arrow-alt-circle-right", "unicode": "f35a", "styles": ["solid", "regular"], "search_terms": ["arrow-circle-o-right", "forward", "next"]}, {"name": "Alternate Arrow Circle Up", "id": "arrow-alt-circle-up", "unicode": "f35b", "styles": ["solid", "regular"], "search_terms": ["arrow-circle-o-up"]}, {"name": "Arrow Circle Down", "id": "arrow-circle-down", "unicode": "f0ab", "styles": ["solid"], "search_terms": ["download"]}, {"name": "Arrow Circle Left", "id": "arrow-circle-left", "unicode": "f0a8", "styles": ["solid"], "search_terms": ["back", "previous"]}, {"name": "Arrow Circle Right", "id": "arrow-circle-right", "unicode": "f0a9", "styles": ["solid"], "search_terms": ["forward", "next"]}, {"name": "Arrow Circle Up", "id": "arrow-circle-up", "unicode": "f0aa", "styles": ["solid"], "search_terms": ["upload"]}, {"name": "arrow-down", "id": "arrow-down", "unicode": "f063", "styles": ["solid"], "search_terms": ["download"]}, {"name": "arrow-left", "id": "arrow-left", "unicode": "f060", "styles": ["solid"], "search_terms": ["back", "previous"]}, {"name": "arrow-right", "id": "arrow-right", "unicode": "f061", "styles": ["solid"], "search_terms": ["forward", "next"]}, {"name": "arrow-up", "id": "arrow-up", "unicode": "f062", "styles": ["solid"], "search_terms": ["forward", "upload"]}, {"name": "Alternate Arrows", "id": "arrows-alt", "unicode": "f0b2", "styles": ["solid"], "search_terms": ["arrow", "arrows", "bigger", "enlarge", "expand", "fullscreen", "move", "position", "reorder", "resize"]}, {"name": "Alternate Arrows Horizontal", "id": "arrows-alt-h", "unicode": "f337", "styles": ["solid"], "search_terms": ["arrows-h", "expand", "horizontal", "landscape", "resize", "wide"]}, {"name": "Alternate Arrows Vertical", "id": "arrows-alt-v", "unicode": "f338", "styles": ["solid"], "search_terms": ["arrows-v", "expand", "portrait", "resize", "tall", "vertical"]}, {"name": "Artstation", "id": "artstation", "unicode": "f77a", "styles": ["brands"], "search_terms": []}, {"name": "Assistive Listening Systems", "id": "assistive-listening-systems", "unicode": "f2a2", "styles": ["solid"], "search_terms": ["amplify", "audio", "deaf", "ear", "headset", "hearing", "sound"]}, {"name": "asterisk", "id": "asterisk", "unicode": "f069", "styles": ["solid"], "search_terms": ["annotation", "details", "reference", "star"]}, {"name": "Asymmetrik, Ltd.", "id": "asymmetrik", "unicode": "f372", "styles": ["brands"], "search_terms": []}, {"name": "At", "id": "at", "unicode": "f1fa", "styles": ["solid"], "search_terms": ["address", "author", "e-mail", "email", "handle"]}, {"name": "Atlas", "id": "atlas", "unicode": "f558", "styles": ["solid"], "search_terms": ["book", "directions", "geography", "globe", "map", "travel", "wayfinding"]}, {"name": "Atlassian", "id": "atlassian", "unicode": "f77b", "styles": ["brands"], "search_terms": []}, {"name": "Atom", "id": "atom", "unicode": "f5d2", "styles": ["solid"], "search_terms": ["atheism", "chemistry", "ion", "nuclear", "science"]}, {"name": "Audible", "id": "audible", "unicode": "f373", "styles": ["brands"], "search_terms": []}, {"name": "Audio Description", "id": "audio-description", "unicode": "f29e", "styles": ["solid"], "search_terms": ["blind", "narration", "video", "visual"]}, {"name": "Autoprefixer", "id": "autoprefixer", "unicode": "f41c", "styles": ["brands"], "search_terms": []}, {"name": "avianex", "id": "avianex", "unicode": "f374", "styles": ["brands"], "search_terms": []}, {"name": "Aviato", "id": "aviato", "unicode": "f421", "styles": ["brands"], "search_terms": []}, {"name": "Award", "id": "award", "unicode": "f559", "styles": ["solid"], "search_terms": ["honor", "praise", "prize", "recognition", "ribbon", "trophy"]}, {"name": "Amazon Web Services (AWS)", "id": "aws", "unicode": "f375", "styles": ["brands"], "search_terms": []}, {"name": "Baby", "id": "baby", "unicode": "f77c", "styles": ["solid"], "search_terms": ["child", "diaper", "doll", "human", "infant", "kid", "offspring", "person", "sprout"]}, {"name": "Baby <PERSON>ge", "id": "baby-carriage", "unicode": "f77d", "styles": ["solid"], "search_terms": ["buggy", "carrier", "infant", "push", "stroller", "transportation", "walk", "wheels"]}, {"name": "Backspace", "id": "backspace", "unicode": "f55a", "styles": ["solid"], "search_terms": ["command", "delete", "erase", "keyboard", "undo"]}, {"name": "backward", "id": "backward", "unicode": "f04a", "styles": ["solid"], "search_terms": ["previous", "rewind"]}, {"name": "<PERSON>", "id": "bacon", "unicode": "f7e5", "styles": ["solid"], "search_terms": ["blt", "breakfast", "ham", "lard", "meat", "pancetta", "pork", "rasher"]}, {"name": "Balance Scale", "id": "balance-scale", "unicode": "f24e", "styles": ["solid"], "search_terms": ["balanced", "justice", "legal", "measure", "weight"]}, {"name": "ban", "id": "ban", "unicode": "f05e", "styles": ["solid"], "search_terms": ["abort", "ban", "block", "cancel", "delete", "hide", "prohibit", "remove", "stop", "trash"]}, {"name": "Band-Aid", "id": "band-aid", "unicode": "f462", "styles": ["solid"], "search_terms": ["bandage", "boo boo", "first aid", "ouch"]}, {"name": "Bandcamp", "id": "bandcamp", "unicode": "f2d5", "styles": ["brands"], "search_terms": []}, {"name": "barcode", "id": "barcode", "unicode": "f02a", "styles": ["solid"], "search_terms": ["info", "laser", "price", "scan", "upc"]}, {"name": "Bars", "id": "bars", "unicode": "f0c9", "styles": ["solid"], "search_terms": ["checklist", "drag", "hamburger", "list", "menu", "nav", "navigation", "ol", "reorder", "settings", "todo", "ul"]}, {"name": "Baseball Ball", "id": "baseball-ball", "unicode": "f433", "styles": ["solid"], "search_terms": ["foul", "hardball", "league", "leather", "mlb", "softball", "sport"]}, {"name": "Basketball Ball", "id": "basketball-ball", "unicode": "f434", "styles": ["solid"], "search_terms": ["dribble", "dunk", "hoop", "nba"]}, {"name": "Bath", "id": "bath", "unicode": "f2cd", "styles": ["solid"], "search_terms": ["clean", "shower", "tub", "wash"]}, {"name": "Battery Empty", "id": "battery-empty", "unicode": "f244", "styles": ["solid"], "search_terms": ["charge", "dead", "power", "status"]}, {"name": "Battery Full", "id": "battery-full", "unicode": "f240", "styles": ["solid"], "search_terms": ["charge", "power", "status"]}, {"name": "Battery 1/2 Full", "id": "battery-half", "unicode": "f242", "styles": ["solid"], "search_terms": ["charge", "power", "status"]}, {"name": "Battery 1/4 Full", "id": "battery-quarter", "unicode": "f243", "styles": ["solid"], "search_terms": ["charge", "low", "power", "status"]}, {"name": "Battery 3/4 Full", "id": "battery-three-quarters", "unicode": "f241", "styles": ["solid"], "search_terms": ["charge", "power", "status"]}, {"name": "Battle.net", "id": "battle-net", "unicode": "f835", "styles": ["brands"], "search_terms": []}, {"name": "Bed", "id": "bed", "unicode": "f236", "styles": ["solid"], "search_terms": ["lodging", "rest", "sleep", "travel"]}, {"name": "beer", "id": "beer", "unicode": "f0fc", "styles": ["solid"], "search_terms": ["alcohol", "ale", "bar", "beverage", "brewery", "drink", "lager", "liquor", "mug", "stein"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "behance", "unicode": "f1b4", "styles": ["brands"], "search_terms": []}, {"name": "Behance Square", "id": "behance-square", "unicode": "f1b5", "styles": ["brands"], "search_terms": []}, {"name": "bell", "id": "bell", "unicode": "f0f3", "styles": ["solid", "regular"], "search_terms": ["alarm", "alert", "chime", "notification", "reminder"]}, {"name": "Bell Slash", "id": "bell-slash", "unicode": "f1f6", "styles": ["solid", "regular"], "search_terms": ["alert", "cancel", "disabled", "notification", "off", "reminder"]}, {"name": "Bezier Curve", "id": "bezier-curve", "unicode": "f55b", "styles": ["solid"], "search_terms": ["curves", "illustrator", "lines", "path", "vector"]}, {"name": "Bible", "id": "bible", "unicode": "f647", "styles": ["solid"], "search_terms": ["book", "catholicism", "christianity", "god", "holy"]}, {"name": "Bicycle", "id": "bicycle", "unicode": "f206", "styles": ["solid"], "search_terms": ["bike", "gears", "pedal", "transportation", "vehicle"]}, {"name": "BIMobject", "id": "bimobject", "unicode": "f378", "styles": ["brands"], "search_terms": []}, {"name": "Binoculars", "id": "binoculars", "unicode": "f1e5", "styles": ["solid"], "search_terms": ["glasses", "magnify", "scenic", "spyglass", "view"]}, {"name": "Biohazard", "id": "biohazard", "unicode": "f780", "styles": ["solid"], "search_terms": ["danger", "dangerous", "hazmat", "medical", "radioactive", "toxic", "waste", "zombie"]}, {"name": "Birthday Cake", "id": "birthday-cake", "unicode": "f1fd", "styles": ["solid"], "search_terms": ["anniversary", "bakery", "candles", "celebration", "dessert", "frosting", "holiday", "party", "pastry"]}, {"name": "Bitbucket", "id": "bitbucket", "unicode": "f171", "styles": ["brands"], "search_terms": ["atlassian", "bitbucket-square", "git"]}, {"name": "Bitcoin", "id": "bitcoin", "unicode": "f379", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON>y", "id": "bity", "unicode": "f37a", "styles": ["brands"], "search_terms": []}, {"name": "Font Awesome Black Tie", "id": "black-tie", "unicode": "f27e", "styles": ["brands"], "search_terms": []}, {"name": "BlackBerry", "id": "blackberry", "unicode": "f37b", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>der", "id": "blender", "unicode": "f517", "styles": ["solid"], "search_terms": ["cocktail", "milkshake", "mixer", "puree", "smoothie"]}, {"name": "Blender Phone", "id": "blender-phone", "unicode": "f6b6", "styles": ["solid"], "search_terms": ["appliance", "cocktail", "communication", "fantasy", "milkshake", "mixer", "puree", "silly", "smoothie"]}, {"name": "Blind", "id": "blind", "unicode": "f29d", "styles": ["solid"], "search_terms": ["cane", "disability", "person", "sight"]}, {"name": "Blog", "id": "blog", "unicode": "f781", "styles": ["solid"], "search_terms": ["journal", "log", "online", "personal", "post", "web 2.0", "wordpress", "writing"]}, {"name": "Blogger", "id": "blogger", "unicode": "f37c", "styles": ["brands"], "search_terms": []}, {"name": "Blogger B", "id": "blogger-b", "unicode": "f37d", "styles": ["brands"], "search_terms": []}, {"name": "Bluetooth", "id": "bluetooth", "unicode": "f293", "styles": ["brands"], "search_terms": []}, {"name": "Bluetooth", "id": "bluetooth-b", "unicode": "f294", "styles": ["brands"], "search_terms": []}, {"name": "bold", "id": "bold", "unicode": "f032", "styles": ["solid"], "search_terms": ["emphasis", "format", "text"]}, {"name": "Lightning Bolt", "id": "bolt", "unicode": "f0e7", "styles": ["solid"], "search_terms": ["electricity", "lightning", "weather", "zap"]}, {"name": "Bomb", "id": "bomb", "unicode": "f1e2", "styles": ["solid"], "search_terms": ["error", "explode", "fuse", "grenade", "warning"]}, {"name": "Bone", "id": "bone", "unicode": "f5d7", "styles": ["solid"], "search_terms": ["calcium", "dog", "skeletal", "skeleton", "tibia"]}, {"name": "<PERSON><PERSON>", "id": "bong", "unicode": "f55c", "styles": ["solid"], "search_terms": ["aparatus", "cannabis", "marijuana", "pipe", "smoke", "smoking"]}, {"name": "book", "id": "book", "unicode": "f02d", "styles": ["solid"], "search_terms": ["diary", "documentation", "journal", "library", "read"]}, {"name": "Book of the Dead", "id": "book-dead", "unicode": "f6b7", "styles": ["solid"], "search_terms": ["Dungeons & Dragons", "crossbones", "d&d", "dark arts", "death", "dnd", "documentation", "evil", "fantasy", "halloween", "holiday", "necronomicon", "read", "skull", "spell"]}, {"name": "Medical Book", "id": "book-medical", "unicode": "f7e6", "styles": ["solid"], "search_terms": ["diary", "documentation", "health", "history", "journal", "library", "read", "record"]}, {"name": "Book Open", "id": "book-open", "unicode": "f518", "styles": ["solid"], "search_terms": ["flyer", "library", "notebook", "open book", "pamphlet", "reading"]}, {"name": "Book Reader", "id": "book-reader", "unicode": "f5da", "styles": ["solid"], "search_terms": ["flyer", "library", "notebook", "open book", "pamphlet", "reading"]}, {"name": "bookmark", "id": "bookmark", "unicode": "f02e", "styles": ["solid", "regular"], "search_terms": ["favorite", "marker", "read", "remember", "save"]}, {"name": "Bootstrap", "id": "bootstrap", "unicode": "f836", "styles": ["brands"], "search_terms": []}, {"name": "Bowling Ball", "id": "bowling-ball", "unicode": "f436", "styles": ["solid"], "search_terms": ["alley", "candlepin", "gutter", "lane", "strike", "tenpin"]}, {"name": "Box", "id": "box", "unicode": "f466", "styles": ["solid"], "search_terms": ["archive", "container", "package", "storage"]}, {"name": "Box Open", "id": "box-open", "unicode": "f49e", "styles": ["solid"], "search_terms": ["archive", "container", "package", "storage", "unpack"]}, {"name": "Boxes", "id": "boxes", "unicode": "f468", "styles": ["solid"], "search_terms": ["archives", "inventory", "storage", "warehouse"]}, {"name": "Braille", "id": "braille", "unicode": "f2a1", "styles": ["solid"], "search_terms": ["alphabet", "blind", "dots", "raised", "vision"]}, {"name": "Brain", "id": "brain", "unicode": "f5dc", "styles": ["solid"], "search_terms": ["cerebellum", "gray matter", "intellect", "medulla oblongata", "mind", "noodle", "wit"]}, {"name": "Bread Slice", "id": "bread-slice", "unicode": "f7ec", "styles": ["solid"], "search_terms": ["bake", "bakery", "baking", "dough", "flour", "gluten", "grain", "sandwich", "sourdough", "toast", "wheat", "yeast"]}, {"name": "Briefcase", "id": "briefcase", "unicode": "f0b1", "styles": ["solid"], "search_terms": ["bag", "business", "luggage", "office", "work"]}, {"name": "Medical Briefcase", "id": "briefcase-medical", "unicode": "f469", "styles": ["solid"], "search_terms": ["doctor", "emt", "first aid", "health"]}, {"name": "Broadcast Tower", "id": "broadcast-tower", "unicode": "f519", "styles": ["solid"], "search_terms": ["airwaves", "antenna", "radio", "reception", "waves"]}, {"name": "Broom", "id": "broom", "unicode": "f51a", "styles": ["solid"], "search_terms": ["clean", "firebolt", "fly", "halloween", "nimbus 2000", "quidditch", "sweep", "witch"]}, {"name": "Brush", "id": "brush", "unicode": "f55d", "styles": ["solid"], "search_terms": ["art", "bristles", "color", "handle", "paint"]}, {"name": "BTC", "id": "btc", "unicode": "f15a", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "buffer", "unicode": "f837", "styles": ["brands"], "search_terms": []}, {"name": "Bug", "id": "bug", "unicode": "f188", "styles": ["solid"], "search_terms": ["beetle", "error", "insect", "report"]}, {"name": "Building", "id": "building", "unicode": "f1ad", "styles": ["solid", "regular"], "search_terms": ["apartment", "business", "city", "company", "office", "work"]}, {"name": "bullhorn", "id": "bullhorn", "unicode": "f0a1", "styles": ["solid"], "search_terms": ["announcement", "broadcast", "louder", "megaphone", "share"]}, {"name": "Bullseye", "id": "bullseye", "unicode": "f140", "styles": ["solid"], "search_terms": ["archery", "goal", "objective", "target"]}, {"name": "Burn", "id": "burn", "unicode": "f46a", "styles": ["solid"], "search_terms": ["caliente", "energy", "fire", "flame", "gas", "heat", "hot"]}, {"name": "Büromöbel-Experte GmbH & Co. KG.", "id": "buromobelexperte", "unicode": "f37f", "styles": ["brands"], "search_terms": []}, {"name": "Bus", "id": "bus", "unicode": "f207", "styles": ["solid"], "search_terms": ["public transportation", "transportation", "travel", "vehicle"]}, {"name": "Bus Alt", "id": "bus-alt", "unicode": "f55e", "styles": ["solid"], "search_terms": ["mta", "public transportation", "transportation", "travel", "vehicle"]}, {"name": "Business Time", "id": "business-time", "unicode": "f64a", "styles": ["solid"], "search_terms": ["alarm", "briefcase", "business socks", "clock", "flight of the conchords", "reminder", "wednesday"]}, {"name": "BuySellAds", "id": "buysellads", "unicode": "f20d", "styles": ["brands"], "search_terms": []}, {"name": "Calculator", "id": "calculator", "unicode": "f1ec", "styles": ["solid"], "search_terms": ["abacus", "addition", "arithmetic", "counting", "math", "multiplication", "subtraction"]}, {"name": "Calendar", "id": "calendar", "unicode": "f133", "styles": ["solid", "regular"], "search_terms": ["calendar-o", "date", "event", "schedule", "time", "when"]}, {"name": "Alternate Calendar", "id": "calendar-alt", "unicode": "f073", "styles": ["solid", "regular"], "search_terms": ["calendar", "date", "event", "schedule", "time", "when"]}, {"name": "Calendar Check", "id": "calendar-check", "unicode": "f274", "styles": ["solid", "regular"], "search_terms": ["accept", "agree", "appointment", "confirm", "correct", "date", "done", "event", "ok", "schedule", "select", "success", "tick", "time", "todo", "when"]}, {"name": "Calendar with Day Focus", "id": "calendar-day", "unicode": "f783", "styles": ["solid"], "search_terms": ["date", "detail", "event", "focus", "schedule", "single day", "time", "today", "when"]}, {"name": "Calendar Minus", "id": "calendar-minus", "unicode": "f272", "styles": ["solid", "regular"], "search_terms": ["calendar", "date", "delete", "event", "negative", "remove", "schedule", "time", "when"]}, {"name": "Calendar Plus", "id": "calendar-plus", "unicode": "f271", "styles": ["solid", "regular"], "search_terms": ["add", "calendar", "create", "date", "event", "new", "positive", "schedule", "time", "when"]}, {"name": "Calendar Times", "id": "calendar-times", "unicode": "f273", "styles": ["solid", "regular"], "search_terms": ["archive", "calendar", "date", "delete", "event", "remove", "schedule", "time", "when", "x"]}, {"name": "Calendar with Week Focus", "id": "calendar-week", "unicode": "f784", "styles": ["solid"], "search_terms": ["date", "detail", "event", "focus", "schedule", "single week", "time", "today", "when"]}, {"name": "camera", "id": "camera", "unicode": "f030", "styles": ["solid"], "search_terms": ["image", "lens", "photo", "picture", "record", "shutter", "video"]}, {"name": "Retro Camera", "id": "camera-retro", "unicode": "f083", "styles": ["solid"], "search_terms": ["image", "lens", "photo", "picture", "record", "shutter", "video"]}, {"name": "Campground", "id": "campground", "unicode": "f6bb", "styles": ["solid"], "search_terms": ["camping", "fall", "outdoors", "teepee", "tent", "tipi"]}, {"name": "Canadian Maple Leaf", "id": "canadian-maple-leaf", "unicode": "f785", "styles": ["brands"], "search_terms": ["canada", "flag", "flora", "nature", "plant"]}, {"name": "<PERSON>", "id": "candy-cane", "unicode": "f786", "styles": ["solid"], "search_terms": ["candy", "christmas", "holiday", "mint", "peppermint", "striped", "xmas"]}, {"name": "Cannabis", "id": "cannabis", "unicode": "f55f", "styles": ["solid"], "search_terms": ["bud", "chronic", "drugs", "endica", "endo", "ganja", "marijuana", "mary jane", "pot", "reefer", "sativa", "spliff", "weed", "whacky-tabacky"]}, {"name": "Capsules", "id": "capsules", "unicode": "f46b", "styles": ["solid"], "search_terms": ["drugs", "medicine", "pills", "prescription"]}, {"name": "Car", "id": "car", "unicode": "f1b9", "styles": ["solid"], "search_terms": ["auto", "automobile", "sedan", "transportation", "travel", "vehicle"]}, {"name": "Alternate Car", "id": "car-alt", "unicode": "f5de", "styles": ["solid"], "search_terms": ["auto", "automobile", "sedan", "transportation", "travel", "vehicle"]}, {"name": "Car Battery", "id": "car-battery", "unicode": "f5df", "styles": ["solid"], "search_terms": ["auto", "electric", "mechanic", "power"]}, {"name": "Car Crash", "id": "car-crash", "unicode": "f5e1", "styles": ["solid"], "search_terms": ["accident", "auto", "automobile", "insurance", "sedan", "transportation", "vehicle", "wreck"]}, {"name": "Car Side", "id": "car-side", "unicode": "f5e4", "styles": ["solid"], "search_terms": ["auto", "automobile", "sedan", "transportation", "travel", "vehicle"]}, {"name": "Caret Down", "id": "caret-down", "unicode": "f0d7", "styles": ["solid"], "search_terms": ["arrow", "dropdown", "expand", "menu", "more", "triangle"]}, {"name": "Caret Left", "id": "caret-left", "unicode": "f0d9", "styles": ["solid"], "search_terms": ["arrow", "back", "previous", "triangle"]}, {"name": "Caret Right", "id": "caret-right", "unicode": "f0da", "styles": ["solid"], "search_terms": ["arrow", "forward", "next", "triangle"]}, {"name": "Caret Square Down", "id": "caret-square-down", "unicode": "f150", "styles": ["solid", "regular"], "search_terms": ["arrow", "caret-square-o-down", "dropdown", "expand", "menu", "more", "triangle"]}, {"name": "Caret Square Left", "id": "caret-square-left", "unicode": "f191", "styles": ["solid", "regular"], "search_terms": ["arrow", "back", "caret-square-o-left", "previous", "triangle"]}, {"name": "Caret Square Right", "id": "caret-square-right", "unicode": "f152", "styles": ["solid", "regular"], "search_terms": ["arrow", "caret-square-o-right", "forward", "next", "triangle"]}, {"name": "Caret Square Up", "id": "caret-square-up", "unicode": "f151", "styles": ["solid", "regular"], "search_terms": ["arrow", "caret-square-o-up", "collapse", "triangle", "upload"]}, {"name": "Caret Up", "id": "caret-up", "unicode": "f0d8", "styles": ["solid"], "search_terms": ["arrow", "collapse", "triangle"]}, {"name": "Carrot", "id": "carrot", "unicode": "f787", "styles": ["solid"], "search_terms": ["bugs bunny", "orange", "vegan", "vegetable"]}, {"name": "Shopping Cart Arrow Down", "id": "cart-arrow-down", "unicode": "f218", "styles": ["solid"], "search_terms": ["download", "save", "shopping"]}, {"name": "Add to Shopping Cart", "id": "cart-plus", "unicode": "f217", "styles": ["solid"], "search_terms": ["add", "create", "new", "positive", "shopping"]}, {"name": "Cash Register", "id": "cash-register", "unicode": "f788", "styles": ["solid"], "search_terms": ["buy", "cha-ching", "change", "checkout", "commerce", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "machine", "pay", "payment", "purchase", "store"]}, {"name": "Cat", "id": "cat", "unicode": "f6be", "styles": ["solid"], "search_terms": ["feline", "halloween", "holiday", "kitten", "kitty", "meow", "pet"]}, {"name": "Amazon Pay Credit Card", "id": "cc-amazon-pay", "unicode": "f42d", "styles": ["brands"], "search_terms": []}, {"name": "American Express Credit Card", "id": "cc-amex", "unicode": "f1f3", "styles": ["brands"], "search_terms": ["amex"]}, {"name": "Apple Pay Credit Card", "id": "cc-apple-pay", "unicode": "f416", "styles": ["brands"], "search_terms": []}, {"name": "Diner's Club Credit Card", "id": "cc-diners-club", "unicode": "f24c", "styles": ["brands"], "search_terms": []}, {"name": "Discover Credit Card", "id": "cc-discover", "unicode": "f1f2", "styles": ["brands"], "search_terms": []}, {"name": "JCB Credit Card", "id": "cc-jcb", "unicode": "f24b", "styles": ["brands"], "search_terms": []}, {"name": "MasterCard Credit Card", "id": "cc-mastercard", "unicode": "f1f1", "styles": ["brands"], "search_terms": []}, {"name": "Paypal Credit Card", "id": "cc-paypal", "unicode": "f1f4", "styles": ["brands"], "search_terms": []}, {"name": "Stripe Credit Card", "id": "cc-stripe", "unicode": "f1f5", "styles": ["brands"], "search_terms": []}, {"name": "Visa Credit Card", "id": "cc-visa", "unicode": "f1f0", "styles": ["brands"], "search_terms": []}, {"name": "Centercode", "id": "centercode", "unicode": "f380", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "centos", "unicode": "f789", "styles": ["brands"], "search_terms": ["linux", "operating system", "os"]}, {"name": "certificate", "id": "certificate", "unicode": "f0a3", "styles": ["solid"], "search_terms": ["badge", "star", "verified"]}, {"name": "Chair", "id": "chair", "unicode": "f6c0", "styles": ["solid"], "search_terms": ["furniture", "seat", "sit"]}, {"name": "Chalkboard", "id": "chalkboard", "unicode": "f51b", "styles": ["solid"], "search_terms": ["blackboard", "learning", "school", "teaching", "whiteboard", "writing"]}, {"name": "Chalkboard Teacher", "id": "chalkboard-teacher", "unicode": "f51c", "styles": ["solid"], "search_terms": ["blackboard", "instructor", "learning", "professor", "school", "whiteboard", "writing"]}, {"name": "Charging Station", "id": "charging-station", "unicode": "f5e7", "styles": ["solid"], "search_terms": ["electric", "ev", "tesla", "vehicle"]}, {"name": "Area Chart", "id": "chart-area", "unicode": "f1fe", "styles": ["solid"], "search_terms": ["analytics", "area", "chart", "graph"]}, {"name": "Bar Chart", "id": "chart-bar", "unicode": "f080", "styles": ["solid", "regular"], "search_terms": ["analytics", "bar", "chart", "graph"]}, {"name": "Line Chart", "id": "chart-line", "unicode": "f201", "styles": ["solid"], "search_terms": ["activity", "analytics", "chart", "dashboard", "gain", "graph", "increase", "line"]}, {"name": "Pie Chart", "id": "chart-pie", "unicode": "f200", "styles": ["solid"], "search_terms": ["analytics", "chart", "diagram", "graph", "pie"]}, {"name": "Check", "id": "check", "unicode": "f00c", "styles": ["solid"], "search_terms": ["accept", "agree", "checkmark", "confirm", "correct", "done", "notice", "notification", "notify", "ok", "select", "success", "tick", "todo", "yes"]}, {"name": "Check Circle", "id": "check-circle", "unicode": "f058", "styles": ["solid", "regular"], "search_terms": ["accept", "agree", "confirm", "correct", "done", "ok", "select", "success", "tick", "todo", "yes"]}, {"name": "Check Double", "id": "check-double", "unicode": "f560", "styles": ["solid"], "search_terms": ["accept", "agree", "checkmark", "confirm", "correct", "done", "notice", "notification", "notify", "ok", "select", "success", "tick", "todo"]}, {"name": "Check Square", "id": "check-square", "unicode": "f14a", "styles": ["solid", "regular"], "search_terms": ["accept", "agree", "checkmark", "confirm", "correct", "done", "ok", "select", "success", "tick", "todo", "yes"]}, {"name": "Cheese", "id": "cheese", "unicode": "f7ef", "styles": ["solid"], "search_terms": ["cheddar", "curd", "gouda", "melt", "parmesan", "sandwich", "swiss", "wedge"]}, {"name": "Chess", "id": "chess", "unicode": "f439", "styles": ["solid"], "search_terms": ["board", "checkmate", "game", "strategy", "tournament"]}, {"name": "Chess Bishop", "id": "chess-bishop", "unicode": "f43a", "styles": ["solid"], "search_terms": ["board", "checkmate", "game", "strategy"]}, {"name": "Chess Board", "id": "chess-board", "unicode": "f43c", "styles": ["solid"], "search_terms": ["board", "checkmate", "game", "strategy"]}, {"name": "Chess King", "id": "chess-king", "unicode": "f43f", "styles": ["solid"], "search_terms": ["board", "checkmate", "game", "strategy"]}, {"name": "Chess Knight", "id": "chess-knight", "unicode": "f441", "styles": ["solid"], "search_terms": ["board", "checkmate", "game", "horse", "strategy"]}, {"name": "Chess Pawn", "id": "chess-pawn", "unicode": "f443", "styles": ["solid"], "search_terms": ["board", "checkmate", "game", "strategy"]}, {"name": "Chess Queen", "id": "chess-queen", "unicode": "f445", "styles": ["solid"], "search_terms": ["board", "checkmate", "game", "strategy"]}, {"name": "Chess Rook", "id": "chess-rook", "unicode": "f447", "styles": ["solid"], "search_terms": ["board", "castle", "checkmate", "game", "strategy"]}, {"name": "Chevron Circle Down", "id": "chevron-circle-down", "unicode": "f13a", "styles": ["solid"], "search_terms": ["arrow", "download", "dropdown", "menu", "more"]}, {"name": "Chevron Circle Left", "id": "chevron-circle-left", "unicode": "f137", "styles": ["solid"], "search_terms": ["arrow", "back", "previous"]}, {"name": "Chevron Circle Right", "id": "chevron-circle-right", "unicode": "f138", "styles": ["solid"], "search_terms": ["arrow", "forward", "next"]}, {"name": "Chevron Circle Up", "id": "chevron-circle-up", "unicode": "f139", "styles": ["solid"], "search_terms": ["arrow", "collapse", "upload"]}, {"name": "chevron-down", "id": "chevron-down", "unicode": "f078", "styles": ["solid"], "search_terms": ["arrow", "download", "expand"]}, {"name": "chevron-left", "id": "chevron-left", "unicode": "f053", "styles": ["solid"], "search_terms": ["arrow", "back", "bracket", "previous"]}, {"name": "chevron-right", "id": "chevron-right", "unicode": "f054", "styles": ["solid"], "search_terms": ["arrow", "bracket", "forward", "next"]}, {"name": "chevron-up", "id": "chevron-up", "unicode": "f077", "styles": ["solid"], "search_terms": ["arrow", "collapse", "upload"]}, {"name": "Child", "id": "child", "unicode": "f1ae", "styles": ["solid"], "search_terms": ["boy", "girl", "kid", "toddler", "young"]}, {"name": "Chrome", "id": "chrome", "unicode": "f268", "styles": ["brands"], "search_terms": ["browser"]}, {"name": "Chromecast", "id": "chromecast", "unicode": "f838", "styles": ["brands"], "search_terms": []}, {"name": "Church", "id": "church", "unicode": "f51d", "styles": ["solid"], "search_terms": ["building", "cathedral", "chapel", "community", "religion"]}, {"name": "Circle", "id": "circle", "unicode": "f111", "styles": ["solid", "regular"], "search_terms": ["circle-thin", "diameter", "dot", "ellipse", "notification", "round"]}, {"name": "Circle Notched", "id": "circle-notch", "unicode": "f1ce", "styles": ["solid"], "search_terms": ["circle-o-notch", "diameter", "dot", "ellipse", "round", "spinner"]}, {"name": "City", "id": "city", "unicode": "f64f", "styles": ["solid"], "search_terms": ["buildings", "busy", "skyscrapers", "urban", "windows"]}, {"name": "Medical Clinic", "id": "clinic-medical", "unicode": "f7f2", "styles": ["solid"], "search_terms": ["doctor", "general practitioner", "hospital", "infirmary", "medicine", "office", "outpatient"]}, {"name": "Clipboard", "id": "clipboard", "unicode": "f328", "styles": ["solid", "regular"], "search_terms": ["copy", "notes", "paste", "record"]}, {"name": "Clipboard with Check", "id": "clipboard-check", "unicode": "f46c", "styles": ["solid"], "search_terms": ["accept", "agree", "confirm", "done", "ok", "select", "success", "tick", "todo", "yes"]}, {"name": "Clipboard List", "id": "clipboard-list", "unicode": "f46d", "styles": ["solid"], "search_terms": ["checklist", "completed", "done", "finished", "intinerary", "ol", "schedule", "tick", "todo", "ul"]}, {"name": "Clock", "id": "clock", "unicode": "f017", "styles": ["solid", "regular"], "search_terms": ["date", "late", "schedule", "time", "timer", "timestamp", "watch"]}, {"name": "<PERSON><PERSON>", "id": "clone", "unicode": "f24d", "styles": ["solid", "regular"], "search_terms": ["arrange", "copy", "duplicate", "paste"]}, {"name": "Closed Captioning", "id": "closed-captioning", "unicode": "f20a", "styles": ["solid", "regular"], "search_terms": ["cc", "deaf", "hearing", "subtitle", "subtitling", "text", "video"]}, {"name": "Cloud", "id": "cloud", "unicode": "f0c2", "styles": ["solid"], "search_terms": ["atmosphere", "fog", "overcast", "save", "upload", "weather"]}, {"name": "Alternate Cloud Download", "id": "cloud-download-alt", "unicode": "f381", "styles": ["solid"], "search_terms": ["download", "export", "save"]}, {"name": "Cloud with (a chance of) Meatball", "id": "cloud-meatball", "unicode": "f73b", "styles": ["solid"], "search_terms": ["FLDSMDFR", "food", "spaghetti", "storm"]}, {"name": "Cloud with Moon", "id": "cloud-moon", "unicode": "f6c3", "styles": ["solid"], "search_terms": ["crescent", "evening", "lunar", "night", "partly cloudy", "sky"]}, {"name": "Cloud with Moon and Rain", "id": "cloud-moon-rain", "unicode": "f73c", "styles": ["solid"], "search_terms": ["crescent", "evening", "lunar", "night", "partly cloudy", "precipitation", "rain", "sky", "storm"]}, {"name": "Cloud with Rain", "id": "cloud-rain", "unicode": "f73d", "styles": ["solid"], "search_terms": ["precipitation", "rain", "sky", "storm"]}, {"name": "Cloud with Heavy Showers", "id": "cloud-showers-heavy", "unicode": "f740", "styles": ["solid"], "search_terms": ["precipitation", "rain", "sky", "storm"]}, {"name": "Cloud with Sun", "id": "cloud-sun", "unicode": "f6c4", "styles": ["solid"], "search_terms": ["clear", "day", "daytime", "fall", "outdoors", "overcast", "partly cloudy"]}, {"name": "Cloud with Sun and Rain", "id": "cloud-sun-rain", "unicode": "f743", "styles": ["solid"], "search_terms": ["day", "overcast", "precipitation", "storm", "summer", "sunshower"]}, {"name": "Alternate Cloud Upload", "id": "cloud-upload-alt", "unicode": "f382", "styles": ["solid"], "search_terms": ["cloud-upload", "import", "save", "upload"]}, {"name": "cloudscale.ch", "id": "cloudscale", "unicode": "f383", "styles": ["brands"], "search_terms": []}, {"name": "Cloudsmith", "id": "cloudsmith", "unicode": "f384", "styles": ["brands"], "search_terms": []}, {"name": "cloudversify", "id": "cloudversify", "unicode": "f385", "styles": ["brands"], "search_terms": []}, {"name": "Cocktail", "id": "cocktail", "unicode": "f561", "styles": ["solid"], "search_terms": ["alcohol", "beverage", "drink", "gin", "glass", "margarita", "martini", "vodka"]}, {"name": "Code", "id": "code", "unicode": "f121", "styles": ["solid"], "search_terms": ["brackets", "code", "development", "html"]}, {"name": "Code Branch", "id": "code-branch", "unicode": "f126", "styles": ["solid"], "search_terms": ["branch", "code-fork", "fork", "git", "github", "rebase", "svn", "vcs", "version"]}, {"name": "Codepen", "id": "codepen", "unicode": "f1cb", "styles": ["brands"], "search_terms": []}, {"name": "Codie <PERSON>", "id": "codiepie", "unicode": "f284", "styles": ["brands"], "search_terms": []}, {"name": "Coffee", "id": "coffee", "unicode": "f0f4", "styles": ["solid"], "search_terms": ["beverage", "breakfast", "cafe", "drink", "fall", "morning", "mug", "seasonal", "tea"]}, {"name": "cog", "id": "cog", "unicode": "f013", "styles": ["solid"], "search_terms": ["gear", "mechanical", "settings", "sprocket", "wheel"]}, {"name": "cogs", "id": "cogs", "unicode": "f085", "styles": ["solid"], "search_terms": ["gears", "mechanical", "settings", "sprocket", "wheel"]}, {"name": "Coins", "id": "coins", "unicode": "f51e", "styles": ["solid"], "search_terms": ["currency", "dime", "financial", "gold", "money", "penny"]}, {"name": "Columns", "id": "columns", "unicode": "f0db", "styles": ["solid"], "search_terms": ["browser", "dashboard", "organize", "panes", "split"]}, {"name": "comment", "id": "comment", "unicode": "f075", "styles": ["solid", "regular"], "search_terms": ["bubble", "chat", "commenting", "conversation", "feedback", "message", "note", "notification", "sms", "speech", "texting"]}, {"name": "Alternate Comment", "id": "comment-alt", "unicode": "f27a", "styles": ["solid", "regular"], "search_terms": ["bubble", "chat", "commenting", "conversation", "feedback", "message", "note", "notification", "sms", "speech", "texting"]}, {"name": "Comment Dollar", "id": "comment-dollar", "unicode": "f651", "styles": ["solid"], "search_terms": ["bubble", "chat", "commenting", "conversation", "feedback", "message", "money", "note", "notification", "pay", "sms", "speech", "spend", "texting", "transfer"]}, {"name": "Comment Dots", "id": "comment-dots", "unicode": "f4ad", "styles": ["solid", "regular"], "search_terms": ["bubble", "chat", "commenting", "conversation", "feedback", "message", "more", "note", "notification", "reply", "sms", "speech", "texting"]}, {"name": "Alternate Medical Chat", "id": "comment-medical", "unicode": "f7f5", "styles": ["solid"], "search_terms": ["advice", "bubble", "chat", "commenting", "conversation", "diagnose", "feedback", "message", "note", "notification", "prescription", "sms", "speech", "texting"]}, {"name": "Comment Slash", "id": "comment-slash", "unicode": "f4b3", "styles": ["solid"], "search_terms": ["bubble", "cancel", "chat", "commenting", "conversation", "feedback", "message", "mute", "note", "notification", "quiet", "sms", "speech", "texting"]}, {"name": "comments", "id": "comments", "unicode": "f086", "styles": ["solid", "regular"], "search_terms": ["bubble", "chat", "commenting", "conversation", "feedback", "message", "note", "notification", "sms", "speech", "texting"]}, {"name": "Comments Dollar", "id": "comments-dollar", "unicode": "f653", "styles": ["solid"], "search_terms": ["bubble", "chat", "commenting", "conversation", "feedback", "message", "money", "note", "notification", "pay", "sms", "speech", "spend", "texting", "transfer"]}, {"name": "Compact Disc", "id": "compact-disc", "unicode": "f51f", "styles": ["solid"], "search_terms": ["album", "bluray", "cd", "disc", "dvd", "media", "movie", "music", "record", "video", "vinyl"]}, {"name": "<PERSON>mp<PERSON>", "id": "compass", "unicode": "f14e", "styles": ["solid", "regular"], "search_terms": ["directions", "directory", "location", "menu", "navigation", "safari", "travel"]}, {"name": "Compress", "id": "compress", "unicode": "f066", "styles": ["solid"], "search_terms": ["collapse", "fullscreen", "minimize", "move", "resize", "shrink", "smaller"]}, {"name": "Alternate Compress <PERSON>", "id": "compress-arrows-alt", "unicode": "f78c", "styles": ["solid"], "search_terms": ["collapse", "fullscreen", "minimize", "move", "resize", "shrink", "smaller"]}, {"name": "Concierge Bell", "id": "concierge-bell", "unicode": "f562", "styles": ["solid"], "search_terms": ["attention", "hotel", "receptionist", "service", "support"]}, {"name": "Confluence", "id": "confluence", "unicode": "f78d", "styles": ["brands"], "search_terms": ["atlassian"]}, {"name": "Connect Develop", "id": "connectdevelop", "unicode": "f20e", "styles": ["brands"], "search_terms": []}, {"name": "Con<PERSON>o", "id": "contao", "unicode": "f26d", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "cookie", "unicode": "f563", "styles": ["solid"], "search_terms": ["baked good", "chips", "chocolate", "eat", "snack", "sweet", "treat"]}, {"name": "<PERSON><PERSON>", "id": "cookie-bite", "unicode": "f564", "styles": ["solid"], "search_terms": ["baked good", "bitten", "chips", "chocolate", "eat", "snack", "sweet", "treat"]}, {"name": "Copy", "id": "copy", "unicode": "f0c5", "styles": ["solid", "regular"], "search_terms": ["clone", "duplicate", "file", "files-o", "paper", "paste"]}, {"name": "Copyright", "id": "copyright", "unicode": "f1f9", "styles": ["solid", "regular"], "search_terms": ["brand", "mark", "register", "trademark"]}, {"name": "<PERSON><PERSON>", "id": "couch", "unicode": "f4b8", "styles": ["solid"], "search_terms": ["chair", "cushion", "furniture", "relax", "sofa"]}, {"name": "cPanel", "id": "cpanel", "unicode": "f388", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons", "id": "creative-commons", "unicode": "f25e", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons Attribution", "id": "creative-commons-by", "unicode": "f4e7", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons Noncommercial", "id": "creative-commons-nc", "unicode": "f4e8", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons Noncommercial (Euro Sign)", "id": "creative-commons-nc-eu", "unicode": "f4e9", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons Noncommercial (Yen Sign)", "id": "creative-commons-nc-jp", "unicode": "f4ea", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons No Derivative Works", "id": "creative-commons-nd", "unicode": "f4eb", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons Public Domain", "id": "creative-commons-pd", "unicode": "f4ec", "styles": ["brands"], "search_terms": []}, {"name": "Alternate Creative Commons Public Domain", "id": "creative-commons-pd-alt", "unicode": "f4ed", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons Remix", "id": "creative-commons-remix", "unicode": "f4ee", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons Share Alike", "id": "creative-commons-sa", "unicode": "f4ef", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons Sampling", "id": "creative-commons-sampling", "unicode": "f4f0", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons Sampling +", "id": "creative-commons-sampling-plus", "unicode": "f4f1", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons Share", "id": "creative-commons-share", "unicode": "f4f2", "styles": ["brands"], "search_terms": []}, {"name": "Creative Commons CC0", "id": "creative-commons-zero", "unicode": "f4f3", "styles": ["brands"], "search_terms": []}, {"name": "Credit Card", "id": "credit-card", "unicode": "f09d", "styles": ["solid", "regular"], "search_terms": ["buy", "checkout", "credit-card-alt", "debit", "money", "payment", "purchase"]}, {"name": "Critical Role", "id": "critical-role", "unicode": "f6c9", "styles": ["brands"], "search_terms": ["Dungeons & Dragons", "d&d", "dnd", "fantasy", "game", "gaming", "tabletop"]}, {"name": "crop", "id": "crop", "unicode": "f125", "styles": ["solid"], "search_terms": ["design", "frame", "mask", "resize", "shrink"]}, {"name": "Alternate Crop", "id": "crop-alt", "unicode": "f565", "styles": ["solid"], "search_terms": ["design", "frame", "mask", "resize", "shrink"]}, {"name": "Cross", "id": "cross", "unicode": "f654", "styles": ["solid"], "search_terms": ["catholicism", "christianity", "church", "jesus"]}, {"name": "Crosshairs", "id": "crosshairs", "unicode": "f05b", "styles": ["solid"], "search_terms": ["aim", "bullseye", "gpd", "picker", "position"]}, {"name": "Crow", "id": "crow", "unicode": "f520", "styles": ["solid"], "search_terms": ["bird", "bullfrog", "fauna", "halloween", "holiday", "toad"]}, {"name": "Crown", "id": "crown", "unicode": "f521", "styles": ["solid"], "search_terms": ["award", "favorite", "king", "queen", "royal", "tiara"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "crutch", "unicode": "f7f7", "styles": ["solid"], "search_terms": ["cane", "injury", "mobility", "wheelchair"]}, {"name": "CSS 3 Logo", "id": "css3", "unicode": "f13c", "styles": ["brands"], "search_terms": ["code"]}, {"name": "Alternate CSS3 Logo", "id": "css3-alt", "unicode": "f38b", "styles": ["brands"], "search_terms": []}, {"name": "C<PERSON>", "id": "cube", "unicode": "f1b2", "styles": ["solid"], "search_terms": ["3d", "block", "dice", "package", "square", "tesseract"]}, {"name": "Cubes", "id": "cubes", "unicode": "f1b3", "styles": ["solid"], "search_terms": ["3d", "block", "dice", "package", "pyramid", "square", "stack", "tesseract"]}, {"name": "Cut", "id": "cut", "unicode": "f0c4", "styles": ["solid"], "search_terms": ["clip", "scissors", "snip"]}, {"name": "Cuttlefish", "id": "cuttlefish", "unicode": "f38c", "styles": ["brands"], "search_terms": []}, {"name": "Dungeons & Dragons", "id": "d-and-d", "unicode": "f38d", "styles": ["brands"], "search_terms": []}, {"name": "D&D Beyond", "id": "d-and-d-beyond", "unicode": "f6ca", "styles": ["brands"], "search_terms": ["Dungeons & Dragons", "d&d", "dnd", "fantasy", "gaming", "tabletop"]}, {"name": "DashCube", "id": "dashcube", "unicode": "f210", "styles": ["brands"], "search_terms": []}, {"name": "Database", "id": "database", "unicode": "f1c0", "styles": ["solid"], "search_terms": ["computer", "development", "directory", "memory", "storage"]}, {"name": "Deaf", "id": "deaf", "unicode": "f2a4", "styles": ["solid"], "search_terms": ["ear", "hearing", "sign language"]}, {"name": "Delicious", "id": "delicious", "unicode": "f1a5", "styles": ["brands"], "search_terms": []}, {"name": "Democrat", "id": "democrat", "unicode": "f747", "styles": ["solid"], "search_terms": ["american", "democratic party", "donkey", "election", "left", "left-wing", "liberal", "politics", "usa"]}, {"name": "deploy.dog", "id": "deploydog", "unicode": "f38e", "styles": ["brands"], "search_terms": []}, {"name": "Deskpro", "id": "deskpro", "unicode": "f38f", "styles": ["brands"], "search_terms": []}, {"name": "Desktop", "id": "desktop", "unicode": "f108", "styles": ["solid"], "search_terms": ["computer", "cpu", "demo", "desktop", "device", "imac", "machine", "monitor", "pc", "screen"]}, {"name": "DEV", "id": "dev", "unicode": "f6cc", "styles": ["brands"], "search_terms": []}, {"name": "deviantART", "id": "deviantart", "unicode": "f1bd", "styles": ["brands"], "search_terms": []}, {"name": "Dharmachakra", "id": "dharma<PERSON><PERSON>", "unicode": "f655", "styles": ["solid"], "search_terms": ["buddhism", "buddhist", "wheel of dharma"]}, {"name": "DHL", "id": "dhl", "unicode": "f790", "styles": ["brands"], "search_terms": ["<PERSON><PERSON>", "Hill<PERSON><PERSON>m and Lynn", "german", "package", "shipping"]}, {"name": "Diagnoses", "id": "diagnoses", "unicode": "f470", "styles": ["solid"], "search_terms": ["analyze", "detect", "diagnosis", "examine", "medicine"]}, {"name": "Diaspora", "id": "diaspora", "unicode": "f791", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "dice", "unicode": "f522", "styles": ["solid"], "search_terms": ["chance", "gambling", "game", "roll"]}, {"name": "Dice D20", "id": "dice-d20", "unicode": "f6cf", "styles": ["solid"], "search_terms": ["Dungeons & Dragons", "chance", "d&d", "dnd", "fantasy", "gambling", "game", "roll"]}, {"name": "Dice D6", "id": "dice-d6", "unicode": "f6d1", "styles": ["solid"], "search_terms": ["Dungeons & Dragons", "chance", "d&d", "dnd", "fantasy", "gambling", "game", "roll"]}, {"name": "<PERSON>ce <PERSON>", "id": "dice-five", "unicode": "f523", "styles": ["solid"], "search_terms": ["chance", "gambling", "game", "roll"]}, {"name": "Dice Four", "id": "dice-four", "unicode": "f524", "styles": ["solid"], "search_terms": ["chance", "gambling", "game", "roll"]}, {"name": "Dice One", "id": "dice-one", "unicode": "f525", "styles": ["solid"], "search_terms": ["chance", "gambling", "game", "roll"]}, {"name": "<PERSON><PERSON>", "id": "dice-six", "unicode": "f526", "styles": ["solid"], "search_terms": ["chance", "gambling", "game", "roll"]}, {"name": "<PERSON><PERSON>", "id": "dice-three", "unicode": "f527", "styles": ["solid"], "search_terms": ["chance", "gambling", "game", "roll"]}, {"name": "Dice Two", "id": "dice-two", "unicode": "f528", "styles": ["solid"], "search_terms": ["chance", "gambling", "game", "roll"]}, {"name": "<PERSON><PERSON>", "id": "digg", "unicode": "f1a6", "styles": ["brands"], "search_terms": []}, {"name": "Digital Ocean", "id": "digital-ocean", "unicode": "f391", "styles": ["brands"], "search_terms": []}, {"name": "Digital Tachograph", "id": "digital-tachograph", "unicode": "f566", "styles": ["solid"], "search_terms": ["data", "distance", "speed", "tachometer"]}, {"name": "Directions", "id": "directions", "unicode": "f5eb", "styles": ["solid"], "search_terms": ["map", "navigation", "sign", "turn"]}, {"name": "Discord", "id": "discord", "unicode": "f392", "styles": ["brands"], "search_terms": []}, {"name": "Discourse", "id": "discourse", "unicode": "f393", "styles": ["brands"], "search_terms": []}, {"name": "Divide", "id": "divide", "unicode": "f529", "styles": ["solid"], "search_terms": ["arithmetic", "calculus", "division", "math"]}, {"name": "Dizzy <PERSON>", "id": "dizzy", "unicode": "f567", "styles": ["solid", "regular"], "search_terms": ["dazed", "dead", "disapprove", "emoticon", "face"]}, {"name": "DNA", "id": "dna", "unicode": "f471", "styles": ["solid"], "search_terms": ["double helix", "genetic", "helix", "molecule", "protein"]}, {"name": "DocHub", "id": "dochub", "unicode": "f394", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON>er", "id": "docker", "unicode": "f395", "styles": ["brands"], "search_terms": []}, {"name": "Dog", "id": "dog", "unicode": "f6d3", "styles": ["solid"], "search_terms": ["animal", "canine", "fauna", "mammal", "pet", "pooch", "puppy", "woof"]}, {"name": "Dollar Sign", "id": "dollar-sign", "unicode": "f155", "styles": ["solid"], "search_terms": ["$", "cost", "dollar-sign", "money", "price", "usd"]}, {"name": "<PERSON>", "id": "dolly", "unicode": "f472", "styles": ["solid"], "search_terms": ["carry", "shipping", "transport"]}, {"name": "<PERSON>", "id": "dolly-flatbed", "unicode": "f474", "styles": ["solid"], "search_terms": ["carry", "inventory", "shipping", "transport"]}, {"name": "Donate", "id": "donate", "unicode": "f4b9", "styles": ["solid"], "search_terms": ["contribute", "generosity", "gift", "give"]}, {"name": "Door Closed", "id": "door-closed", "unicode": "f52a", "styles": ["solid"], "search_terms": ["enter", "exit", "locked"]}, {"name": "Door Open", "id": "door-open", "unicode": "f52b", "styles": ["solid"], "search_terms": ["enter", "exit", "welcome"]}, {"name": "Dot Circle", "id": "dot-circle", "unicode": "f192", "styles": ["solid", "regular"], "search_terms": ["bullseye", "notification", "target"]}, {"name": "Dove", "id": "dove", "unicode": "f4ba", "styles": ["solid"], "search_terms": ["bird", "fauna", "flying", "peace", "war"]}, {"name": "Download", "id": "download", "unicode": "f019", "styles": ["solid"], "search_terms": ["export", "hard drive", "save", "transfer"]}, {"name": "Draft2digital", "id": "draft2digital", "unicode": "f396", "styles": ["brands"], "search_terms": []}, {"name": "Drafting Compass", "id": "drafting-compass", "unicode": "f568", "styles": ["solid"], "search_terms": ["design", "map", "mechanical drawing", "plot", "plotting"]}, {"name": "Dragon", "id": "dragon", "unicode": "f6d5", "styles": ["solid"], "search_terms": ["Dungeons & Dragons", "d&d", "dnd", "fantasy", "fire", "lizard", "serpent"]}, {"name": "Draw Polygon", "id": "draw-polygon", "unicode": "f5ee", "styles": ["solid"], "search_terms": ["anchors", "lines", "object", "render", "shape"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "dribbble", "unicode": "f17d", "styles": ["brands"], "search_terms": []}, {"name": "Dribbble Square", "id": "dribbble-square", "unicode": "f397", "styles": ["brands"], "search_terms": []}, {"name": "Dropbox", "id": "dropbox", "unicode": "f16b", "styles": ["brands"], "search_terms": []}, {"name": "Drum", "id": "drum", "unicode": "f569", "styles": ["solid"], "search_terms": ["instrument", "music", "percussion", "snare", "sound"]}, {"name": "Drum Steelpan", "id": "drum-steelpan", "unicode": "f56a", "styles": ["solid"], "search_terms": ["calypso", "instrument", "music", "percussion", "reggae", "snare", "sound", "steel", "tropical"]}, {"name": "Drumstick with <PERSON><PERSON> Taken Out", "id": "drumstick-bite", "unicode": "f6d7", "styles": ["solid"], "search_terms": ["bone", "chicken", "leg", "meat", "poultry", "turkey"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "drupal", "unicode": "f1a9", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "dumbbell", "unicode": "f44b", "styles": ["solid"], "search_terms": ["exercise", "gym", "strength", "weight", "weight-lifting"]}, {"name": "Dumpster", "id": "dumpster", "unicode": "f793", "styles": ["solid"], "search_terms": ["alley", "bin", "commercial", "trash", "waste"]}, {"name": "Dumpster Fire", "id": "dumpster-fire", "unicode": "f794", "styles": ["solid"], "search_terms": ["alley", "bin", "commercial", "danger", "dangerous", "euphemism", "flame", "heat", "hot", "trash", "waste"]}, {"name": "Dungeon", "id": "dungeon", "unicode": "f6d9", "styles": ["solid"], "search_terms": ["Dungeons & Dragons", "building", "d&d", "dnd", "door", "entrance", "fantasy", "gate"]}, {"name": "Dyalog", "id": "dyalog", "unicode": "f399", "styles": ["brands"], "search_terms": []}, {"name": "Earlybirds", "id": "earlybirds", "unicode": "f39a", "styles": ["brands"], "search_terms": []}, {"name": "eBay", "id": "ebay", "unicode": "f4f4", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON> Browser", "id": "edge", "unicode": "f282", "styles": ["brands"], "search_terms": ["browser", "ie"]}, {"name": "Edit", "id": "edit", "unicode": "f044", "styles": ["solid", "regular"], "search_terms": ["edit", "pen", "pencil", "update", "write"]}, {"name": "Egg", "id": "egg", "unicode": "f7fb", "styles": ["solid"], "search_terms": ["breakfast", "chicken", "easter", "shell", "yolk"]}, {"name": "eject", "id": "eject", "unicode": "f052", "styles": ["solid"], "search_terms": ["abort", "cancel", "cd", "discharge"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "elementor", "unicode": "f430", "styles": ["brands"], "search_terms": []}, {"name": "Horizontal Ellipsis", "id": "ellipsis-h", "unicode": "f141", "styles": ["solid"], "search_terms": ["dots", "drag", "kebab", "list", "menu", "nav", "navigation", "ol", "reorder", "settings", "ul"]}, {"name": "Vertical Ellipsis", "id": "ellipsis-v", "unicode": "f142", "styles": ["solid"], "search_terms": ["dots", "drag", "kebab", "list", "menu", "nav", "navigation", "ol", "reorder", "settings", "ul"]}, {"name": "<PERSON><PERSON>", "id": "ello", "unicode": "f5f1", "styles": ["brands"], "search_terms": []}, {"name": "Ember", "id": "ember", "unicode": "f423", "styles": ["brands"], "search_terms": []}, {"name": "Galactic Empire", "id": "empire", "unicode": "f1d1", "styles": ["brands"], "search_terms": []}, {"name": "Envelope", "id": "envelope", "unicode": "f0e0", "styles": ["solid", "regular"], "search_terms": ["e-mail", "email", "letter", "mail", "message", "notification", "support"]}, {"name": "Envelope Open", "id": "envelope-open", "unicode": "f2b6", "styles": ["solid", "regular"], "search_terms": ["e-mail", "email", "letter", "mail", "message", "notification", "support"]}, {"name": "Envelope Open-text", "id": "envelope-open-text", "unicode": "f658", "styles": ["solid"], "search_terms": ["e-mail", "email", "letter", "mail", "message", "notification", "support"]}, {"name": "Envelope Square", "id": "envelope-square", "unicode": "f199", "styles": ["solid"], "search_terms": ["e-mail", "email", "letter", "mail", "message", "notification", "support"]}, {"name": "Envira Gallery", "id": "envira", "unicode": "f299", "styles": ["brands"], "search_terms": ["leaf"]}, {"name": "Equals", "id": "equals", "unicode": "f52c", "styles": ["solid"], "search_terms": ["arithmetic", "even", "match", "math"]}, {"name": "eraser", "id": "eraser", "unicode": "f12d", "styles": ["solid"], "search_terms": ["art", "delete", "remove", "rubber"]}, {"name": "Erl<PERSON>", "id": "erlang", "unicode": "f39d", "styles": ["brands"], "search_terms": []}, {"name": "Ethereum", "id": "ethereum", "unicode": "f42e", "styles": ["brands"], "search_terms": []}, {"name": "Ethernet", "id": "ethernet", "unicode": "f796", "styles": ["solid"], "search_terms": ["cable", "cat 5", "cat 6", "connection", "hardware", "internet", "network", "wired"]}, {"name": "Etsy", "id": "etsy", "unicode": "f2d7", "styles": ["brands"], "search_terms": []}, {"name": "Euro Sign", "id": "euro-sign", "unicode": "f153", "styles": ["solid"], "search_terms": ["currency", "dollar", "exchange", "money"]}, {"name": "Evernote", "id": "evernote", "unicode": "f839", "styles": ["brands"], "search_terms": []}, {"name": "Alternate Exchange", "id": "exchange-alt", "unicode": "f362", "styles": ["solid"], "search_terms": ["arrow", "arrows", "exchange", "reciprocate", "return", "swap", "transfer"]}, {"name": "exclamation", "id": "exclamation", "unicode": "f12a", "styles": ["solid"], "search_terms": ["alert", "danger", "error", "important", "notice", "notification", "notify", "problem", "warning"]}, {"name": "Exclamation Circle", "id": "exclamation-circle", "unicode": "f06a", "styles": ["solid"], "search_terms": ["alert", "danger", "error", "important", "notice", "notification", "notify", "problem", "warning"]}, {"name": "Exclamation Triangle", "id": "exclamation-triangle", "unicode": "f071", "styles": ["solid"], "search_terms": ["alert", "danger", "error", "important", "notice", "notification", "notify", "problem", "warning"]}, {"name": "Expand", "id": "expand", "unicode": "f065", "styles": ["solid"], "search_terms": ["arrow", "bigger", "enlarge", "resize"]}, {"name": "Alternate Expand Arrows", "id": "expand-arrows-alt", "unicode": "f31e", "styles": ["solid"], "search_terms": ["arrows-alt", "bigger", "enlarge", "move", "resize"]}, {"name": "ExpeditedSSL", "id": "expeditedssl", "unicode": "f23e", "styles": ["brands"], "search_terms": []}, {"name": "Alternate External Link", "id": "external-link-alt", "unicode": "f35d", "styles": ["solid"], "search_terms": ["external-link", "new", "open", "share"]}, {"name": "Alternate External Link Square", "id": "external-link-square-alt", "unicode": "f360", "styles": ["solid"], "search_terms": ["external-link-square", "new", "open", "share"]}, {"name": "Eye", "id": "eye", "unicode": "f06e", "styles": ["solid", "regular"], "search_terms": ["look", "optic", "see", "seen", "show", "sight", "views", "visible"]}, {"name": "Eye Dropper", "id": "eye-dropper", "unicode": "f1fb", "styles": ["solid"], "search_terms": ["beaker", "clone", "color", "copy", "eyedropper", "pipette"]}, {"name": "Eye Slash", "id": "eye-slash", "unicode": "f070", "styles": ["solid", "regular"], "search_terms": ["blind", "hide", "show", "toggle", "unseen", "views", "visible", "visiblity"]}, {"name": "Facebook", "id": "facebook", "unicode": "f09a", "styles": ["brands"], "search_terms": ["facebook-official", "social network"]}, {"name": "Facebook F", "id": "facebook-f", "unicode": "f39e", "styles": ["brands"], "search_terms": ["facebook"]}, {"name": "Facebook Messenger", "id": "facebook-messenger", "unicode": "f39f", "styles": ["brands"], "search_terms": []}, {"name": "Facebook Square", "id": "facebook-square", "unicode": "f082", "styles": ["brands"], "search_terms": ["social network"]}, {"name": "Fantasy Flight-games", "id": "fantasy-flight-games", "unicode": "f6dc", "styles": ["brands"], "search_terms": ["Dungeons & Dragons", "d&d", "dnd", "fantasy", "game", "gaming", "tabletop"]}, {"name": "fast-backward", "id": "fast-backward", "unicode": "f049", "styles": ["solid"], "search_terms": ["beginning", "first", "previous", "rewind", "start"]}, {"name": "fast-forward", "id": "fast-forward", "unicode": "f050", "styles": ["solid"], "search_terms": ["end", "last", "next"]}, {"name": "Fax", "id": "fax", "unicode": "f1ac", "styles": ["solid"], "search_terms": ["business", "communicate", "copy", "facsimile", "send"]}, {"name": "<PERSON><PERSON>", "id": "feather", "unicode": "f52d", "styles": ["solid"], "search_terms": ["bird", "light", "plucked", "quill", "write"]}, {"name": "<PERSON> Feather", "id": "feather-alt", "unicode": "f56b", "styles": ["solid"], "search_terms": ["bird", "light", "plucked", "quill", "write"]}, {"name": "FedEx", "id": "fedex", "unicode": "f797", "styles": ["brands"], "search_terms": ["Federal Express", "package", "shipping"]}, {"name": "<PERSON><PERSON>", "id": "fedora", "unicode": "f798", "styles": ["brands"], "search_terms": ["linux", "operating system", "os"]}, {"name": "Female", "id": "female", "unicode": "f182", "styles": ["solid"], "search_terms": ["human", "person", "profile", "user", "woman"]}, {"name": "fighter-jet", "id": "fighter-jet", "unicode": "f0fb", "styles": ["solid"], "search_terms": ["airplane", "fast", "fly", "goose", "maverick", "plane", "quick", "top gun", "transportation", "travel"]}, {"name": "Figma", "id": "figma", "unicode": "f799", "styles": ["brands"], "search_terms": ["app", "design", "interface"]}, {"name": "File", "id": "file", "unicode": "f15b", "styles": ["solid", "regular"], "search_terms": ["document", "new", "page", "pdf", "resume"]}, {"name": "Alternate File", "id": "file-alt", "unicode": "f15c", "styles": ["solid", "regular"], "search_terms": ["document", "file-text", "invoice", "new", "page", "pdf"]}, {"name": "Archive File", "id": "file-archive", "unicode": "f1c6", "styles": ["solid", "regular"], "search_terms": [".zip", "bundle", "compress", "compression", "download", "zip"]}, {"name": "Audio File", "id": "file-audio", "unicode": "f1c7", "styles": ["solid", "regular"], "search_terms": ["document", "mp3", "music", "page", "play", "sound"]}, {"name": "Code File", "id": "file-code", "unicode": "f1c9", "styles": ["solid", "regular"], "search_terms": ["css", "development", "document", "html"]}, {"name": "File Contract", "id": "file-contract", "unicode": "f56c", "styles": ["solid"], "search_terms": ["agreement", "binding", "document", "legal", "signature"]}, {"name": "File CSV", "id": "file-csv", "unicode": "f6dd", "styles": ["solid"], "search_terms": ["document", "excel", "numbers", "spreadsheets", "table"]}, {"name": "File Download", "id": "file-download", "unicode": "f56d", "styles": ["solid"], "search_terms": ["document", "export", "save"]}, {"name": "Excel File", "id": "file-excel", "unicode": "f1c3", "styles": ["solid", "regular"], "search_terms": ["csv", "document", "numbers", "spreadsheets", "table"]}, {"name": "File Export", "id": "file-export", "unicode": "f56e", "styles": ["solid"], "search_terms": ["download", "save"]}, {"name": "Image File", "id": "file-image", "unicode": "f1c5", "styles": ["solid", "regular"], "search_terms": ["document", "image", "jpg", "photo", "png"]}, {"name": "File Import", "id": "file-import", "unicode": "f56f", "styles": ["solid"], "search_terms": ["copy", "document", "send", "upload"]}, {"name": "File Invoice", "id": "file-invoice", "unicode": "f570", "styles": ["solid"], "search_terms": ["account", "bill", "charge", "document", "payment", "receipt"]}, {"name": "File Invoice with US Dollar", "id": "file-invoice-dollar", "unicode": "f571", "styles": ["solid"], "search_terms": ["$", "account", "bill", "charge", "document", "dollar-sign", "money", "payment", "receipt", "usd"]}, {"name": "Medical File", "id": "file-medical", "unicode": "f477", "styles": ["solid"], "search_terms": ["document", "health", "history", "prescription", "record"]}, {"name": "Alternate Medical File", "id": "file-medical-alt", "unicode": "f478", "styles": ["solid"], "search_terms": ["document", "health", "history", "prescription", "record"]}, {"name": "PDF File", "id": "file-pdf", "unicode": "f1c1", "styles": ["solid", "regular"], "search_terms": ["acrobat", "document", "preview", "save"]}, {"name": "Powerpoint File", "id": "file-powerpoint", "unicode": "f1c4", "styles": ["solid", "regular"], "search_terms": ["display", "document", "keynote", "presentation"]}, {"name": "File Prescription", "id": "file-prescription", "unicode": "f572", "styles": ["solid"], "search_terms": ["document", "drugs", "medical", "medicine", "rx"]}, {"name": "File Signature", "id": "file-signature", "unicode": "f573", "styles": ["solid"], "search_terms": ["<PERSON>", "contract", "document", "name"]}, {"name": "File Upload", "id": "file-upload", "unicode": "f574", "styles": ["solid"], "search_terms": ["document", "import", "page", "save"]}, {"name": "Video File", "id": "file-video", "unicode": "f1c8", "styles": ["solid", "regular"], "search_terms": ["document", "m4v", "movie", "mp4", "play"]}, {"name": "Word File", "id": "file-word", "unicode": "f1c2", "styles": ["solid", "regular"], "search_terms": ["document", "edit", "page", "text", "writing"]}, {"name": "Fill", "id": "fill", "unicode": "f575", "styles": ["solid"], "search_terms": ["bucket", "color", "paint", "paint bucket"]}, {"name": "<PERSON>ll <PERSON>", "id": "fill-drip", "unicode": "f576", "styles": ["solid"], "search_terms": ["bucket", "color", "drop", "paint", "paint bucket", "spill"]}, {"name": "Film", "id": "film", "unicode": "f008", "styles": ["solid"], "search_terms": ["cinema", "movie", "strip", "video"]}, {"name": "Filter", "id": "filter", "unicode": "f0b0", "styles": ["solid"], "search_terms": ["funnel", "options", "separate", "sort"]}, {"name": "Fingerprint", "id": "fingerprint", "unicode": "f577", "styles": ["solid"], "search_terms": ["human", "id", "identification", "lock", "smudge", "touch", "unique", "unlock"]}, {"name": "fire", "id": "fire", "unicode": "f06d", "styles": ["solid"], "search_terms": ["burn", "caliente", "flame", "heat", "hot", "popular"]}, {"name": "Alternate Fire", "id": "fire-alt", "unicode": "f7e4", "styles": ["solid"], "search_terms": ["burn", "caliente", "flame", "heat", "hot", "popular"]}, {"name": "fire-extinguisher", "id": "fire-extinguisher", "unicode": "f134", "styles": ["solid"], "search_terms": ["burn", "caliente", "fire fighter", "flame", "heat", "hot", "rescue"]}, {"name": "Firefox", "id": "firefox", "unicode": "f269", "styles": ["brands"], "search_terms": ["browser"]}, {"name": "First Aid", "id": "first-aid", "unicode": "f479", "styles": ["solid"], "search_terms": ["emergency", "emt", "health", "medical", "rescue"]}, {"name": "First Order", "id": "first-order", "unicode": "f2b0", "styles": ["brands"], "search_terms": []}, {"name": "Alternate First Order", "id": "first-order-alt", "unicode": "f50a", "styles": ["brands"], "search_terms": []}, {"name": "firstdraft", "id": "firstdraft", "unicode": "f3a1", "styles": ["brands"], "search_terms": []}, {"name": "Fish", "id": "fish", "unicode": "f578", "styles": ["solid"], "search_terms": ["fauna", "gold", "seafood", "swimming"]}, {"name": "Raised Fist", "id": "fist-raised", "unicode": "f6de", "styles": ["solid"], "search_terms": ["Dungeons & Dragons", "d&d", "dnd", "fantasy", "hand", "ki", "monk", "resist", "strength", "unarmed combat"]}, {"name": "flag", "id": "flag", "unicode": "f024", "styles": ["solid", "regular"], "search_terms": ["country", "notice", "notification", "notify", "pole", "report", "symbol"]}, {"name": "flag-checkered", "id": "flag-checkered", "unicode": "f11e", "styles": ["solid"], "search_terms": ["notice", "notification", "notify", "pole", "racing", "report", "symbol"]}, {"name": "United States of America Flag", "id": "flag-usa", "unicode": "f74d", "styles": ["solid"], "search_terms": ["betsy ross", "country", "old glory", "stars", "stripes", "symbol"]}, {"name": "Flask", "id": "flask", "unicode": "f0c3", "styles": ["solid"], "search_terms": ["beaker", "experimental", "labs", "science"]}, {"name": "<PERSON>lickr", "id": "flickr", "unicode": "f16e", "styles": ["brands"], "search_terms": []}, {"name": "Flipboard", "id": "flipboard", "unicode": "f44d", "styles": ["brands"], "search_terms": []}, {"name": "Flushed Face", "id": "flushed", "unicode": "f579", "styles": ["solid", "regular"], "search_terms": ["embarrassed", "emoticon", "face"]}, {"name": "Fly", "id": "fly", "unicode": "f417", "styles": ["brands"], "search_terms": []}, {"name": "Folder", "id": "folder", "unicode": "f07b", "styles": ["solid", "regular"], "search_terms": ["archive", "directory", "document", "file"]}, {"name": "Folder Minus", "id": "folder-minus", "unicode": "f65d", "styles": ["solid"], "search_terms": ["archive", "delete", "directory", "document", "file", "negative", "remove"]}, {"name": "Folder Open", "id": "folder-open", "unicode": "f07c", "styles": ["solid", "regular"], "search_terms": ["archive", "directory", "document", "empty", "file", "new"]}, {"name": "Folder Plus", "id": "folder-plus", "unicode": "f65e", "styles": ["solid"], "search_terms": ["add", "archive", "create", "directory", "document", "file", "new", "positive"]}, {"name": "font", "id": "font", "unicode": "f031", "styles": ["solid"], "search_terms": ["alphabet", "glyph", "text", "type", "typeface"]}, {"name": "Font Awesome", "id": "font-awesome", "unicode": "f2b4", "styles": ["brands"], "search_terms": ["meanpath"]}, {"name": "Alternate Font <PERSON>", "id": "font-awesome-alt", "unicode": "f35c", "styles": ["brands"], "search_terms": []}, {"name": "Font Awesome Flag", "id": "font-awesome-flag", "unicode": "f425", "styles": ["brands"], "search_terms": []}, {"name": "Font Awesome Full Logo", "id": "font-awesome-logo-full", "unicode": "f4e6", "styles": ["regular", "solid", "brands"], "search_terms": []}, {"name": "Fonticons", "id": "fonticons", "unicode": "f280", "styles": ["brands"], "search_terms": []}, {"name": "Fonticons Fi", "id": "fonticons-fi", "unicode": "f3a2", "styles": ["brands"], "search_terms": []}, {"name": "Football Ball", "id": "football-ball", "unicode": "f44e", "styles": ["solid"], "search_terms": ["ball", "fall", "nfl", "pigskin", "seasonal"]}, {"name": "Fort Awesome", "id": "fort-awesome", "unicode": "f286", "styles": ["brands"], "search_terms": ["castle"]}, {"name": "Alternate Fort Awesome", "id": "fort-awesome-alt", "unicode": "f3a3", "styles": ["brands"], "search_terms": ["castle"]}, {"name": "Forumbee", "id": "forumbee", "unicode": "f211", "styles": ["brands"], "search_terms": []}, {"name": "forward", "id": "forward", "unicode": "f04e", "styles": ["solid"], "search_terms": ["forward", "next", "skip"]}, {"name": "Foursquare", "id": "foursquare", "unicode": "f180", "styles": ["brands"], "search_terms": []}, {"name": "Free Code Camp", "id": "free-code-camp", "unicode": "f2c5", "styles": ["brands"], "search_terms": []}, {"name": "FreeBSD", "id": "freebsd", "unicode": "f3a4", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON>", "id": "frog", "unicode": "f52e", "styles": ["solid"], "search_terms": ["amphibian", "bullfrog", "fauna", "hop", "kermit", "kiss", "prince", "ribbit", "toad", "wart"]}, {"name": "Frowning Face", "id": "frown", "unicode": "f119", "styles": ["solid", "regular"], "search_terms": ["disapprove", "emoticon", "face", "rating", "sad"]}, {"name": "Frowning Face With Open Mouth", "id": "frown-open", "unicode": "f57a", "styles": ["solid", "regular"], "search_terms": ["disapprove", "emoticon", "face", "rating", "sad"]}, {"name": "Fulcrum", "id": "fulcrum", "unicode": "f50b", "styles": ["brands"], "search_terms": []}, {"name": "Funnel Dollar", "id": "funnel-dollar", "unicode": "f662", "styles": ["solid"], "search_terms": ["filter", "money", "options", "separate", "sort"]}, {"name": "Futbol", "id": "futbol", "unicode": "f1e3", "styles": ["solid", "regular"], "search_terms": ["ball", "football", "mls", "soccer"]}, {"name": "Galactic Republic", "id": "galactic-republic", "unicode": "f50c", "styles": ["brands"], "search_terms": ["politics", "star wars"]}, {"name": "Galactic Senate", "id": "galactic-senate", "unicode": "f50d", "styles": ["brands"], "search_terms": ["star wars"]}, {"name": "Gamepad", "id": "gamepad", "unicode": "f11b", "styles": ["solid"], "search_terms": ["controller", "d-pad", "joystick", "video"]}, {"name": "Gas Pump", "id": "gas-pump", "unicode": "f52f", "styles": ["solid"], "search_terms": ["car", "fuel", "gasoline", "petrol"]}, {"name": "Gavel", "id": "gavel", "unicode": "f0e3", "styles": ["solid"], "search_terms": ["hammer", "judge", "law", "lawyer", "opinion"]}, {"name": "Gem", "id": "gem", "unicode": "f3a5", "styles": ["solid", "regular"], "search_terms": ["diamond", "jewelry", "sapphire", "stone", "treasure"]}, {"name": "Genderless", "id": "genderless", "unicode": "f22d", "styles": ["solid"], "search_terms": ["androgynous", "asexual", "sexless"]}, {"name": "Get Pocket", "id": "get-pocket", "unicode": "f265", "styles": ["brands"], "search_terms": []}, {"name": "GG Currency", "id": "gg", "unicode": "f260", "styles": ["brands"], "search_terms": []}, {"name": "GG Currency Circle", "id": "gg-circle", "unicode": "f261", "styles": ["brands"], "search_terms": []}, {"name": "Ghost", "id": "ghost", "unicode": "f6e2", "styles": ["solid"], "search_terms": ["apparition", "blinky", "clyde", "floating", "halloween", "holiday", "inky", "pinky", "spirit"]}, {"name": "gift", "id": "gift", "unicode": "f06b", "styles": ["solid"], "search_terms": ["christmas", "generosity", "giving", "holiday", "party", "present", "wrapped", "xmas"]}, {"name": "Gifts", "id": "gifts", "unicode": "f79c", "styles": ["solid"], "search_terms": ["christmas", "generosity", "giving", "holiday", "party", "present", "wrapped", "xmas"]}, {"name": "Git", "id": "git", "unicode": "f1d3", "styles": ["brands"], "search_terms": []}, {"name": "Git Square", "id": "git-square", "unicode": "f1d2", "styles": ["brands"], "search_terms": []}, {"name": "GitHub", "id": "github", "unicode": "f09b", "styles": ["brands"], "search_terms": ["octocat"]}, {"name": "Alternate GitHub", "id": "github-alt", "unicode": "f113", "styles": ["brands"], "search_terms": ["octocat"]}, {"name": "GitHub Square", "id": "github-square", "unicode": "f092", "styles": ["brands"], "search_terms": ["octocat"]}, {"name": "GitKraken", "id": "gitkraken", "unicode": "f3a6", "styles": ["brands"], "search_terms": []}, {"name": "GitLab", "id": "gitlab", "unicode": "f296", "styles": ["brands"], "search_terms": ["Axosoft"]}, {"name": "Gitter", "id": "gitter", "unicode": "f426", "styles": ["brands"], "search_terms": []}, {"name": "Glass Cheers", "id": "glass-cheers", "unicode": "f79f", "styles": ["solid"], "search_terms": ["alcohol", "bar", "beverage", "celebration", "champagne", "clink", "drink", "holiday", "new year's eve", "party", "toast"]}, {"name": "<PERSON><PERSON>", "id": "glass-martini", "unicode": "f000", "styles": ["solid"], "search_terms": ["alcohol", "bar", "beverage", "drink", "liquor"]}, {"name": "Alternate <PERSON>", "id": "glass-martini-alt", "unicode": "f57b", "styles": ["solid"], "search_terms": ["alcohol", "bar", "beverage", "drink", "liquor"]}, {"name": "<PERSON> Whiskey", "id": "glass-whiskey", "unicode": "f7a0", "styles": ["solid"], "search_terms": ["alcohol", "bar", "beverage", "bourbon", "drink", "liquor", "neat", "rye", "scotch", "whisky"]}, {"name": "Glasses", "id": "glasses", "unicode": "f530", "styles": ["solid"], "search_terms": ["hipster", "nerd", "reading", "sight", "spectacles", "vision"]}, {"name": "Glide", "id": "glide", "unicode": "f2a5", "styles": ["brands"], "search_terms": []}, {"name": "Glide G", "id": "glide-g", "unicode": "f2a6", "styles": ["brands"], "search_terms": []}, {"name": "Globe", "id": "globe", "unicode": "f0ac", "styles": ["solid"], "search_terms": ["all", "coordinates", "country", "earth", "global", "gps", "language", "localize", "location", "map", "online", "place", "planet", "translate", "travel", "world"]}, {"name": "Globe with Africa shown", "id": "globe-africa", "unicode": "f57c", "styles": ["solid"], "search_terms": ["all", "country", "earth", "global", "gps", "language", "localize", "location", "map", "online", "place", "planet", "translate", "travel", "world"]}, {"name": "Globe with Americas shown", "id": "globe-americas", "unicode": "f57d", "styles": ["solid"], "search_terms": ["all", "country", "earth", "global", "gps", "language", "localize", "location", "map", "online", "place", "planet", "translate", "travel", "world"]}, {"name": "Globe with Asia shown", "id": "globe-asia", "unicode": "f57e", "styles": ["solid"], "search_terms": ["all", "country", "earth", "global", "gps", "language", "localize", "location", "map", "online", "place", "planet", "translate", "travel", "world"]}, {"name": "Globe with Europe shown", "id": "globe-europe", "unicode": "f7a2", "styles": ["solid"], "search_terms": ["all", "country", "earth", "global", "gps", "language", "localize", "location", "map", "online", "place", "planet", "translate", "travel", "world"]}, {"name": "Gofore", "id": "gofore", "unicode": "f3a7", "styles": ["brands"], "search_terms": []}, {"name": "Golf Ball", "id": "golf-ball", "unicode": "f450", "styles": ["solid"], "search_terms": ["caddy", "eagle", "putt", "tee"]}, {"name": "Goodreads", "id": "goodreads", "unicode": "f3a8", "styles": ["brands"], "search_terms": []}, {"name": "Goodreads G", "id": "goodreads-g", "unicode": "f3a9", "styles": ["brands"], "search_terms": []}, {"name": "Google Logo", "id": "google", "unicode": "f1a0", "styles": ["brands"], "search_terms": []}, {"name": "Google Drive", "id": "google-drive", "unicode": "f3aa", "styles": ["brands"], "search_terms": []}, {"name": "Google Play", "id": "google-play", "unicode": "f3ab", "styles": ["brands"], "search_terms": []}, {"name": "Google Plus", "id": "google-plus", "unicode": "f2b3", "styles": ["brands"], "search_terms": ["google-plus-circle", "google-plus-official"]}, {"name": "Google Plus G", "id": "google-plus-g", "unicode": "f0d5", "styles": ["brands"], "search_terms": ["google-plus", "social network"]}, {"name": "Google Plus Square", "id": "google-plus-square", "unicode": "f0d4", "styles": ["brands"], "search_terms": ["social network"]}, {"name": "Google Wallet", "id": "google-wallet", "unicode": "f1ee", "styles": ["brands"], "search_terms": []}, {"name": "Gopuram", "id": "gopuram", "unicode": "f664", "styles": ["solid"], "search_terms": ["building", "entrance", "hinduism", "temple", "tower"]}, {"name": "Graduation Cap", "id": "graduation-cap", "unicode": "f19d", "styles": ["solid"], "search_terms": ["ceremony", "college", "graduate", "learning", "school", "student"]}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Gitt<PERSON>)", "id": "gratipay", "unicode": "f184", "styles": ["brands"], "search_terms": ["favorite", "heart", "like", "love"]}, {"name": "Grav", "id": "grav", "unicode": "f2d6", "styles": ["brands"], "search_terms": []}, {"name": "Greater Than", "id": "greater-than", "unicode": "f531", "styles": ["solid"], "search_terms": ["arithmetic", "compare", "math"]}, {"name": "Greater Than Equal To", "id": "greater-than-equal", "unicode": "f532", "styles": ["solid"], "search_terms": ["arithmetic", "compare", "math"]}, {"name": "Grimacing Face", "id": "grimace", "unicode": "f57f", "styles": ["solid", "regular"], "search_terms": ["cringe", "emoticon", "face", "teeth"]}, {"name": "Grinning Face", "id": "grin", "unicode": "f580", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "laugh", "smile"]}, {"name": "Alternate Grinning Face", "id": "grin-alt", "unicode": "f581", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "laugh", "smile"]}, {"name": "Grinning Face With Smiling Eyes", "id": "grin-beam", "unicode": "f582", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "laugh", "smile"]}, {"name": "Grinning Face With Sweat", "id": "grin-beam-sweat", "unicode": "f583", "styles": ["solid", "regular"], "search_terms": ["embarass", "emoticon", "face", "smile"]}, {"name": "Smiling Face With Heart-Eyes", "id": "grin-hearts", "unicode": "f584", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "love", "smile"]}, {"name": "Grinning Squinting Face", "id": "grin-squint", "unicode": "f585", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "laugh", "smile"]}, {"name": "Rolling on the Floor Laughing", "id": "grin-squint-tears", "unicode": "f586", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "happy", "smile"]}, {"name": "Star-Struck", "id": "grin-stars", "unicode": "f587", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "star-struck"]}, {"name": "Face With Tears of Joy", "id": "grin-tears", "unicode": "f588", "styles": ["solid", "regular"], "search_terms": ["LOL", "emoticon", "face"]}, {"name": "Face With Tongue", "id": "grin-tongue", "unicode": "f589", "styles": ["solid", "regular"], "search_terms": ["LOL", "emoticon", "face"]}, {"name": "Squinting Face With Tongue", "id": "grin-tongue-squint", "unicode": "f58a", "styles": ["solid", "regular"], "search_terms": ["LOL", "emoticon", "face"]}, {"name": "Winking Face With Tongue", "id": "grin-tongue-wink", "unicode": "f58b", "styles": ["solid", "regular"], "search_terms": ["LOL", "emoticon", "face"]}, {"name": "Grinning Winking Face", "id": "grin-wink", "unicode": "f58c", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "flirt", "laugh", "smile"]}, {"name": "Grip Horizontal", "id": "grip-horizontal", "unicode": "f58d", "styles": ["solid"], "search_terms": ["affordance", "drag", "drop", "grab", "handle"]}, {"name": "Grip Lines", "id": "grip-lines", "unicode": "f7a4", "styles": ["solid"], "search_terms": ["affordance", "drag", "drop", "grab", "handle"]}, {"name": "Grip Lines Vertical", "id": "grip-lines-vertical", "unicode": "f7a5", "styles": ["solid"], "search_terms": ["affordance", "drag", "drop", "grab", "handle"]}, {"name": "Grip Vertical", "id": "grip-vertical", "unicode": "f58e", "styles": ["solid"], "search_terms": ["affordance", "drag", "drop", "grab", "handle"]}, {"name": "Gripfire, Inc.", "id": "gripfire", "unicode": "f3ac", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "grunt", "unicode": "f3ad", "styles": ["brands"], "search_terms": []}, {"name": "Guitar", "id": "guitar", "unicode": "f7a6", "styles": ["solid"], "search_terms": ["acoustic", "instrument", "music", "rock", "rock and roll", "song", "strings"]}, {"name": "Gulp", "id": "gulp", "unicode": "f3ae", "styles": ["brands"], "search_terms": []}, {"name": "H Square", "id": "h-square", "unicode": "f0fd", "styles": ["solid"], "search_terms": ["directions", "emergency", "hospital", "hotel", "map"]}, {"name": "Hacker News", "id": "hacker-news", "unicode": "f1d4", "styles": ["brands"], "search_terms": []}, {"name": "Hacker News Square", "id": "hacker-news-square", "unicode": "f3af", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "hackerrank", "unicode": "f5f7", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "hamburger", "unicode": "f805", "styles": ["solid"], "search_terms": ["bacon", "beef", "burger", "burger king", "cheeseburger", "fast food", "grill", "ground beef", "mc<PERSON><PERSON><PERSON>", "sandwich"]}, {"name": "Hammer", "id": "hammer", "unicode": "f6e3", "styles": ["solid"], "search_terms": ["admin", "fix", "repair", "settings", "tool"]}, {"name": "Ham<PERSON>", "id": "hamsa", "unicode": "f665", "styles": ["solid"], "search_terms": ["amulet", "christianity", "islam", "jewish", "judaism", "muslim", "protection"]}, {"name": "Hand Holding", "id": "hand-holding", "unicode": "f4bd", "styles": ["solid"], "search_terms": ["carry", "lift"]}, {"name": "Hand Holding Heart", "id": "hand-holding-heart", "unicode": "f4be", "styles": ["solid"], "search_terms": ["carry", "charity", "gift", "lift", "package"]}, {"name": "Hand Holding US Dollar", "id": "hand-holding-usd", "unicode": "f4c0", "styles": ["solid"], "search_terms": ["$", "carry", "dollar sign", "donation", "giving", "lift", "money", "price"]}, {"name": "Lizard (Hand)", "id": "hand-lizard", "unicode": "f258", "styles": ["solid", "regular"], "search_terms": ["game", "rosh<PERSON><PERSON>"]}, {"name": "Hand with Middle Finger Raised", "id": "hand-middle-finger", "unicode": "f806", "styles": ["solid"], "search_terms": ["flip the bird", "gesture", "hate", "rude"]}, {"name": "Paper (Hand)", "id": "hand-paper", "unicode": "f256", "styles": ["solid", "regular"], "search_terms": ["game", "halt", "rosh<PERSON><PERSON>", "stop"]}, {"name": "Peace (Hand)", "id": "hand-peace", "unicode": "f25b", "styles": ["solid", "regular"], "search_terms": ["rest", "truce"]}, {"name": "Hand Pointing Down", "id": "hand-point-down", "unicode": "f0a7", "styles": ["solid", "regular"], "search_terms": ["finger", "hand-o-down", "point"]}, {"name": "Hand Pointing Left", "id": "hand-point-left", "unicode": "f0a5", "styles": ["solid", "regular"], "search_terms": ["back", "finger", "hand-o-left", "left", "point", "previous"]}, {"name": "Hand Pointing Right", "id": "hand-point-right", "unicode": "f0a4", "styles": ["solid", "regular"], "search_terms": ["finger", "forward", "hand-o-right", "next", "point", "right"]}, {"name": "Hand Pointing Up", "id": "hand-point-up", "unicode": "f0a6", "styles": ["solid", "regular"], "search_terms": ["finger", "hand-o-up", "point"]}, {"name": "<PERSON>er (Hand)", "id": "hand-pointer", "unicode": "f25a", "styles": ["solid", "regular"], "search_terms": ["arrow", "cursor", "select"]}, {"name": "Rock (Hand)", "id": "hand-rock", "unicode": "f255", "styles": ["solid", "regular"], "search_terms": ["fist", "game", "rosh<PERSON><PERSON>"]}, {"name": "<PERSON><PERSON><PERSON> (Hand)", "id": "hand-scissors", "unicode": "f257", "styles": ["solid", "regular"], "search_terms": ["cut", "game", "rosh<PERSON><PERSON>"]}, {"name": "<PERSON><PERSON><PERSON> (Hand)", "id": "hand-spock", "unicode": "f259", "styles": ["solid", "regular"], "search_terms": ["live long", "prosper", "salute", "star trek", "vulcan"]}, {"name": "Hands", "id": "hands", "unicode": "f4c2", "styles": ["solid"], "search_terms": ["carry", "hold", "lift"]}, {"name": "Helping Hands", "id": "hands-helping", "unicode": "f4c4", "styles": ["solid"], "search_terms": ["aid", "assistance", "handshake", "partnership", "volunteering"]}, {"name": "Handshake", "id": "handshake", "unicode": "f2b5", "styles": ["solid", "regular"], "search_terms": ["agreement", "greeting", "meeting", "partnership"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "hanu<PERSON><PERSON>", "unicode": "f6e6", "styles": ["solid"], "search_terms": ["candle", "<PERSON><PERSON><PERSON><PERSON>", "jewish", "judaism", "light"]}, {"name": "Hard Hat", "id": "hard-hat", "unicode": "f807", "styles": ["solid"], "search_terms": ["construction", "hardhat", "helmet", "safety"]}, {"name": "Hashtag", "id": "hashtag", "unicode": "f292", "styles": ["solid"], "search_terms": ["Twitter", "instagram", "pound", "social media", "tag"]}, {"name": "Wizard's Hat", "id": "hat-wizard", "unicode": "f6e8", "styles": ["solid"], "search_terms": ["Dungeons & Dragons", "accessory", "buckle", "clothing", "d&d", "dnd", "fantasy", "halloween", "head", "holiday", "mage", "magic", "pointy", "witch"]}, {"name": "Haykal", "id": "haykal", "unicode": "f666", "styles": ["solid"], "search_terms": ["bahai", "bahá'í", "star"]}, {"name": "HDD", "id": "hdd", "unicode": "f0a0", "styles": ["solid", "regular"], "search_terms": ["cpu", "hard drive", "harddrive", "machine", "save", "storage"]}, {"name": "heading", "id": "heading", "unicode": "f1dc", "styles": ["solid"], "search_terms": ["format", "header", "text", "title"]}, {"name": "headphones", "id": "headphones", "unicode": "f025", "styles": ["solid"], "search_terms": ["audio", "listen", "music", "sound", "speaker"]}, {"name": "Alternate Headphones", "id": "headphones-alt", "unicode": "f58f", "styles": ["solid"], "search_terms": ["audio", "listen", "music", "sound", "speaker"]}, {"name": "Headset", "id": "headset", "unicode": "f590", "styles": ["solid"], "search_terms": ["audio", "gamer", "gaming", "listen", "live chat", "microphone", "shot caller", "sound", "support", "telemarketer"]}, {"name": "Heart", "id": "heart", "unicode": "f004", "styles": ["solid", "regular"], "search_terms": ["favorite", "like", "love", "relationship", "valentine"]}, {"name": "Heart Broken", "id": "heart-broken", "unicode": "f7a9", "styles": ["solid"], "search_terms": ["breakup", "crushed", "dislike", "dumped", "grief", "love", "lovesick", "relationship", "sad"]}, {"name": "Heartbeat", "id": "heartbeat", "unicode": "f21e", "styles": ["solid"], "search_terms": ["ekg", "electrocardiogram", "health", "lifeline", "vital signs"]}, {"name": "Helicopter", "id": "helicopter", "unicode": "f533", "styles": ["solid"], "search_terms": ["airwolf", "apache", "chopper", "flight", "fly", "travel"]}, {"name": "Highlighter", "id": "highlighter", "unicode": "f591", "styles": ["solid"], "search_terms": ["edit", "marker", "sharpie", "update", "write"]}, {"name": "Hiking", "id": "hiking", "unicode": "f6ec", "styles": ["solid"], "search_terms": ["activity", "backpack", "fall", "fitness", "outdoors", "person", "seasonal", "walking"]}, {"name": "<PERSON><PERSON>", "id": "hippo", "unicode": "f6ed", "styles": ["solid"], "search_terms": ["animal", "fauna", "hippopotamus", "hungry", "mammal"]}, {"name": "Hips", "id": "hips", "unicode": "f452", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON>re<PERSON><PERSON><PERSON><PERSON>", "id": "hire-a-helper", "unicode": "f3b0", "styles": ["brands"], "search_terms": []}, {"name": "History", "id": "history", "unicode": "f1da", "styles": ["solid"], "search_terms": ["Rewind", "clock", "reverse", "time", "time machine"]}, {"name": "Hockey Puck", "id": "hockey-puck", "unicode": "f453", "styles": ["solid"], "search_terms": ["ice", "nhl", "sport"]}, {"name": "<PERSON>", "id": "holly-berry", "unicode": "f7aa", "styles": ["solid"], "search_terms": ["catwoman", "christmas", "decoration", "flora", "halle", "holiday", "ororo munroe", "plant", "storm", "xmas"]}, {"name": "home", "id": "home", "unicode": "f015", "styles": ["solid"], "search_terms": ["abode", "building", "house", "main"]}, {"name": "<PERSON><PERSON>", "id": "hooli", "unicode": "f427", "styles": ["brands"], "search_terms": []}, {"name": "Hornbill", "id": "hornbill", "unicode": "f592", "styles": ["brands"], "search_terms": []}, {"name": "Horse", "id": "horse", "unicode": "f6f0", "styles": ["solid"], "search_terms": ["equus", "fauna", "mammmal", "mare", "neigh", "pony"]}, {"name": "Horse Head", "id": "horse-head", "unicode": "f7ab", "styles": ["solid"], "search_terms": ["equus", "fauna", "mammmal", "mare", "neigh", "pony"]}, {"name": "hospital", "id": "hospital", "unicode": "f0f8", "styles": ["solid", "regular"], "search_terms": ["building", "emergency room", "medical center"]}, {"name": "Alternate Hospital", "id": "hospital-alt", "unicode": "f47d", "styles": ["solid"], "search_terms": ["building", "emergency room", "medical center"]}, {"name": "Hospital Symbol", "id": "hospital-symbol", "unicode": "f47e", "styles": ["solid"], "search_terms": ["clinic", "emergency", "map"]}, {"name": "Hot Tub", "id": "hot-tub", "unicode": "f593", "styles": ["solid"], "search_terms": ["bath", "<PERSON><PERSON><PERSON><PERSON>", "massage", "sauna", "spa"]}, {"name": "Hot Dog", "id": "hotdog", "unicode": "f80f", "styles": ["solid"], "search_terms": ["bun", "chili", "frankfurt", "frankfurter", "kosher", "polish", "sandwich", "sausage", "vienna", "weiner"]}, {"name": "Hotel", "id": "hotel", "unicode": "f594", "styles": ["solid"], "search_terms": ["building", "inn", "lodging", "motel", "resort", "travel"]}, {"name": "<PERSON><PERSON>", "id": "hotjar", "unicode": "f3b1", "styles": ["brands"], "search_terms": []}, {"name": "Hourglass", "id": "hourglass", "unicode": "f254", "styles": ["solid", "regular"], "search_terms": ["hour", "minute", "sand", "stopwatch", "time"]}, {"name": "Hourglass End", "id": "hourglass-end", "unicode": "f253", "styles": ["solid"], "search_terms": ["hour", "minute", "sand", "stopwatch", "time"]}, {"name": "Hourglass Half", "id": "hourglass-half", "unicode": "f252", "styles": ["solid"], "search_terms": ["hour", "minute", "sand", "stopwatch", "time"]}, {"name": "Hourglass Start", "id": "hourglass-start", "unicode": "f251", "styles": ["solid"], "search_terms": ["hour", "minute", "sand", "stopwatch", "time"]}, {"name": "Damaged House", "id": "house-damage", "unicode": "f6f1", "styles": ["solid"], "search_terms": ["building", "devastation", "disaster", "home", "insurance"]}, {"name": "Houzz", "id": "<PERSON>uzz", "unicode": "f27c", "styles": ["brands"], "search_terms": []}, {"name": "Hryvnia", "id": "hryvnia", "unicode": "f6f2", "styles": ["solid"], "search_terms": ["currency", "money", "ukraine", "ukrainian"]}, {"name": "HTML 5 Logo", "id": "html5", "unicode": "f13b", "styles": ["brands"], "search_terms": []}, {"name": "HubSpot", "id": "hubspot", "unicode": "f3b2", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON> <PERSON><PERSON>", "id": "i-cursor", "unicode": "f246", "styles": ["solid"], "search_terms": ["editing", "i-beam", "type", "writing"]}, {"name": "Ice Cream", "id": "ice-cream", "unicode": "f810", "styles": ["solid"], "search_terms": ["chocolate", "cone", "dessert", "frozen", "scoop", "sorbet", "vanilla", "yogurt"]}, {"name": "Icicles", "id": "icicles", "unicode": "f7ad", "styles": ["solid"], "search_terms": ["cold", "frozen", "hanging", "ice", "seasonal", "sharp"]}, {"name": "Identification Badge", "id": "id-badge", "unicode": "f2c1", "styles": ["solid", "regular"], "search_terms": ["address", "contact", "identification", "license", "profile"]}, {"name": "Identification Card", "id": "id-card", "unicode": "f2c2", "styles": ["solid", "regular"], "search_terms": ["contact", "demographics", "document", "identification", "issued", "profile"]}, {"name": "Alternate Identification Card", "id": "id-card-alt", "unicode": "f47f", "styles": ["solid"], "search_terms": ["contact", "demographics", "document", "identification", "issued", "profile"]}, {"name": "Igloo", "id": "igloo", "unicode": "f7ae", "styles": ["solid"], "search_terms": ["dome", "dwelling", "eskimo", "home", "house", "ice", "snow"]}, {"name": "Image", "id": "image", "unicode": "f03e", "styles": ["solid", "regular"], "search_terms": ["album", "landscape", "photo", "picture"]}, {"name": "Images", "id": "images", "unicode": "f302", "styles": ["solid", "regular"], "search_terms": ["album", "landscape", "photo", "picture"]}, {"name": "IMDB", "id": "imdb", "unicode": "f2d8", "styles": ["brands"], "search_terms": []}, {"name": "inbox", "id": "inbox", "unicode": "f01c", "styles": ["solid"], "search_terms": ["archive", "desk", "email", "mail", "message"]}, {"name": "Indent", "id": "indent", "unicode": "f03c", "styles": ["solid"], "search_terms": ["align", "justify", "paragraph", "tab"]}, {"name": "Industry", "id": "industry", "unicode": "f275", "styles": ["solid"], "search_terms": ["building", "factory", "industrial", "manufacturing", "mill", "warehouse"]}, {"name": "Infinity", "id": "infinity", "unicode": "f534", "styles": ["solid"], "search_terms": ["eternity", "forever", "math"]}, {"name": "Info", "id": "info", "unicode": "f129", "styles": ["solid"], "search_terms": ["details", "help", "information", "more", "support"]}, {"name": "Info Circle", "id": "info-circle", "unicode": "f05a", "styles": ["solid"], "search_terms": ["details", "help", "information", "more", "support"]}, {"name": "Instagram", "id": "instagram", "unicode": "f16d", "styles": ["brands"], "search_terms": []}, {"name": "Intercom", "id": "intercom", "unicode": "f7af", "styles": ["brands"], "search_terms": ["app", "customer", "messenger"]}, {"name": "Internet-explorer", "id": "internet-explorer", "unicode": "f26b", "styles": ["brands"], "search_terms": ["browser", "ie"]}, {"name": "InVision", "id": "invision", "unicode": "f7b0", "styles": ["brands"], "search_terms": ["app", "design", "interface"]}, {"name": "ioxhost", "id": "ioxhost", "unicode": "f208", "styles": ["brands"], "search_terms": []}, {"name": "italic", "id": "italic", "unicode": "f033", "styles": ["solid"], "search_terms": ["edit", "emphasis", "font", "format", "text", "type"]}, {"name": "itch.io", "id": "itch-io", "unicode": "f83a", "styles": ["brands"], "search_terms": []}, {"name": "iTunes", "id": "itunes", "unicode": "f3b4", "styles": ["brands"], "search_terms": []}, {"name": "Itunes Note", "id": "itunes-note", "unicode": "f3b5", "styles": ["brands"], "search_terms": []}, {"name": "Java", "id": "java", "unicode": "f4e4", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "jedi", "unicode": "f669", "styles": ["solid"], "search_terms": ["crest", "force", "sith", "skywalker", "star wars", "yoda"]}, {"name": "Jedi Order", "id": "jedi-order", "unicode": "f50e", "styles": ["brands"], "search_terms": ["star wars"]}, {"name": "<PERSON><PERSON>", "id": "jenkins", "unicode": "f3b6", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "jira", "unicode": "f7b1", "styles": ["brands"], "search_terms": ["atlassian"]}, {"name": "<PERSON><PERSON>", "id": "joget", "unicode": "f3b7", "styles": ["brands"], "search_terms": []}, {"name": "Joint", "id": "joint", "unicode": "f595", "styles": ["solid"], "search_terms": ["blunt", "cannabis", "doobie", "drugs", "marijuana", "roach", "smoke", "smoking", "spliff"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "j<PERSON><PERSON>", "unicode": "f1aa", "styles": ["brands"], "search_terms": []}, {"name": "Journal of the Whills", "id": "journal-whills", "unicode": "f66a", "styles": ["solid"], "search_terms": ["book", "force", "jedi", "sith", "star wars", "yoda"]}, {"name": "JavaScript (JS)", "id": "js", "unicode": "f3b8", "styles": ["brands"], "search_terms": []}, {"name": "JavaScript (JS) Square", "id": "js-square", "unicode": "f3b9", "styles": ["brands"], "search_terms": []}, {"name": "jsFiddle", "id": "jsfiddle", "unicode": "f1cc", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "kaaba", "unicode": "f66b", "styles": ["solid"], "search_terms": ["building", "cube", "islam", "muslim"]}, {"name": "<PERSON><PERSON>", "id": "kaggle", "unicode": "f5fa", "styles": ["brands"], "search_terms": []}, {"name": "key", "id": "key", "unicode": "f084", "styles": ["solid"], "search_terms": ["lock", "password", "private", "secret", "unlock"]}, {"name": "Keybase", "id": "keybase", "unicode": "f4f5", "styles": ["brands"], "search_terms": []}, {"name": "Keyboard", "id": "keyboard", "unicode": "f11c", "styles": ["solid", "regular"], "search_terms": ["accessory", "edit", "input", "text", "type", "write"]}, {"name": "KeyCDN", "id": "keycdn", "unicode": "f3ba", "styles": ["brands"], "search_terms": []}, {"name": "Khanda", "id": "khanda", "unicode": "f66d", "styles": ["solid"], "search_terms": ["chakkar", "sikh", "sikhism", "sword"]}, {"name": "Kickstarter", "id": "kickstarter", "unicode": "f3bb", "styles": ["brands"], "search_terms": []}, {"name": "Kickstarter K", "id": "kickstarter-k", "unicode": "f3bc", "styles": ["brands"], "search_terms": []}, {"name": "Kissing Face", "id": "kiss", "unicode": "f596", "styles": ["solid", "regular"], "search_terms": ["beso", "emoticon", "face", "love", "smooch"]}, {"name": "Kissing Face With Smiling Eyes", "id": "kiss-beam", "unicode": "f597", "styles": ["solid", "regular"], "search_terms": ["beso", "emoticon", "face", "love", "smooch"]}, {"name": "Face Blowing a Kiss", "id": "kiss-wink-heart", "unicode": "f598", "styles": ["solid", "regular"], "search_terms": ["beso", "emoticon", "face", "love", "smooch"]}, {"name": "<PERSON><PERSON>", "id": "kiwi-bird", "unicode": "f535", "styles": ["solid"], "search_terms": ["bird", "fauna", "new zealand"]}, {"name": "KORVUE", "id": "korvue", "unicode": "f42f", "styles": ["brands"], "search_terms": []}, {"name": "Landmark", "id": "landmark", "unicode": "f66f", "styles": ["solid"], "search_terms": ["building", "historic", "memorable", "monument", "politics"]}, {"name": "Language", "id": "language", "unicode": "f1ab", "styles": ["solid"], "search_terms": ["dialect", "idiom", "localize", "speech", "translate", "vernacular"]}, {"name": "Laptop", "id": "laptop", "unicode": "f109", "styles": ["solid"], "search_terms": ["computer", "cpu", "dell", "demo", "device", "mac", "macbook", "machine", "pc"]}, {"name": "Laptop Code", "id": "laptop-code", "unicode": "f5fc", "styles": ["solid"], "search_terms": ["computer", "cpu", "dell", "demo", "develop", "device", "mac", "macbook", "machine", "pc"]}, {"name": "Laptop Medical", "id": "laptop-medical", "unicode": "f812", "styles": ["solid"], "search_terms": ["computer", "device", "ehr", "electronic health records", "history"]}, {"name": "<PERSON><PERSON>", "id": "laravel", "unicode": "f3bd", "styles": ["brands"], "search_terms": []}, {"name": "last.fm", "id": "lastfm", "unicode": "f202", "styles": ["brands"], "search_terms": []}, {"name": "last.fm Square", "id": "lastfm-square", "unicode": "f203", "styles": ["brands"], "search_terms": []}, {"name": "Grinning Face With Big Eyes", "id": "laugh", "unicode": "f599", "styles": ["solid", "regular"], "search_terms": ["LOL", "emoticon", "face", "laugh", "smile"]}, {"name": "Laugh Face with Beaming Eyes", "id": "laugh-beam", "unicode": "f59a", "styles": ["solid", "regular"], "search_terms": ["LOL", "emoticon", "face", "happy", "smile"]}, {"name": "Laughing Squinting Face", "id": "laugh-squint", "unicode": "f59b", "styles": ["solid", "regular"], "search_terms": ["LOL", "emoticon", "face", "happy", "smile"]}, {"name": "Laughing Winking Face", "id": "laugh-wink", "unicode": "f59c", "styles": ["solid", "regular"], "search_terms": ["LOL", "emoticon", "face", "happy", "smile"]}, {"name": "Layer Group", "id": "layer-group", "unicode": "f5fd", "styles": ["solid"], "search_terms": ["arrange", "develop", "layers", "map", "stack"]}, {"name": "leaf", "id": "leaf", "unicode": "f06c", "styles": ["solid"], "search_terms": ["eco", "flora", "nature", "plant", "vegan"]}, {"name": "Leanpub", "id": "leanpub", "unicode": "f212", "styles": ["brands"], "search_terms": []}, {"name": "Lemon", "id": "lemon", "unicode": "f094", "styles": ["solid", "regular"], "search_terms": ["citrus", "lemonade", "lime", "tart"]}, {"name": "Less", "id": "less", "unicode": "f41d", "styles": ["brands"], "search_terms": []}, {"name": "Less Than", "id": "less-than", "unicode": "f536", "styles": ["solid"], "search_terms": ["arithmetic", "compare", "math"]}, {"name": "Less Than Equal To", "id": "less-than-equal", "unicode": "f537", "styles": ["solid"], "search_terms": ["arithmetic", "compare", "math"]}, {"name": "Alternate Level Down", "id": "level-down-alt", "unicode": "f3be", "styles": ["solid"], "search_terms": ["arrow", "level-down"]}, {"name": "Alternate Level Up", "id": "level-up-alt", "unicode": "f3bf", "styles": ["solid"], "search_terms": ["arrow", "level-up"]}, {"name": "Life Ring", "id": "life-ring", "unicode": "f1cd", "styles": ["solid", "regular"], "search_terms": ["coast guard", "help", "overboard", "save", "support"]}, {"name": "Lightbulb", "id": "lightbulb", "unicode": "f0eb", "styles": ["solid", "regular"], "search_terms": ["energy", "idea", "inspiration", "light"]}, {"name": "Line", "id": "line", "unicode": "f3c0", "styles": ["brands"], "search_terms": []}, {"name": "Link", "id": "link", "unicode": "f0c1", "styles": ["solid"], "search_terms": ["attach", "attachment", "chain", "connect"]}, {"name": "LinkedIn", "id": "linkedin", "unicode": "f08c", "styles": ["brands"], "search_terms": ["linkedin-square"]}, {"name": "LinkedIn In", "id": "linkedin-in", "unicode": "f0e1", "styles": ["brands"], "search_terms": ["linkedin"]}, {"name": "<PERSON><PERSON>", "id": "linode", "unicode": "f2b8", "styles": ["brands"], "search_terms": []}, {"name": "Linux", "id": "linux", "unicode": "f17c", "styles": ["brands"], "search_terms": ["tux"]}, {"name": "Turkish Lira Sign", "id": "lira-sign", "unicode": "f195", "styles": ["solid"], "search_terms": ["currency", "money", "try", "turkish"]}, {"name": "List", "id": "list", "unicode": "f03a", "styles": ["solid"], "search_terms": ["checklist", "completed", "done", "finished", "ol", "todo", "ul"]}, {"name": "Alternate List", "id": "list-alt", "unicode": "f022", "styles": ["solid", "regular"], "search_terms": ["checklist", "completed", "done", "finished", "ol", "todo", "ul"]}, {"name": "list-ol", "id": "list-ol", "unicode": "f0cb", "styles": ["solid"], "search_terms": ["checklist", "completed", "done", "finished", "numbers", "ol", "todo", "ul"]}, {"name": "list-ul", "id": "list-ul", "unicode": "f0ca", "styles": ["solid"], "search_terms": ["checklist", "completed", "done", "finished", "ol", "todo", "ul"]}, {"name": "location-arrow", "id": "location-arrow", "unicode": "f124", "styles": ["solid"], "search_terms": ["address", "compass", "coordinate", "direction", "gps", "map", "navigation", "place"]}, {"name": "lock", "id": "lock", "unicode": "f023", "styles": ["solid"], "search_terms": ["admin", "lock", "open", "password", "private", "protect", "security"]}, {"name": "Lock Open", "id": "lock-open", "unicode": "f3c1", "styles": ["solid"], "search_terms": ["admin", "lock", "open", "password", "private", "protect", "security"]}, {"name": "Alternate <PERSON> Down", "id": "long-arrow-alt-down", "unicode": "f309", "styles": ["solid"], "search_terms": ["download", "long-arrow-down"]}, {"name": "Alternate <PERSON> Left", "id": "long-arrow-alt-left", "unicode": "f30a", "styles": ["solid"], "search_terms": ["back", "long-arrow-left", "previous"]}, {"name": "Alternate <PERSON> Arrow Right", "id": "long-arrow-alt-right", "unicode": "f30b", "styles": ["solid"], "search_terms": ["forward", "long-arrow-right", "next"]}, {"name": "Alternate Long Arrow Up", "id": "long-arrow-alt-up", "unicode": "f30c", "styles": ["solid"], "search_terms": ["long-arrow-up", "upload"]}, {"name": "Low Vision", "id": "low-vision", "unicode": "f2a8", "styles": ["solid"], "search_terms": ["blind", "eye", "sight"]}, {"name": "Luggage Cart", "id": "luggage-cart", "unicode": "f59d", "styles": ["solid"], "search_terms": ["bag", "baggage", "suitcase", "travel"]}, {"name": "lyft", "id": "lyft", "unicode": "f3c3", "styles": ["brands"], "search_terms": []}, {"name": "Magento", "id": "magento", "unicode": "f3c4", "styles": ["brands"], "search_terms": []}, {"name": "magic", "id": "magic", "unicode": "f0d0", "styles": ["solid"], "search_terms": ["autocomplete", "automatic", "mage", "magic", "spell", "wand", "witch", "wizard"]}, {"name": "magnet", "id": "magnet", "unicode": "f076", "styles": ["solid"], "search_terms": ["Attract", "lodestone", "tool"]}, {"name": "Mail Bulk", "id": "mail-bulk", "unicode": "f674", "styles": ["solid"], "search_terms": ["archive", "envelope", "letter", "post office", "postal", "postcard", "send", "stamp", "usps"]}, {"name": "Mailchimp", "id": "mailchimp", "unicode": "f59e", "styles": ["brands"], "search_terms": []}, {"name": "Male", "id": "male", "unicode": "f183", "styles": ["solid"], "search_terms": ["human", "man", "person", "profile", "user"]}, {"name": "Mandalorian", "id": "mandalorian", "unicode": "f50f", "styles": ["brands"], "search_terms": []}, {"name": "Map", "id": "map", "unicode": "f279", "styles": ["solid", "regular"], "search_terms": ["address", "coordinates", "destination", "gps", "localize", "location", "map", "navigation", "paper", "pin", "place", "point of interest", "position", "route", "travel"]}, {"name": "Map Marked", "id": "map-marked", "unicode": "f59f", "styles": ["solid"], "search_terms": ["address", "coordinates", "destination", "gps", "localize", "location", "map", "navigation", "paper", "pin", "place", "point of interest", "position", "route", "travel"]}, {"name": "Alternate Map Marked", "id": "map-marked-alt", "unicode": "f5a0", "styles": ["solid"], "search_terms": ["address", "coordinates", "destination", "gps", "localize", "location", "map", "navigation", "paper", "pin", "place", "point of interest", "position", "route", "travel"]}, {"name": "map-marker", "id": "map-marker", "unicode": "f041", "styles": ["solid"], "search_terms": ["address", "coordinates", "destination", "gps", "localize", "location", "map", "navigation", "paper", "pin", "place", "point of interest", "position", "route", "travel"]}, {"name": "Alternate Map Marker", "id": "map-marker-alt", "unicode": "f3c5", "styles": ["solid"], "search_terms": ["address", "coordinates", "destination", "gps", "localize", "location", "map", "navigation", "paper", "pin", "place", "point of interest", "position", "route", "travel"]}, {"name": "Map Pin", "id": "map-pin", "unicode": "f276", "styles": ["solid"], "search_terms": ["address", "agree", "coordinates", "destination", "gps", "localize", "location", "map", "marker", "navigation", "pin", "place", "position", "travel"]}, {"name": "Map Signs", "id": "map-signs", "unicode": "f277", "styles": ["solid"], "search_terms": ["directions", "directory", "map", "signage", "wayfinding"]}, {"name": "<PERSON><PERSON>", "id": "markdown", "unicode": "f60f", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "marker", "unicode": "f5a1", "styles": ["solid"], "search_terms": ["design", "edit", "sharpie", "update", "write"]}, {"name": "Mars", "id": "mars", "unicode": "f222", "styles": ["solid"], "search_terms": ["male"]}, {"name": "Mars Double", "id": "mars-double", "unicode": "f227", "styles": ["solid"], "search_terms": []}, {"name": "Mars Stroke", "id": "mars-stroke", "unicode": "f229", "styles": ["solid"], "search_terms": []}, {"name": "Mars Stroke Horizontal", "id": "mars-stroke-h", "unicode": "f22b", "styles": ["solid"], "search_terms": []}, {"name": "Mars Stroke Vertical", "id": "mars-stroke-v", "unicode": "f22a", "styles": ["solid"], "search_terms": []}, {"name": "Mask", "id": "mask", "unicode": "f6fa", "styles": ["solid"], "search_terms": ["carnivale", "costume", "disguise", "halloween", "secret", "super hero"]}, {"name": "Mastodon", "id": "mastodon", "unicode": "f4f6", "styles": ["brands"], "search_terms": []}, {"name": "MaxCDN", "id": "maxcdn", "unicode": "f136", "styles": ["brands"], "search_terms": []}, {"name": "Medal", "id": "medal", "unicode": "f5a2", "styles": ["solid"], "search_terms": ["award", "ribbon", "star", "trophy"]}, {"name": "MedApps", "id": "medapps", "unicode": "f3c6", "styles": ["brands"], "search_terms": []}, {"name": "Medium", "id": "medium", "unicode": "f23a", "styles": ["brands"], "search_terms": []}, {"name": "Medium M", "id": "medium-m", "unicode": "f3c7", "styles": ["brands"], "search_terms": []}, {"name": "medkit", "id": "medkit", "unicode": "f0fa", "styles": ["solid"], "search_terms": ["first aid", "firstaid", "health", "help", "support"]}, {"name": "MRT", "id": "medrt", "unicode": "f3c8", "styles": ["brands"], "search_terms": []}, {"name": "Meetup", "id": "meetup", "unicode": "f2e0", "styles": ["brands"], "search_terms": []}, {"name": "Megaport", "id": "megaport", "unicode": "f5a3", "styles": ["brands"], "search_terms": []}, {"name": "Neutral Face", "id": "meh", "unicode": "f11a", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "neutral", "rating"]}, {"name": "Face Without Mouth", "id": "meh-blank", "unicode": "f5a4", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "neutral", "rating"]}, {"name": "Face With Rolling Eyes", "id": "meh-rolling-eyes", "unicode": "f5a5", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "neutral", "rating"]}, {"name": "Memory", "id": "memory", "unicode": "f538", "styles": ["solid"], "search_terms": ["DIMM", "RAM", "hardware", "storage", "technology"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "mendeley", "unicode": "f7b3", "styles": ["brands"], "search_terms": []}, {"name": "Menorah", "id": "menorah", "unicode": "f676", "styles": ["solid"], "search_terms": ["candle", "<PERSON><PERSON><PERSON><PERSON>", "jewish", "judaism", "light"]}, {"name": "Mercury", "id": "mercury", "unicode": "f223", "styles": ["solid"], "search_terms": ["transgender"]}, {"name": "Meteor", "id": "meteor", "unicode": "f753", "styles": ["solid"], "search_terms": ["armageddon", "asteroid", "comet", "shooting star", "space"]}, {"name": "Microchip", "id": "microchip", "unicode": "f2db", "styles": ["solid"], "search_terms": ["cpu", "hardware", "processor", "technology"]}, {"name": "microphone", "id": "microphone", "unicode": "f130", "styles": ["solid"], "search_terms": ["audio", "podcast", "record", "sing", "sound", "voice"]}, {"name": "Alternate Microphone", "id": "microphone-alt", "unicode": "f3c9", "styles": ["solid"], "search_terms": ["audio", "podcast", "record", "sing", "sound", "voice"]}, {"name": "Alternate Microphone Slash", "id": "microphone-alt-slash", "unicode": "f539", "styles": ["solid"], "search_terms": ["audio", "disable", "mute", "podcast", "record", "sing", "sound", "voice"]}, {"name": "Microphone Slash", "id": "microphone-slash", "unicode": "f131", "styles": ["solid"], "search_terms": ["audio", "disable", "mute", "podcast", "record", "sing", "sound", "voice"]}, {"name": "Microscope", "id": "microscope", "unicode": "f610", "styles": ["solid"], "search_terms": ["electron", "lens", "optics", "science", "shrink"]}, {"name": "Microsoft", "id": "microsoft", "unicode": "f3ca", "styles": ["brands"], "search_terms": []}, {"name": "minus", "id": "minus", "unicode": "f068", "styles": ["solid"], "search_terms": ["collapse", "delete", "hide", "minify", "negative", "remove", "trash"]}, {"name": "Minus Circle", "id": "minus-circle", "unicode": "f056", "styles": ["solid"], "search_terms": ["delete", "hide", "negative", "remove", "shape", "trash"]}, {"name": "Minus Square", "id": "minus-square", "unicode": "f146", "styles": ["solid", "regular"], "search_terms": ["collapse", "delete", "hide", "minify", "negative", "remove", "shape", "trash"]}, {"name": "<PERSON><PERSON>", "id": "mitten", "unicode": "f7b5", "styles": ["solid"], "search_terms": ["clothing", "cold", "glove", "hands", "knitted", "seasonal", "warmth"]}, {"name": "Mix", "id": "mix", "unicode": "f3cb", "styles": ["brands"], "search_terms": []}, {"name": "Mixcloud", "id": "mixcloud", "unicode": "f289", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON>ni", "unicode": "f3cc", "styles": ["brands"], "search_terms": []}, {"name": "Mobile Phone", "id": "mobile", "unicode": "f10b", "styles": ["solid"], "search_terms": ["apple", "call", "cell phone", "cellphone", "device", "iphone", "number", "screen", "telephone"]}, {"name": "Alternate Mobile", "id": "mobile-alt", "unicode": "f3cd", "styles": ["solid"], "search_terms": ["apple", "call", "cell phone", "cellphone", "device", "iphone", "number", "screen", "telephone"]}, {"name": "MODX", "id": "modx", "unicode": "f285", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "monero", "unicode": "f3d0", "styles": ["brands"], "search_terms": []}, {"name": "Money Bill", "id": "money-bill", "unicode": "f0d6", "styles": ["solid"], "search_terms": ["buy", "cash", "checkout", "money", "payment", "price", "purchase"]}, {"name": "Alternate Money Bill", "id": "money-bill-alt", "unicode": "f3d1", "styles": ["solid", "regular"], "search_terms": ["buy", "cash", "checkout", "money", "payment", "price", "purchase"]}, {"name": "Wavy Money Bill", "id": "money-bill-wave", "unicode": "f53a", "styles": ["solid"], "search_terms": ["buy", "cash", "checkout", "money", "payment", "price", "purchase"]}, {"name": "Alternate W<PERSON><PERSON>", "id": "money-bill-wave-alt", "unicode": "f53b", "styles": ["solid"], "search_terms": ["buy", "cash", "checkout", "money", "payment", "price", "purchase"]}, {"name": "Money Check", "id": "money-check", "unicode": "f53c", "styles": ["solid"], "search_terms": ["bank check", "buy", "checkout", "cheque", "money", "payment", "price", "purchase"]}, {"name": "Alternate Money Check", "id": "money-check-alt", "unicode": "f53d", "styles": ["solid"], "search_terms": ["bank check", "buy", "checkout", "cheque", "money", "payment", "price", "purchase"]}, {"name": "Monument", "id": "monument", "unicode": "f5a6", "styles": ["solid"], "search_terms": ["building", "historic", "landmark", "memorable"]}, {"name": "Moon", "id": "moon", "unicode": "f186", "styles": ["solid", "regular"], "search_terms": ["contrast", "crescent", "dark", "lunar", "night"]}, {"name": "Mortar Pestle", "id": "mortar-pestle", "unicode": "f5a7", "styles": ["solid"], "search_terms": ["crush", "culinary", "grind", "medical", "mix", "pharmacy", "prescription", "spices"]}, {"name": "Mosque", "id": "mosque", "unicode": "f678", "styles": ["solid"], "search_terms": ["building", "islam", "landmark", "muslim"]}, {"name": "Motorcycle", "id": "motorcycle", "unicode": "f21c", "styles": ["solid"], "search_terms": ["bike", "machine", "transportation", "vehicle"]}, {"name": "Mountain", "id": "mountain", "unicode": "f6fc", "styles": ["solid"], "search_terms": ["glacier", "hiking", "hill", "landscape", "travel", "view"]}, {"name": "<PERSON>", "id": "mouse-pointer", "unicode": "f245", "styles": ["solid"], "search_terms": ["arrow", "cursor", "select"]}, {"name": "<PERSON>g <PERSON>", "id": "mug-hot", "unicode": "f7b6", "styles": ["solid"], "search_terms": ["caliente", "cocoa", "coffee", "cup", "drink", "holiday", "hot chocolate", "steam", "tea", "warmth"]}, {"name": "Music", "id": "music", "unicode": "f001", "styles": ["solid"], "search_terms": ["lyrics", "melody", "note", "sing", "sound"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "napster", "unicode": "f3d2", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "neos", "unicode": "f612", "styles": ["brands"], "search_terms": []}, {"name": "Wired Network", "id": "network-wired", "unicode": "f6ff", "styles": ["solid"], "search_terms": ["computer", "connect", "ethernet", "internet", "intranet"]}, {"name": "Neuter", "id": "neuter", "unicode": "f22c", "styles": ["solid"], "search_terms": []}, {"name": "Newspaper", "id": "newspaper", "unicode": "f1ea", "styles": ["solid", "regular"], "search_terms": ["article", "editorial", "headline", "journal", "journalism", "news", "press"]}, {"name": "Nimblr", "id": "nimblr", "unicode": "f5a8", "styles": ["brands"], "search_terms": []}, {"name": "Nintendo Switch", "id": "nintendo-switch", "unicode": "f418", "styles": ["brands"], "search_terms": []}, {"name": "Node.js", "id": "node", "unicode": "f419", "styles": ["brands"], "search_terms": []}, {"name": "Node.js JS", "id": "node-js", "unicode": "f3d3", "styles": ["brands"], "search_terms": []}, {"name": "Not Equal", "id": "not-equal", "unicode": "f53e", "styles": ["solid"], "search_terms": ["arithmetic", "compare", "math"]}, {"name": "Medical Notes", "id": "notes-medical", "unicode": "f481", "styles": ["solid"], "search_terms": ["clipboard", "doctor", "ehr", "health", "history", "records"]}, {"name": "npm", "id": "npm", "unicode": "f3d4", "styles": ["brands"], "search_terms": []}, {"name": "NS8", "id": "ns8", "unicode": "f3d5", "styles": ["brands"], "search_terms": []}, {"name": "Nutritionix", "id": "nutritionix", "unicode": "f3d6", "styles": ["brands"], "search_terms": []}, {"name": "Object Group", "id": "object-group", "unicode": "f247", "styles": ["solid", "regular"], "search_terms": ["combine", "copy", "design", "merge", "select"]}, {"name": "Object Ungroup", "id": "object-ungroup", "unicode": "f248", "styles": ["solid", "regular"], "search_terms": ["copy", "design", "merge", "select", "separate"]}, {"name": "Odnoklassniki", "id": "odnoklassniki", "unicode": "f263", "styles": ["brands"], "search_terms": []}, {"name": "Odnoklassniki Square", "id": "odnoklassniki-square", "unicode": "f264", "styles": ["brands"], "search_terms": []}, {"name": "Oil Can", "id": "oil-can", "unicode": "f613", "styles": ["solid"], "search_terms": ["auto", "crude", "gasoline", "grease", "lubricate", "petroleum"]}, {"name": "Old Republic", "id": "old-republic", "unicode": "f510", "styles": ["brands"], "search_terms": ["politics", "star wars"]}, {"name": "Om", "id": "om", "unicode": "f679", "styles": ["solid"], "search_terms": ["buddhism", "hinduism", "jainism", "mantra"]}, {"name": "OpenCart", "id": "opencart", "unicode": "f23d", "styles": ["brands"], "search_terms": []}, {"name": "OpenID", "id": "openid", "unicode": "f19b", "styles": ["brands"], "search_terms": []}, {"name": "Opera", "id": "opera", "unicode": "f26a", "styles": ["brands"], "search_terms": []}, {"name": "Optin Monster", "id": "optin-monster", "unicode": "f23c", "styles": ["brands"], "search_terms": []}, {"name": "Open Source Initiative", "id": "osi", "unicode": "f41a", "styles": ["brands"], "search_terms": []}, {"name": "Otter", "id": "otter", "unicode": "f700", "styles": ["solid"], "search_terms": ["animal", "badger", "fauna", "fur", "mammal", "marten"]}, {"name": "Outdent", "id": "outdent", "unicode": "f03b", "styles": ["solid"], "search_terms": ["align", "justify", "paragraph", "tab"]}, {"name": "page4 Corporation", "id": "page4", "unicode": "f3d7", "styles": ["brands"], "search_terms": []}, {"name": "Pagelines", "id": "pagelines", "unicode": "f18c", "styles": ["brands"], "search_terms": ["eco", "flora", "leaf", "leaves", "nature", "plant", "tree"]}, {"name": "Pager", "id": "pager", "unicode": "f815", "styles": ["solid"], "search_terms": ["beeper", "cellphone", "communication"]}, {"name": "Paint Brush", "id": "paint-brush", "unicode": "f1fc", "styles": ["solid"], "search_terms": ["acrylic", "art", "brush", "color", "fill", "paint", "pigment", "watercolor"]}, {"name": "Paint Roller", "id": "paint-roller", "unicode": "f5aa", "styles": ["solid"], "search_terms": ["acrylic", "art", "brush", "color", "fill", "paint", "pigment", "watercolor"]}, {"name": "Palette", "id": "palette", "unicode": "f53f", "styles": ["solid"], "search_terms": ["acrylic", "art", "brush", "color", "fill", "paint", "pigment", "watercolor"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "palfed", "unicode": "f3d8", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "pallet", "unicode": "f482", "styles": ["solid"], "search_terms": ["archive", "box", "inventory", "shipping", "warehouse"]}, {"name": "Paper Plane", "id": "paper-plane", "unicode": "f1d8", "styles": ["solid", "regular"], "search_terms": ["air", "float", "fold", "mail", "paper", "send"]}, {"name": "Paperclip", "id": "paperclip", "unicode": "f0c6", "styles": ["solid"], "search_terms": ["attach", "attachment", "connect", "link"]}, {"name": "Parachute Box", "id": "parachute-box", "unicode": "f4cd", "styles": ["solid"], "search_terms": ["aid", "assistance", "rescue", "supplies"]}, {"name": "paragraph", "id": "paragraph", "unicode": "f1dd", "styles": ["solid"], "search_terms": ["edit", "format", "text", "writing"]}, {"name": "Parking", "id": "parking", "unicode": "f540", "styles": ["solid"], "search_terms": ["auto", "car", "garage", "meter"]}, {"name": "Passport", "id": "passport", "unicode": "f5ab", "styles": ["solid"], "search_terms": ["document", "id", "identification", "issued", "travel"]}, {"name": "Pastafarianism", "id": "pastafarianism", "unicode": "f67b", "styles": ["solid"], "search_terms": ["agnosticism", "atheism", "flying spaghetti monster", "fsm"]}, {"name": "Paste", "id": "paste", "unicode": "f0ea", "styles": ["solid"], "search_terms": ["clipboard", "copy", "document", "paper"]}, {"name": "Patreon", "id": "patreon", "unicode": "f3d9", "styles": ["brands"], "search_terms": []}, {"name": "pause", "id": "pause", "unicode": "f04c", "styles": ["solid"], "search_terms": ["hold", "wait"]}, {"name": "Pause Circle", "id": "pause-circle", "unicode": "f28b", "styles": ["solid", "regular"], "search_terms": ["hold", "wait"]}, {"name": "<PERSON>w", "id": "paw", "unicode": "f1b0", "styles": ["solid"], "search_terms": ["animal", "cat", "dog", "pet", "print"]}, {"name": "<PERSON><PERSON>", "id": "paypal", "unicode": "f1ed", "styles": ["brands"], "search_terms": []}, {"name": "Peace", "id": "peace", "unicode": "f67c", "styles": ["solid"], "search_terms": ["serenity", "tranquility", "truce", "war"]}, {"name": "Pen", "id": "pen", "unicode": "f304", "styles": ["solid"], "search_terms": ["design", "edit", "update", "write"]}, {"name": "Alternate Pen", "id": "pen-alt", "unicode": "f305", "styles": ["solid"], "search_terms": ["design", "edit", "update", "write"]}, {"name": "<PERSON> Fancy", "id": "pen-fancy", "unicode": "f5ac", "styles": ["solid"], "search_terms": ["design", "edit", "fountain pen", "update", "write"]}, {"name": "<PERSON>", "id": "pen-nib", "unicode": "f5ad", "styles": ["solid"], "search_terms": ["design", "edit", "fountain pen", "update", "write"]}, {"name": "Pen Square", "id": "pen-square", "unicode": "f14b", "styles": ["solid"], "search_terms": ["edit", "pencil-square", "update", "write"]}, {"name": "Alternate Pencil", "id": "pencil-alt", "unicode": "f303", "styles": ["solid"], "search_terms": ["design", "edit", "pencil", "update", "write"]}, {"name": "Pencil Ruler", "id": "pencil-ruler", "unicode": "f5ae", "styles": ["solid"], "search_terms": ["design", "draft", "draw", "pencil"]}, {"name": "Penny Arcade", "id": "penny-arcade", "unicode": "f704", "styles": ["brands"], "search_terms": ["Dungeons & Dragons", "d&d", "dnd", "fantasy", "game", "gaming", "pax", "tabletop"]}, {"name": "People Carry", "id": "people-carry", "unicode": "f4ce", "styles": ["solid"], "search_terms": ["box", "carry", "fragile", "help", "movers", "package"]}, {"name": "Hot Pepper", "id": "pepper-hot", "unicode": "f816", "styles": ["solid"], "search_terms": ["buffalo wings", "capsicum", "chili", "chilli", "habanero", "jalapeno", "mexican", "spicy", "tabasco", "vegetable"]}, {"name": "Percent", "id": "percent", "unicode": "f295", "styles": ["solid"], "search_terms": ["discount", "fraction", "proportion", "rate", "ratio"]}, {"name": "Percentage", "id": "percentage", "unicode": "f541", "styles": ["solid"], "search_terms": ["discount", "fraction", "proportion", "rate", "ratio"]}, {"name": "Periscope", "id": "periscope", "unicode": "f3da", "styles": ["brands"], "search_terms": []}, {"name": "Person Entering Booth", "id": "person-booth", "unicode": "f756", "styles": ["solid"], "search_terms": ["changing", "changing room", "election", "human", "person", "vote", "voting"]}, {"name": "Phabricator", "id": "phabricator", "unicode": "f3db", "styles": ["brands"], "search_terms": []}, {"name": "Phoenix Framework", "id": "phoenix-framework", "unicode": "f3dc", "styles": ["brands"], "search_terms": []}, {"name": "Phoenix Squadron", "id": "phoenix-squadron", "unicode": "f511", "styles": ["brands"], "search_terms": []}, {"name": "Phone", "id": "phone", "unicode": "f095", "styles": ["solid"], "search_terms": ["call", "earphone", "number", "support", "telephone", "voice"]}, {"name": "Phone Slash", "id": "phone-slash", "unicode": "f3dd", "styles": ["solid"], "search_terms": ["call", "cancel", "earphone", "mute", "number", "support", "telephone", "voice"]}, {"name": "Phone Square", "id": "phone-square", "unicode": "f098", "styles": ["solid"], "search_terms": ["call", "earphone", "number", "support", "telephone", "voice"]}, {"name": "Phone Volume", "id": "phone-volume", "unicode": "f2a0", "styles": ["solid"], "search_terms": ["call", "earphone", "number", "sound", "support", "telephone", "voice", "volume-control-phone"]}, {"name": "PHP", "id": "php", "unicode": "f457", "styles": ["brands"], "search_terms": []}, {"name": "Pied Piper Logo", "id": "pied-piper", "unicode": "f2ae", "styles": ["brands"], "search_terms": []}, {"name": "Alternate Pied Piper <PERSON>", "id": "pied-piper-alt", "unicode": "f1a8", "styles": ["brands"], "search_terms": []}, {"name": "Pied Piper-hat", "id": "pied-piper-hat", "unicode": "f4e5", "styles": ["brands"], "search_terms": ["clothing"]}, {"name": "Pied Piper PP Logo (Old)", "id": "pied-piper-pp", "unicode": "f1a7", "styles": ["brands"], "search_terms": []}, {"name": "Piggy Bank", "id": "piggy-bank", "unicode": "f4d3", "styles": ["solid"], "search_terms": ["bank", "save", "savings"]}, {"name": "Pills", "id": "pills", "unicode": "f484", "styles": ["solid"], "search_terms": ["drugs", "medicine", "prescription", "tablets"]}, {"name": "Pinterest", "id": "pinterest", "unicode": "f0d2", "styles": ["brands"], "search_terms": []}, {"name": "Pinterest P", "id": "pinterest-p", "unicode": "f231", "styles": ["brands"], "search_terms": []}, {"name": "Pinterest Square", "id": "pinterest-square", "unicode": "f0d3", "styles": ["brands"], "search_terms": []}, {"name": "Pizza Slice", "id": "pizza-slice", "unicode": "f818", "styles": ["solid"], "search_terms": ["cheese", "chicago", "italian", "mozzarella", "new york", "pepperoni", "pie", "slice", "teenage mutant ninja turtles", "tomato"]}, {"name": "Place of Worship", "id": "place-of-worship", "unicode": "f67f", "styles": ["solid"], "search_terms": ["building", "church", "holy", "mosque", "synagogue"]}, {"name": "plane", "id": "plane", "unicode": "f072", "styles": ["solid"], "search_terms": ["airplane", "destination", "fly", "location", "mode", "travel", "trip"]}, {"name": "Plane Arrival", "id": "plane-arrival", "unicode": "f5af", "styles": ["solid"], "search_terms": ["airplane", "arriving", "destination", "fly", "land", "landing", "location", "mode", "travel", "trip"]}, {"name": "Plane Departure", "id": "plane-departure", "unicode": "f5b0", "styles": ["solid"], "search_terms": ["airplane", "departing", "destination", "fly", "location", "mode", "take off", "taking off", "travel", "trip"]}, {"name": "play", "id": "play", "unicode": "f04b", "styles": ["solid"], "search_terms": ["audio", "music", "playing", "sound", "start", "video"]}, {"name": "Play Circle", "id": "play-circle", "unicode": "f144", "styles": ["solid", "regular"], "search_terms": ["audio", "music", "playing", "sound", "start", "video"]}, {"name": "PlayStation", "id": "playstation", "unicode": "f3df", "styles": ["brands"], "search_terms": []}, {"name": "Plug", "id": "plug", "unicode": "f1e6", "styles": ["solid"], "search_terms": ["connect", "electric", "online", "power"]}, {"name": "plus", "id": "plus", "unicode": "f067", "styles": ["solid"], "search_terms": ["add", "create", "expand", "new", "positive", "shape"]}, {"name": "Plus Circle", "id": "plus-circle", "unicode": "f055", "styles": ["solid"], "search_terms": ["add", "create", "expand", "new", "positive", "shape"]}, {"name": "Plus Square", "id": "plus-square", "unicode": "f0fe", "styles": ["solid", "regular"], "search_terms": ["add", "create", "expand", "new", "positive", "shape"]}, {"name": "Podcast", "id": "podcast", "unicode": "f2ce", "styles": ["solid"], "search_terms": ["audio", "broadcast", "music", "sound"]}, {"name": "Poll", "id": "poll", "unicode": "f681", "styles": ["solid"], "search_terms": ["results", "survey", "trend", "vote", "voting"]}, {"name": "Poll H", "id": "poll-h", "unicode": "f682", "styles": ["solid"], "search_terms": ["results", "survey", "trend", "vote", "voting"]}, {"name": "Poo", "id": "poo", "unicode": "f2fe", "styles": ["solid"], "search_terms": ["crap", "poop", "shit", "smile", "turd"]}, {"name": "Poo Storm", "id": "poo-storm", "unicode": "f75a", "styles": ["solid"], "search_terms": ["bolt", "cloud", "euphemism", "lightning", "mess", "poop", "shit", "turd"]}, {"name": "<PERSON><PERSON>", "id": "poop", "unicode": "f619", "styles": ["solid"], "search_terms": ["crap", "poop", "shit", "smile", "turd"]}, {"name": "Portrait", "id": "portrait", "unicode": "f3e0", "styles": ["solid"], "search_terms": ["id", "image", "photo", "picture", "selfie"]}, {"name": "Pound Sign", "id": "pound-sign", "unicode": "f154", "styles": ["solid"], "search_terms": ["currency", "gbp", "money"]}, {"name": "Power Off", "id": "power-off", "unicode": "f011", "styles": ["solid"], "search_terms": ["cancel", "computer", "on", "reboot", "restart"]}, {"name": "<PERSON><PERSON>", "id": "pray", "unicode": "f683", "styles": ["solid"], "search_terms": ["kneel", "preach", "religion", "worship"]}, {"name": "Praying Hands", "id": "praying-hands", "unicode": "f684", "styles": ["solid"], "search_terms": ["kneel", "preach", "religion", "worship"]}, {"name": "Prescription", "id": "prescription", "unicode": "f5b1", "styles": ["solid"], "search_terms": ["drugs", "medical", "medicine", "pharmacy", "rx"]}, {"name": "Prescription Bottle", "id": "prescription-bottle", "unicode": "f485", "styles": ["solid"], "search_terms": ["drugs", "medical", "medicine", "pharmacy", "rx"]}, {"name": "Alternate Prescription Bottle", "id": "prescription-bottle-alt", "unicode": "f486", "styles": ["solid"], "search_terms": ["drugs", "medical", "medicine", "pharmacy", "rx"]}, {"name": "print", "id": "print", "unicode": "f02f", "styles": ["solid"], "search_terms": ["business", "copy", "document", "office", "paper"]}, {"name": "Procedures", "id": "procedures", "unicode": "f487", "styles": ["solid"], "search_terms": ["EKG", "bed", "electrocardiogram", "health", "hospital", "life", "patient", "vital"]}, {"name": "Product Hunt", "id": "product-hunt", "unicode": "f288", "styles": ["brands"], "search_terms": []}, {"name": "Project Diagram", "id": "project-diagram", "unicode": "f542", "styles": ["solid"], "search_terms": ["chart", "graph", "network", "pert"]}, {"name": "Pushed", "id": "pushed", "unicode": "f3e1", "styles": ["brands"], "search_terms": []}, {"name": "Puzzle Piece", "id": "puzzle-piece", "unicode": "f12e", "styles": ["solid"], "search_terms": ["add-on", "addon", "game", "section"]}, {"name": "Python", "id": "python", "unicode": "f3e2", "styles": ["brands"], "search_terms": []}, {"name": "QQ", "id": "qq", "unicode": "f1d6", "styles": ["brands"], "search_terms": []}, {"name": "qrcode", "id": "qrcode", "unicode": "f029", "styles": ["solid"], "search_terms": ["barcode", "info", "information", "scan"]}, {"name": "Question", "id": "question", "unicode": "f128", "styles": ["solid"], "search_terms": ["help", "information", "support", "unknown"]}, {"name": "Question Circle", "id": "question-circle", "unicode": "f059", "styles": ["solid", "regular"], "search_terms": ["help", "information", "support", "unknown"]}, {"name": "Quidditch", "id": "quidditch", "unicode": "f458", "styles": ["solid"], "search_terms": ["ball", "bludger", "broom", "golden snitch", "harry potter", "hogwarts", "quaffle", "sport", "wizard"]}, {"name": "QuinScape", "id": "quinscape", "unicode": "f459", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "quora", "unicode": "f2c4", "styles": ["brands"], "search_terms": []}, {"name": "quote-left", "id": "quote-left", "unicode": "f10d", "styles": ["solid"], "search_terms": ["mention", "note", "phrase", "text", "type"]}, {"name": "quote-right", "id": "quote-right", "unicode": "f10e", "styles": ["solid"], "search_terms": ["mention", "note", "phrase", "text", "type"]}, {"name": "Quran", "id": "quran", "unicode": "f687", "styles": ["solid"], "search_terms": ["book", "islam", "muslim", "religion"]}, {"name": "R Project", "id": "r-project", "unicode": "f4f7", "styles": ["brands"], "search_terms": []}, {"name": "Radiation", "id": "radiation", "unicode": "f7b9", "styles": ["solid"], "search_terms": ["danger", "dangerous", "deadly", "hazard", "nuclear", "radioactive", "warning"]}, {"name": "Alternate Radiation", "id": "radiation-alt", "unicode": "f7ba", "styles": ["solid"], "search_terms": ["danger", "dangerous", "deadly", "hazard", "nuclear", "radioactive", "warning"]}, {"name": "Rainbow", "id": "rainbow", "unicode": "f75b", "styles": ["solid"], "search_terms": ["gold", "lep<PERSON><PERSON><PERSON>", "prism", "rain", "sky"]}, {"name": "random", "id": "random", "unicode": "f074", "styles": ["solid"], "search_terms": ["arrows", "shuffle", "sort", "swap", "switch", "transfer"]}, {"name": "Raspberry Pi", "id": "raspberry-pi", "unicode": "f7bb", "styles": ["brands"], "search_terms": []}, {"name": "Ra<PERSON><PERSON>", "id": "ravelry", "unicode": "f2d9", "styles": ["brands"], "search_terms": []}, {"name": "React", "id": "react", "unicode": "f41b", "styles": ["brands"], "search_terms": []}, {"name": "ReactEurope", "id": "reacteurope", "unicode": "f75d", "styles": ["brands"], "search_terms": []}, {"name": "ReadMe", "id": "readme", "unicode": "f4d5", "styles": ["brands"], "search_terms": []}, {"name": "Rebel Alliance", "id": "rebel", "unicode": "f1d0", "styles": ["brands"], "search_terms": []}, {"name": "Receipt", "id": "receipt", "unicode": "f543", "styles": ["solid"], "search_terms": ["check", "invoice", "money", "pay", "table"]}, {"name": "Recycle", "id": "recycle", "unicode": "f1b8", "styles": ["solid"], "search_terms": ["Waste", "compost", "garbage", "reuse", "trash"]}, {"name": "red river", "id": "red-river", "unicode": "f3e3", "styles": ["brands"], "search_terms": []}, {"name": "reddit Logo", "id": "reddit", "unicode": "f1a1", "styles": ["brands"], "search_terms": []}, {"name": "reddit Alien", "id": "reddit-alien", "unicode": "f281", "styles": ["brands"], "search_terms": []}, {"name": "reddit Square", "id": "reddit-square", "unicode": "f1a2", "styles": ["brands"], "search_terms": []}, {"name": "Redhat", "id": "redhat", "unicode": "f7bc", "styles": ["brands"], "search_terms": ["linux", "operating system", "os"]}, {"name": "Redo", "id": "redo", "unicode": "f01e", "styles": ["solid"], "search_terms": ["forward", "refresh", "reload", "repeat"]}, {"name": "Alternate <PERSON>o", "id": "redo-alt", "unicode": "f2f9", "styles": ["solid"], "search_terms": ["forward", "refresh", "reload", "repeat"]}, {"name": "Registered Trademark", "id": "registered", "unicode": "f25d", "styles": ["solid", "regular"], "search_terms": ["copyright", "mark", "trademark"]}, {"name": "<PERSON><PERSON>", "id": "renren", "unicode": "f18b", "styles": ["brands"], "search_terms": []}, {"name": "Reply", "id": "reply", "unicode": "f3e5", "styles": ["solid"], "search_terms": ["mail", "message", "respond"]}, {"name": "reply-all", "id": "reply-all", "unicode": "f122", "styles": ["solid"], "search_terms": ["mail", "message", "respond"]}, {"name": "replyd", "id": "replyd", "unicode": "f3e6", "styles": ["brands"], "search_terms": []}, {"name": "Republican", "id": "republican", "unicode": "f75e", "styles": ["solid"], "search_terms": ["american", "conservative", "election", "elephant", "politics", "republican party", "right", "right-wing", "usa"]}, {"name": "Researchgate", "id": "researchgate", "unicode": "f4f8", "styles": ["brands"], "search_terms": []}, {"name": "Resolving", "id": "resolving", "unicode": "f3e7", "styles": ["brands"], "search_terms": []}, {"name": "Restroom", "id": "restroom", "unicode": "f7bd", "styles": ["solid"], "search_terms": ["bathroom", "john", "loo", "potty", "washroom", "waste", "wc"]}, {"name": "Retweet", "id": "retweet", "unicode": "f079", "styles": ["solid"], "search_terms": ["refresh", "reload", "share", "swap"]}, {"name": "Rev.io", "id": "rev", "unicode": "f5b2", "styles": ["brands"], "search_terms": []}, {"name": "Ribbon", "id": "ribbon", "unicode": "f4d6", "styles": ["solid"], "search_terms": ["badge", "cause", "lapel", "pin"]}, {"name": "Ring", "id": "ring", "unicode": "f70b", "styles": ["solid"], "search_terms": ["Dungeons & Dragons", "<PERSON><PERSON>", "band", "binding", "d&d", "dnd", "engagement", "fantasy", "gold", "jewelry", "marriage", "precious"]}, {"name": "road", "id": "road", "unicode": "f018", "styles": ["solid"], "search_terms": ["highway", "map", "pavement", "route", "street", "travel"]}, {"name": "Robot", "id": "robot", "unicode": "f544", "styles": ["solid"], "search_terms": ["android", "automate", "computer", "cyborg"]}, {"name": "rocket", "id": "rocket", "unicode": "f135", "styles": ["solid"], "search_terms": ["aircraft", "app", "jet", "launch", "nasa", "space"]}, {"name": "Rocket.Chat", "id": "rocketchat", "unicode": "f3e8", "styles": ["brands"], "search_terms": []}, {"name": "Rockrms", "id": "rockrms", "unicode": "f3e9", "styles": ["brands"], "search_terms": []}, {"name": "Route", "id": "route", "unicode": "f4d7", "styles": ["solid"], "search_terms": ["directions", "navigation", "travel"]}, {"name": "rss", "id": "rss", "unicode": "f09e", "styles": ["solid"], "search_terms": ["blog", "feed", "journal", "news", "writing"]}, {"name": "RSS Square", "id": "rss-square", "unicode": "f143", "styles": ["solid"], "search_terms": ["blog", "feed", "journal", "news", "writing"]}, {"name": "R<PERSON>le Sign", "id": "ruble-sign", "unicode": "f158", "styles": ["solid"], "search_terms": ["currency", "money", "rub"]}, {"name": "Ruler", "id": "ruler", "unicode": "f545", "styles": ["solid"], "search_terms": ["design", "draft", "length", "measure", "planning"]}, {"name": "Ruler Combined", "id": "ruler-combined", "unicode": "f546", "styles": ["solid"], "search_terms": ["design", "draft", "length", "measure", "planning"]}, {"name": "Ruler <PERSON>", "id": "ruler-horizontal", "unicode": "f547", "styles": ["solid"], "search_terms": ["design", "draft", "length", "measure", "planning"]}, {"name": "Ruler Vertical", "id": "ruler-vertical", "unicode": "f548", "styles": ["solid"], "search_terms": ["design", "draft", "length", "measure", "planning"]}, {"name": "Running", "id": "running", "unicode": "f70c", "styles": ["solid"], "search_terms": ["exercise", "health", "jog", "person", "run", "sport", "sprint"]}, {"name": "Indian Rupee Sign", "id": "rupee-sign", "unicode": "f156", "styles": ["solid"], "search_terms": ["currency", "indian", "inr", "money"]}, {"name": "Crying Face", "id": "sad-cry", "unicode": "f5b3", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "tear", "tears"]}, {"name": "Loudly Crying Face", "id": "sad-tear", "unicode": "f5b4", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "tear", "tears"]}, {"name": "Safari", "id": "safari", "unicode": "f267", "styles": ["brands"], "search_terms": ["browser"]}, {"name": "Salesforce", "id": "salesforce", "unicode": "f83b", "styles": ["brands"], "search_terms": []}, {"name": "Sass", "id": "sass", "unicode": "f41e", "styles": ["brands"], "search_terms": []}, {"name": "Satellite", "id": "satellite", "unicode": "f7bf", "styles": ["solid"], "search_terms": ["communications", "hardware", "orbit", "space"]}, {"name": "Satellite Dish", "id": "satellite-dish", "unicode": "f7c0", "styles": ["solid"], "search_terms": ["SETI", "communications", "hardware", "receiver", "saucer", "signal"]}, {"name": "Save", "id": "save", "unicode": "f0c7", "styles": ["solid", "regular"], "search_terms": ["disk", "download", "floppy", "floppy-o"]}, {"name": "SCHLIX", "id": "schlix", "unicode": "f3ea", "styles": ["brands"], "search_terms": []}, {"name": "School", "id": "school", "unicode": "f549", "styles": ["solid"], "search_terms": ["building", "education", "learn", "student", "teacher"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "id": "screwdriver", "unicode": "f54a", "styles": ["solid"], "search_terms": ["admin", "fix", "mechanic", "repair", "settings", "tool"]}, {"name": "Scribd", "id": "scribd", "unicode": "f28a", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "scroll", "unicode": "f70e", "styles": ["solid"], "search_terms": ["Dungeons & Dragons", "announcement", "d&d", "dnd", "fantasy", "paper", "script"]}, {"name": "Sd Card", "id": "sd-card", "unicode": "f7c2", "styles": ["solid"], "search_terms": ["image", "memory", "photo", "save"]}, {"name": "Search", "id": "search", "unicode": "f002", "styles": ["solid"], "search_terms": ["bigger", "enlarge", "find", "magnify", "preview", "zoom"]}, {"name": "Search Dollar", "id": "search-dollar", "unicode": "f688", "styles": ["solid"], "search_terms": ["bigger", "enlarge", "find", "magnify", "money", "preview", "zoom"]}, {"name": "Search Location", "id": "search-location", "unicode": "f689", "styles": ["solid"], "search_terms": ["bigger", "enlarge", "find", "magnify", "preview", "zoom"]}, {"name": "Search Minus", "id": "search-minus", "unicode": "f010", "styles": ["solid"], "search_terms": ["minify", "negative", "smaller", "zoom", "zoom out"]}, {"name": "Search Plus", "id": "search-plus", "unicode": "f00e", "styles": ["solid"], "search_terms": ["bigger", "enlarge", "magnify", "positive", "zoom", "zoom in"]}, {"name": "Searchengin", "id": "searchengin", "unicode": "f3eb", "styles": ["brands"], "search_terms": []}, {"name": "Seedling", "id": "seedling", "unicode": "f4d8", "styles": ["solid"], "search_terms": ["flora", "grow", "plant", "vegan"]}, {"name": "Sell<PERSON>", "id": "sellcast", "unicode": "f2da", "styles": ["brands"], "search_terms": ["eercast"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "sellsy", "unicode": "f213", "styles": ["brands"], "search_terms": []}, {"name": "Server", "id": "server", "unicode": "f233", "styles": ["solid"], "search_terms": ["computer", "cpu", "database", "hardware", "network"]}, {"name": "Servicestack", "id": "servicestack", "unicode": "f3ec", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "shapes", "unicode": "f61f", "styles": ["solid"], "search_terms": ["blocks", "build", "circle", "square", "triangle"]}, {"name": "Share", "id": "share", "unicode": "f064", "styles": ["solid"], "search_terms": ["forward", "save", "send", "social"]}, {"name": "Alternate Share", "id": "share-alt", "unicode": "f1e0", "styles": ["solid"], "search_terms": ["forward", "save", "send", "social"]}, {"name": "Alternate Share Square", "id": "share-alt-square", "unicode": "f1e1", "styles": ["solid"], "search_terms": ["forward", "save", "send", "social"]}, {"name": "Share Square", "id": "share-square", "unicode": "f14d", "styles": ["solid", "regular"], "search_terms": ["forward", "save", "send", "social"]}, {"name": "<PERSON><PERSON>", "id": "shekel-sign", "unicode": "f20b", "styles": ["solid"], "search_terms": ["currency", "ils", "money"]}, {"name": "Alternate Shield", "id": "shield-alt", "unicode": "f3ed", "styles": ["solid"], "search_terms": ["achievement", "award", "block", "defend", "security", "winner"]}, {"name": "Ship", "id": "ship", "unicode": "f21a", "styles": ["solid"], "search_terms": ["boat", "sea", "water"]}, {"name": "Shipping Fast", "id": "shipping-fast", "unicode": "f48b", "styles": ["solid"], "search_terms": ["express", "fedex", "mail", "overnight", "package", "ups"]}, {"name": "Shirts in Bulk", "id": "shirtsinbulk", "unicode": "f214", "styles": ["brands"], "search_terms": []}, {"name": "Shoe Prints", "id": "shoe-prints", "unicode": "f54b", "styles": ["solid"], "search_terms": ["feet", "footprints", "steps", "walk"]}, {"name": "Shopping Bag", "id": "shopping-bag", "unicode": "f290", "styles": ["solid"], "search_terms": ["buy", "checkout", "grocery", "payment", "purchase"]}, {"name": "Shopping Basket", "id": "shopping-basket", "unicode": "f291", "styles": ["solid"], "search_terms": ["buy", "checkout", "grocery", "payment", "purchase"]}, {"name": "shopping-cart", "id": "shopping-cart", "unicode": "f07a", "styles": ["solid"], "search_terms": ["buy", "checkout", "grocery", "payment", "purchase"]}, {"name": "Shopware", "id": "shopware", "unicode": "f5b5", "styles": ["brands"], "search_terms": []}, {"name": "Shower", "id": "shower", "unicode": "f2cc", "styles": ["solid"], "search_terms": ["bath", "clean", "faucet", "water"]}, {"name": "Shuttle Van", "id": "shuttle-van", "unicode": "f5b6", "styles": ["solid"], "search_terms": ["airport", "machine", "public-transportation", "transportation", "travel", "vehicle"]}, {"name": "Sign", "id": "sign", "unicode": "f4d9", "styles": ["solid"], "search_terms": ["directions", "real estate", "signage", "wayfinding"]}, {"name": "Alternate Sign In", "id": "sign-in-alt", "unicode": "f2f6", "styles": ["solid"], "search_terms": ["arrow", "enter", "join", "log in", "login", "sign in", "sign up", "sign-in", "signin", "signup"]}, {"name": "Sign Language", "id": "sign-language", "unicode": "f2a7", "styles": ["solid"], "search_terms": ["Translate", "asl", "deaf", "hands"]}, {"name": "Alternate Sign Out", "id": "sign-out-alt", "unicode": "f2f5", "styles": ["solid"], "search_terms": ["arrow", "exit", "leave", "log out", "logout", "sign-out"]}, {"name": "signal", "id": "signal", "unicode": "f012", "styles": ["solid"], "search_terms": ["bars", "graph", "online", "reception", "status"]}, {"name": "Signature", "id": "signature", "unicode": "f5b7", "styles": ["solid"], "search_terms": ["<PERSON>", "cursive", "name", "writing"]}, {"name": "SIM Card", "id": "sim-card", "unicode": "f7c4", "styles": ["solid"], "search_terms": ["hard drive", "hardware", "portable", "storage", "technology", "tiny"]}, {"name": "SimplyBuilt", "id": "simplybuilt", "unicode": "f215", "styles": ["brands"], "search_terms": []}, {"name": "SISTRIX", "id": "sistrix", "unicode": "f3ee", "styles": ["brands"], "search_terms": []}, {"name": "Sitemap", "id": "sitemap", "unicode": "f0e8", "styles": ["solid"], "search_terms": ["directory", "hierarchy", "ia", "information architecture", "organization"]}, {"name": "<PERSON><PERSON>", "id": "sith", "unicode": "f512", "styles": ["brands"], "search_terms": []}, {"name": "Skating", "id": "skating", "unicode": "f7c5", "styles": ["solid"], "search_terms": ["activity", "figure skating", "fitness", "ice", "person", "winter"]}, {"name": "Sketch", "id": "sketch", "unicode": "f7c6", "styles": ["brands"], "search_terms": ["app", "design", "interface"]}, {"name": "Skiing", "id": "skiing", "unicode": "f7c9", "styles": ["solid"], "search_terms": ["activity", "downhill", "fast", "fitness", "olympics", "outdoors", "person", "seasonal", "slalom"]}, {"name": "Skiing Nordic", "id": "skiing-nordic", "unicode": "f7ca", "styles": ["solid"], "search_terms": ["activity", "cross country", "fitness", "outdoors", "person", "seasonal"]}, {"name": "Skull", "id": "skull", "unicode": "f54c", "styles": ["solid"], "search_terms": ["bones", "skeleton", "x-ray", "yorick"]}, {"name": "Skull & Crossbones", "id": "skull-crossbones", "unicode": "f714", "styles": ["solid"], "search_terms": ["Dungeons & Dragons", "alert", "bones", "d&d", "danger", "dead", "deadly", "death", "dnd", "fantasy", "halloween", "holiday", "jolly-roger", "pirate", "poison", "skeleton", "warning"]}, {"name": "skyatlas", "id": "skyatlas", "unicode": "f216", "styles": ["brands"], "search_terms": []}, {"name": "Skype", "id": "skype", "unicode": "f17e", "styles": ["brands"], "search_terms": []}, {"name": "Slack Logo", "id": "slack", "unicode": "f198", "styles": ["brands"], "search_terms": ["anchor", "hash", "hashtag"]}, {"name": "Slack Hashtag", "id": "slack-hash", "unicode": "f3ef", "styles": ["brands"], "search_terms": ["anchor", "hash", "hashtag"]}, {"name": "Slash", "id": "slash", "unicode": "f715", "styles": ["solid"], "search_terms": ["cancel", "close", "mute", "off", "stop", "x"]}, {"name": "Sleigh", "id": "sleigh", "unicode": "f7cc", "styles": ["solid"], "search_terms": ["christmas", "claus", "fly", "holiday", "santa", "sled", "snow", "xmas"]}, {"name": "Horizontal Sliders", "id": "sliders-h", "unicode": "f1de", "styles": ["solid"], "search_terms": ["adjust", "settings", "sliders", "toggle"]}, {"name": "Slideshare", "id": "slideshare", "unicode": "f1e7", "styles": ["brands"], "search_terms": []}, {"name": "Smiling Face", "id": "smile", "unicode": "f118", "styles": ["solid", "regular"], "search_terms": ["approve", "emoticon", "face", "happy", "rating", "satisfied"]}, {"name": "Beaming Face With Smiling Eyes", "id": "smile-beam", "unicode": "f5b8", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "happy", "positive"]}, {"name": "Winking Face", "id": "smile-wink", "unicode": "f4da", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "happy", "hint", "joke"]}, {"name": "Smog", "id": "smog", "unicode": "f75f", "styles": ["solid"], "search_terms": ["dragon", "fog", "haze", "pollution", "smoke", "weather"]}, {"name": "Smoking", "id": "smoking", "unicode": "f48d", "styles": ["solid"], "search_terms": ["cancer", "cigarette", "nicotine", "smoking status", "tobacco"]}, {"name": "Smoking Ban", "id": "smoking-ban", "unicode": "f54d", "styles": ["solid"], "search_terms": ["ban", "cancel", "no smoking", "non-smoking"]}, {"name": "SMS", "id": "sms", "unicode": "f7cd", "styles": ["solid"], "search_terms": ["chat", "conversation", "message", "mobile", "notification", "phone", "sms", "texting"]}, {"name": "Snapchat", "id": "snapchat", "unicode": "f2ab", "styles": ["brands"], "search_terms": []}, {"name": "Snapchat Ghost", "id": "snapchat-ghost", "unicode": "f2ac", "styles": ["brands"], "search_terms": []}, {"name": "Snapchat Square", "id": "snapchat-square", "unicode": "f2ad", "styles": ["brands"], "search_terms": []}, {"name": "Snowboarding", "id": "snowboarding", "unicode": "f7ce", "styles": ["solid"], "search_terms": ["activity", "fitness", "olympics", "outdoors", "person"]}, {"name": "Snowflake", "id": "snowflake", "unicode": "f2dc", "styles": ["solid", "regular"], "search_terms": ["precipitation", "rain", "winter"]}, {"name": "Snowman", "id": "snowman", "unicode": "f7d0", "styles": ["solid"], "search_terms": ["decoration", "frost", "frosty", "holiday"]}, {"name": "Snowplow", "id": "snowplow", "unicode": "f7d2", "styles": ["solid"], "search_terms": ["clean up", "cold", "road", "storm", "winter"]}, {"name": "Socks", "id": "socks", "unicode": "f696", "styles": ["solid"], "search_terms": ["business socks", "business time", "clothing", "feet", "flight of the conchords", "wednesday"]}, {"name": "Solar Panel", "id": "solar-panel", "unicode": "f5ba", "styles": ["solid"], "search_terms": ["clean", "eco-friendly", "energy", "green", "sun"]}, {"name": "Sort", "id": "sort", "unicode": "f0dc", "styles": ["solid"], "search_terms": ["filter", "order"]}, {"name": "Sort Alpha Down", "id": "sort-alpha-down", "unicode": "f15d", "styles": ["solid"], "search_terms": ["filter", "order", "sort-alpha-asc"]}, {"name": "Sort Alpha Up", "id": "sort-alpha-up", "unicode": "f15e", "styles": ["solid"], "search_terms": ["filter", "order", "sort-alpha-desc"]}, {"name": "Sort Amount Down", "id": "sort-amount-down", "unicode": "f160", "styles": ["solid"], "search_terms": ["filter", "order", "sort-amount-asc"]}, {"name": "Sort Amount Up", "id": "sort-amount-up", "unicode": "f161", "styles": ["solid"], "search_terms": ["filter", "order", "sort-amount-desc"]}, {"name": "Sort Down (Descending)", "id": "sort-down", "unicode": "f0dd", "styles": ["solid"], "search_terms": ["arrow", "descending", "filter", "order", "sort-desc"]}, {"name": "Sort Numeric Down", "id": "sort-numeric-down", "unicode": "f162", "styles": ["solid"], "search_terms": ["filter", "numbers", "order", "sort-numeric-asc"]}, {"name": "Sort Numeric Up", "id": "sort-numeric-up", "unicode": "f163", "styles": ["solid"], "search_terms": ["filter", "numbers", "order", "sort-numeric-desc"]}, {"name": "Sort Up (Ascending)", "id": "sort-up", "unicode": "f0de", "styles": ["solid"], "search_terms": ["arrow", "ascending", "filter", "order", "sort-asc"]}, {"name": "SoundCloud", "id": "soundcloud", "unicode": "f1be", "styles": ["brands"], "search_terms": []}, {"name": "Sourcetree", "id": "sourcetree", "unicode": "f7d3", "styles": ["brands"], "search_terms": []}, {"name": "Spa", "id": "spa", "unicode": "f5bb", "styles": ["solid"], "search_terms": ["flora", "massage", "mindfulness", "plant", "wellness"]}, {"name": "Space Shuttle", "id": "space-shuttle", "unicode": "f197", "styles": ["solid"], "search_terms": ["astronaut", "machine", "nasa", "rocket", "transportation"]}, {"name": "Speakap", "id": "speakap", "unicode": "f3f3", "styles": ["brands"], "search_terms": []}, {"name": "Speaker <PERSON>", "id": "speaker-deck", "unicode": "f83c", "styles": ["brands"], "search_terms": []}, {"name": "Spider", "id": "spider", "unicode": "f717", "styles": ["solid"], "search_terms": ["arachnid", "bug", "charlotte", "crawl", "eight", "halloween"]}, {"name": "Spinner", "id": "spinner", "unicode": "f110", "styles": ["solid"], "search_terms": ["circle", "loading", "progress"]}, {"name": "Splotch", "id": "splotch", "unicode": "f5bc", "styles": ["solid"], "search_terms": ["Ink", "blob", "blotch", "glob", "stain"]}, {"name": "Spotify", "id": "spotify", "unicode": "f1bc", "styles": ["brands"], "search_terms": []}, {"name": "Spray Can", "id": "spray-can", "unicode": "f5bd", "styles": ["solid"], "search_terms": ["Paint", "aerosol", "design", "graffiti", "tag"]}, {"name": "Square", "id": "square", "unicode": "f0c8", "styles": ["solid", "regular"], "search_terms": ["block", "box", "shape"]}, {"name": "Square Full", "id": "square-full", "unicode": "f45c", "styles": ["solid"], "search_terms": ["block", "box", "shape"]}, {"name": "Alternate Square Root", "id": "square-root-alt", "unicode": "f698", "styles": ["solid"], "search_terms": ["arithmetic", "calculus", "division", "math"]}, {"name": "Squarespace", "id": "squarespace", "unicode": "f5be", "styles": ["brands"], "search_terms": []}, {"name": "Stack Exchange", "id": "stack-exchange", "unicode": "f18d", "styles": ["brands"], "search_terms": []}, {"name": "Stack Overflow", "id": "stack-overflow", "unicode": "f16c", "styles": ["brands"], "search_terms": []}, {"name": "Stamp", "id": "stamp", "unicode": "f5bf", "styles": ["solid"], "search_terms": ["art", "certificate", "imprint", "rubber", "seal"]}, {"name": "Star", "id": "star", "unicode": "f005", "styles": ["solid", "regular"], "search_terms": ["achievement", "award", "favorite", "important", "night", "rating", "score"]}, {"name": "Star and Crescent", "id": "star-and-crescent", "unicode": "f699", "styles": ["solid"], "search_terms": ["islam", "muslim", "religion"]}, {"name": "star-half", "id": "star-half", "unicode": "f089", "styles": ["solid", "regular"], "search_terms": ["achievement", "award", "rating", "score", "star-half-empty", "star-half-full"]}, {"name": "Alternate Star Half", "id": "star-half-alt", "unicode": "f5c0", "styles": ["solid"], "search_terms": ["achievement", "award", "rating", "score", "star-half-empty", "star-half-full"]}, {"name": "Star of David", "id": "star-of-david", "unicode": "f69a", "styles": ["solid"], "search_terms": ["jewish", "judaism", "religion"]}, {"name": "Star of Life", "id": "star-of-life", "unicode": "f621", "styles": ["solid"], "search_terms": ["doctor", "emt", "first aid", "health", "medical"]}, {"name": "StayLinked", "id": "staylinked", "unicode": "f3f5", "styles": ["brands"], "search_terms": []}, {"name": "Steam", "id": "steam", "unicode": "f1b6", "styles": ["brands"], "search_terms": []}, {"name": "Steam Square", "id": "steam-square", "unicode": "f1b7", "styles": ["brands"], "search_terms": []}, {"name": "Steam Symbol", "id": "steam-symbol", "unicode": "f3f6", "styles": ["brands"], "search_terms": []}, {"name": "step-backward", "id": "step-backward", "unicode": "f048", "styles": ["solid"], "search_terms": ["beginning", "first", "previous", "rewind", "start"]}, {"name": "step-forward", "id": "step-forward", "unicode": "f051", "styles": ["solid"], "search_terms": ["end", "last", "next"]}, {"name": "Stethoscope", "id": "stethoscope", "unicode": "f0f1", "styles": ["solid"], "search_terms": ["diagnosis", "doctor", "general practitioner", "hospital", "infirmary", "medicine", "office", "outpatient"]}, {"name": "<PERSON><PERSON>", "id": "sticker-mule", "unicode": "f3f7", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "sticky-note", "unicode": "f249", "styles": ["solid", "regular"], "search_terms": ["message", "note", "paper", "reminder", "sticker"]}, {"name": "stop", "id": "stop", "unicode": "f04d", "styles": ["solid"], "search_terms": ["block", "box", "square"]}, {"name": "Stop Circle", "id": "stop-circle", "unicode": "f28d", "styles": ["solid", "regular"], "search_terms": ["block", "box", "circle", "square"]}, {"name": "Stopwatch", "id": "stopwatch", "unicode": "f2f2", "styles": ["solid"], "search_terms": ["clock", "reminder", "time"]}, {"name": "Store", "id": "store", "unicode": "f54e", "styles": ["solid"], "search_terms": ["building", "buy", "purchase", "shopping"]}, {"name": "Alternate Store", "id": "store-alt", "unicode": "f54f", "styles": ["solid"], "search_terms": ["building", "buy", "purchase", "shopping"]}, {"name": "Strava", "id": "strava", "unicode": "f428", "styles": ["brands"], "search_terms": []}, {"name": "Stream", "id": "stream", "unicode": "f550", "styles": ["solid"], "search_terms": ["flow", "list", "timeline"]}, {"name": "Street View", "id": "street-view", "unicode": "f21d", "styles": ["solid"], "search_terms": ["directions", "location", "map", "navigation"]}, {"name": "Strikethrough", "id": "strikethrough", "unicode": "f0cc", "styles": ["solid"], "search_terms": ["cancel", "edit", "font", "format", "text", "type"]}, {"name": "Stripe", "id": "stripe", "unicode": "f429", "styles": ["brands"], "search_terms": []}, {"name": "Stripe S", "id": "stripe-s", "unicode": "f42a", "styles": ["brands"], "search_terms": []}, {"name": "Stroopwafel", "id": "stro<PERSON><PERSON><PERSON>l", "unicode": "f551", "styles": ["solid"], "search_terms": ["caramel", "cookie", "dessert", "sweets", "waffle"]}, {"name": "Studio Vinari", "id": "<PERSON><PERSON><PERSON>", "unicode": "f3f8", "styles": ["brands"], "search_terms": []}, {"name": "StumbleUpon Logo", "id": "stumbleupon", "unicode": "f1a4", "styles": ["brands"], "search_terms": []}, {"name": "StumbleUpon Circle", "id": "stumbleupon-circle", "unicode": "f1a3", "styles": ["brands"], "search_terms": []}, {"name": "subscript", "id": "subscript", "unicode": "f12c", "styles": ["solid"], "search_terms": ["edit", "font", "format", "text", "type"]}, {"name": "Subway", "id": "subway", "unicode": "f239", "styles": ["solid"], "search_terms": ["machine", "railway", "train", "transportation", "vehicle"]}, {"name": "Suitcase", "id": "suitcase", "unicode": "f0f2", "styles": ["solid"], "search_terms": ["baggage", "luggage", "move", "suitcase", "travel", "trip"]}, {"name": "Suitcase Rolling", "id": "suitcase-rolling", "unicode": "f5c1", "styles": ["solid"], "search_terms": ["baggage", "luggage", "move", "suitcase", "travel", "trip"]}, {"name": "Sun", "id": "sun", "unicode": "f185", "styles": ["solid", "regular"], "search_terms": ["brighten", "contrast", "day", "lighter", "sol", "solar", "star", "weather"]}, {"name": "Superpowers", "id": "superpowers", "unicode": "f2dd", "styles": ["brands"], "search_terms": []}, {"name": "superscript", "id": "superscript", "unicode": "f12b", "styles": ["solid"], "search_terms": ["edit", "exponential", "font", "format", "text", "type"]}, {"name": "Supple", "id": "supple", "unicode": "f3f9", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "surprise", "unicode": "f5c2", "styles": ["solid", "regular"], "search_terms": ["emoticon", "face", "shocked"]}, {"name": "Suse", "id": "suse", "unicode": "f7d6", "styles": ["brands"], "search_terms": ["linux", "operating system", "os"]}, {"name": "Swatchbook", "id": "swatchbook", "unicode": "f5c3", "styles": ["solid"], "search_terms": ["Pantone", "color", "design", "hue", "palette"]}, {"name": "Swimmer", "id": "swimmer", "unicode": "f5c4", "styles": ["solid"], "search_terms": ["athlete", "head", "man", "olympics", "person", "pool", "water"]}, {"name": "Swimming Pool", "id": "swimming-pool", "unicode": "f5c5", "styles": ["solid"], "search_terms": ["ladder", "recreation", "swim", "water"]}, {"name": "Symfony", "id": "symfony", "unicode": "f83d", "styles": ["brands"], "search_terms": []}, {"name": "Synagogue", "id": "synagogue", "unicode": "f69b", "styles": ["solid"], "search_terms": ["building", "jewish", "judaism", "religion", "star of david", "temple"]}, {"name": "Sync", "id": "sync", "unicode": "f021", "styles": ["solid"], "search_terms": ["exchange", "refresh", "reload", "rotate", "swap"]}, {"name": "Alternate Sync", "id": "sync-alt", "unicode": "f2f1", "styles": ["solid"], "search_terms": ["exchange", "refresh", "reload", "rotate", "swap"]}, {"name": "Syringe", "id": "syringe", "unicode": "f48e", "styles": ["solid"], "search_terms": ["doctor", "immunizations", "medical", "needle"]}, {"name": "table", "id": "table", "unicode": "f0ce", "styles": ["solid"], "search_terms": ["data", "excel", "spreadsheet"]}, {"name": "Table Tennis", "id": "table-tennis", "unicode": "f45d", "styles": ["solid"], "search_terms": ["ball", "paddle", "ping pong"]}, {"name": "tablet", "id": "tablet", "unicode": "f10a", "styles": ["solid"], "search_terms": ["apple", "device", "ipad", "kindle", "screen"]}, {"name": "Alternate Tablet", "id": "tablet-alt", "unicode": "f3fa", "styles": ["solid"], "search_terms": ["apple", "device", "ipad", "kindle", "screen"]}, {"name": "Tablets", "id": "tablets", "unicode": "f490", "styles": ["solid"], "search_terms": ["drugs", "medicine", "pills", "prescription"]}, {"name": "Alternate Tachometer", "id": "tachometer-alt", "unicode": "f3fd", "styles": ["solid"], "search_terms": ["dashboard", "fast", "odometer", "speed", "speedometer"]}, {"name": "tag", "id": "tag", "unicode": "f02b", "styles": ["solid"], "search_terms": ["discount", "label", "price", "shopping"]}, {"name": "tags", "id": "tags", "unicode": "f02c", "styles": ["solid"], "search_terms": ["discount", "label", "price", "shopping"]}, {"name": "Ta<PERSON>", "id": "tape", "unicode": "f4db", "styles": ["solid"], "search_terms": ["design", "package", "sticky"]}, {"name": "Tasks", "id": "tasks", "unicode": "f0ae", "styles": ["solid"], "search_terms": ["checklist", "downloading", "downloads", "loading", "progress", "project management", "settings", "to do"]}, {"name": "Taxi", "id": "taxi", "unicode": "f1ba", "styles": ["solid"], "search_terms": ["cab", "cabbie", "car", "car service", "lyft", "machine", "transportation", "travel", "uber", "vehicle"]}, {"name": "TeamSpeak", "id": "teamspeak", "unicode": "f4f9", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON>", "id": "teeth", "unicode": "f62e", "styles": ["solid"], "search_terms": ["bite", "dental", "dentist", "gums", "mouth", "smile", "tooth"]}, {"name": "Teeth Open", "id": "teeth-open", "unicode": "f62f", "styles": ["solid"], "search_terms": ["dental", "dentist", "gums bite", "mouth", "smile", "tooth"]}, {"name": "Telegram", "id": "telegram", "unicode": "f2c6", "styles": ["brands"], "search_terms": []}, {"name": "Telegram Plane", "id": "telegram-plane", "unicode": "f3fe", "styles": ["brands"], "search_terms": []}, {"name": "High Temperature", "id": "temperature-high", "unicode": "f769", "styles": ["solid"], "search_terms": ["cook", "mercury", "summer", "thermometer", "warm"]}, {"name": "Low Temperature", "id": "temperature-low", "unicode": "f76b", "styles": ["solid"], "search_terms": ["cold", "cool", "mercury", "thermometer", "winter"]}, {"name": "Tencent <PERSON>", "id": "tencent-weibo", "unicode": "f1d5", "styles": ["brands"], "search_terms": []}, {"name": "Tenge", "id": "tenge", "unicode": "f7d7", "styles": ["solid"], "search_terms": ["currency", "kazakhstan", "money", "price"]}, {"name": "Terminal", "id": "terminal", "unicode": "f120", "styles": ["solid"], "search_terms": ["code", "command", "console", "development", "prompt"]}, {"name": "text-height", "id": "text-height", "unicode": "f034", "styles": ["solid"], "search_terms": ["edit", "font", "format", "text", "type"]}, {"name": "text-width", "id": "text-width", "unicode": "f035", "styles": ["solid"], "search_terms": ["edit", "font", "format", "text", "type"]}, {"name": "th", "id": "th", "unicode": "f00a", "styles": ["solid"], "search_terms": ["blocks", "boxes", "grid", "squares"]}, {"name": "th-large", "id": "th-large", "unicode": "f009", "styles": ["solid"], "search_terms": ["blocks", "boxes", "grid", "squares"]}, {"name": "th-list", "id": "th-list", "unicode": "f00b", "styles": ["solid"], "search_terms": ["checklist", "completed", "done", "finished", "ol", "todo", "ul"]}, {"name": "The Red Yeti", "id": "the-red-yeti", "unicode": "f69d", "styles": ["brands"], "search_terms": []}, {"name": "Theater Masks", "id": "theater-masks", "unicode": "f630", "styles": ["solid"], "search_terms": ["comedy", "perform", "theatre", "tragedy"]}, {"name": "Themeco", "id": "themeco", "unicode": "f5c6", "styles": ["brands"], "search_terms": []}, {"name": "ThemeIsle", "id": "<PERSON><PERSON><PERSON>", "unicode": "f2b2", "styles": ["brands"], "search_terms": []}, {"name": "Thermometer", "id": "thermometer", "unicode": "f491", "styles": ["solid"], "search_terms": ["mercury", "status", "temperature"]}, {"name": "Thermometer Empty", "id": "thermometer-empty", "unicode": "f2cb", "styles": ["solid"], "search_terms": ["cold", "mercury", "status", "temperature"]}, {"name": "Thermometer Full", "id": "thermometer-full", "unicode": "f2c7", "styles": ["solid"], "search_terms": ["fever", "hot", "mercury", "status", "temperature"]}, {"name": "Thermometer 1/2 Full", "id": "thermometer-half", "unicode": "f2c9", "styles": ["solid"], "search_terms": ["mercury", "status", "temperature"]}, {"name": "Thermometer 1/4 Full", "id": "thermometer-quarter", "unicode": "f2ca", "styles": ["solid"], "search_terms": ["mercury", "status", "temperature"]}, {"name": "Thermometer 3/4 Full", "id": "thermometer-three-quarters", "unicode": "f2c8", "styles": ["solid"], "search_terms": ["mercury", "status", "temperature"]}, {"name": "Think Peaks", "id": "think-peaks", "unicode": "f731", "styles": ["brands"], "search_terms": []}, {"name": "thumbs-down", "id": "thumbs-down", "unicode": "f165", "styles": ["solid", "regular"], "search_terms": ["disagree", "disapprove", "dislike", "hand", "social", "thumbs-o-down"]}, {"name": "thumbs-up", "id": "thumbs-up", "unicode": "f164", "styles": ["solid", "regular"], "search_terms": ["agree", "approve", "favorite", "hand", "like", "ok", "okay", "social", "success", "thumbs-o-up", "yes", "you got it dude"]}, {"name": "Thumbtack", "id": "thumbtack", "unicode": "f08d", "styles": ["solid"], "search_terms": ["coordinates", "location", "marker", "pin", "thumb-tack"]}, {"name": "Alternate Ticket", "id": "ticket-alt", "unicode": "f3ff", "styles": ["solid"], "search_terms": ["movie", "pass", "support", "ticket"]}, {"name": "Times", "id": "times", "unicode": "f00d", "styles": ["solid"], "search_terms": ["close", "cross", "error", "exit", "incorrect", "notice", "notification", "notify", "problem", "wrong", "x"]}, {"name": "Times Circle", "id": "times-circle", "unicode": "f057", "styles": ["solid", "regular"], "search_terms": ["close", "cross", "exit", "incorrect", "notice", "notification", "notify", "problem", "wrong", "x"]}, {"name": "tint", "id": "tint", "unicode": "f043", "styles": ["solid"], "search_terms": ["color", "drop", "droplet", "raindrop", "waterdrop"]}, {"name": "<PERSON><PERSON>", "id": "tint-slash", "unicode": "f5c7", "styles": ["solid"], "search_terms": ["color", "drop", "droplet", "raindrop", "waterdrop"]}, {"name": "Tired Face", "id": "tired", "unicode": "f5c8", "styles": ["solid", "regular"], "search_terms": ["angry", "emoticon", "face", "grumpy", "upset"]}, {"name": "Toggle Off", "id": "toggle-off", "unicode": "f204", "styles": ["solid"], "search_terms": ["switch"]}, {"name": "Toggle On", "id": "toggle-on", "unicode": "f205", "styles": ["solid"], "search_terms": ["switch"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "toilet", "unicode": "f7d8", "styles": ["solid"], "search_terms": ["bathroom", "flush", "john", "loo", "pee", "plumbing", "poop", "porcelain", "potty", "restroom", "throne", "washroom", "waste", "wc"]}, {"name": "Toilet Paper", "id": "toilet-paper", "unicode": "f71e", "styles": ["solid"], "search_terms": ["bathroom", "halloween", "holiday", "lavatory", "prank", "restroom", "roll"]}, {"name": "Toolbox", "id": "toolbox", "unicode": "f552", "styles": ["solid"], "search_terms": ["admin", "container", "fix", "repair", "settings", "tools"]}, {"name": "Tools", "id": "tools", "unicode": "f7d9", "styles": ["solid"], "search_terms": ["admin", "fix", "repair", "screwdriver", "settings", "tools", "wrench"]}, {"name": "Tooth", "id": "tooth", "unicode": "f5c9", "styles": ["solid"], "search_terms": ["bicuspid", "dental", "dentist", "molar", "mouth", "teeth"]}, {"name": "Torah", "id": "torah", "unicode": "f6a0", "styles": ["solid"], "search_terms": ["book", "jewish", "judaism", "religion"]}, {"name": "Torii Gate", "id": "torii-gate", "unicode": "f6a1", "styles": ["solid"], "search_terms": ["building", "shintoism"]}, {"name": "Tractor", "id": "tractor", "unicode": "f722", "styles": ["solid"], "search_terms": ["agriculture", "farm", "vehicle"]}, {"name": "Trade Federation", "id": "trade-federation", "unicode": "f513", "styles": ["brands"], "search_terms": []}, {"name": "Trademark", "id": "trademark", "unicode": "f25c", "styles": ["solid"], "search_terms": ["copyright", "register", "symbol"]}, {"name": "Traffic Light", "id": "traffic-light", "unicode": "f637", "styles": ["solid"], "search_terms": ["direction", "road", "signal", "travel"]}, {"name": "Train", "id": "train", "unicode": "f238", "styles": ["solid"], "search_terms": ["bullet", "commute", "locomotive", "railway", "subway"]}, {"name": "Tram", "id": "tram", "unicode": "f7da", "styles": ["solid"], "search_terms": ["crossing", "machine", "mountains", "seasonal", "transportation"]}, {"name": "Transgender", "id": "transgender", "unicode": "f224", "styles": ["solid"], "search_terms": ["intersex"]}, {"name": "Alternate Transgender", "id": "transgender-alt", "unicode": "f225", "styles": ["solid"], "search_terms": ["intersex"]}, {"name": "Trash", "id": "trash", "unicode": "f1f8", "styles": ["solid"], "search_terms": ["delete", "garbage", "hide", "remove"]}, {"name": "Alternate Trash", "id": "trash-alt", "unicode": "f2ed", "styles": ["solid", "regular"], "search_terms": ["delete", "garbage", "hide", "remove", "trash-o"]}, {"name": "Trash Restore", "id": "trash-restore", "unicode": "f829", "styles": ["solid"], "search_terms": ["back", "control z", "oops", "undo"]}, {"name": "Alternative Trash Restore", "id": "trash-restore-alt", "unicode": "f82a", "styles": ["solid"], "search_terms": ["back", "control z", "oops", "undo"]}, {"name": "Tree", "id": "tree", "unicode": "f1bb", "styles": ["solid"], "search_terms": ["bark", "fall", "flora", "forest", "nature", "plant", "seasonal"]}, {"name": "Trello", "id": "trello", "unicode": "f181", "styles": ["brands"], "search_terms": ["atlassian"]}, {"name": "TripAdvisor", "id": "tripadvisor", "unicode": "f262", "styles": ["brands"], "search_terms": []}, {"name": "trophy", "id": "trophy", "unicode": "f091", "styles": ["solid"], "search_terms": ["achievement", "award", "cup", "game", "winner"]}, {"name": "truck", "id": "truck", "unicode": "f0d1", "styles": ["solid"], "search_terms": ["cargo", "delivery", "shipping", "vehicle"]}, {"name": "Truck Loading", "id": "truck-loading", "unicode": "f4de", "styles": ["solid"], "search_terms": ["box", "cargo", "delivery", "inventory", "moving", "rental", "vehicle"]}, {"name": "Truck Monster", "id": "truck-monster", "unicode": "f63b", "styles": ["solid"], "search_terms": ["offroad", "vehicle", "wheel"]}, {"name": "Truck Moving", "id": "truck-moving", "unicode": "f4df", "styles": ["solid"], "search_terms": ["cargo", "inventory", "rental", "vehicle"]}, {"name": "Truck Side", "id": "truck-pickup", "unicode": "f63c", "styles": ["solid"], "search_terms": ["cargo", "vehicle"]}, {"name": "T-Shirt", "id": "tshirt", "unicode": "f553", "styles": ["solid"], "search_terms": ["clothing", "fashion", "garment", "shirt"]}, {"name": "TTY", "id": "tty", "unicode": "f1e4", "styles": ["solid"], "search_terms": ["communication", "deaf", "telephone", "teletypewriter", "text"]}, {"name": "Tumblr", "id": "tumblr", "unicode": "f173", "styles": ["brands"], "search_terms": []}, {"name": "Tumblr Square", "id": "tumblr-square", "unicode": "f174", "styles": ["brands"], "search_terms": []}, {"name": "Television", "id": "tv", "unicode": "f26c", "styles": ["solid"], "search_terms": ["computer", "display", "monitor", "television"]}, {"name": "Twitch", "id": "twitch", "unicode": "f1e8", "styles": ["brands"], "search_terms": []}, {"name": "Twitter", "id": "twitter", "unicode": "f099", "styles": ["brands"], "search_terms": ["social network", "tweet"]}, {"name": "Twitter Square", "id": "twitter-square", "unicode": "f081", "styles": ["brands"], "search_terms": ["social network", "tweet"]}, {"name": "Typo3", "id": "typo3", "unicode": "f42b", "styles": ["brands"], "search_terms": []}, {"name": "Uber", "id": "uber", "unicode": "f402", "styles": ["brands"], "search_terms": []}, {"name": "Ubuntu", "id": "ubuntu", "unicode": "f7df", "styles": ["brands"], "search_terms": ["linux", "operating system", "os"]}, {"name": "UIkit", "id": "uikit", "unicode": "f403", "styles": ["brands"], "search_terms": []}, {"name": "Um<PERSON>lla", "id": "umbrella", "unicode": "f0e9", "styles": ["solid"], "search_terms": ["protection", "rain", "storm", "wet"]}, {"name": "Umbrella Beach", "id": "umbrella-beach", "unicode": "f5ca", "styles": ["solid"], "search_terms": ["protection", "recreation", "sand", "shade", "summer", "sun"]}, {"name": "Underline", "id": "underline", "unicode": "f0cd", "styles": ["solid"], "search_terms": ["edit", "emphasis", "format", "text", "writing"]}, {"name": "Undo", "id": "undo", "unicode": "f0e2", "styles": ["solid"], "search_terms": ["back", "control z", "exchange", "oops", "return", "rotate", "swap"]}, {"name": "Alternate Undo", "id": "undo-alt", "unicode": "f2ea", "styles": ["solid"], "search_terms": ["back", "control z", "exchange", "oops", "return", "swap"]}, {"name": "Uniregistry", "id": "uniregistry", "unicode": "f404", "styles": ["brands"], "search_terms": []}, {"name": "Universal Access", "id": "universal-access", "unicode": "f29a", "styles": ["solid"], "search_terms": ["accessibility", "hearing", "person", "seeing", "visual impairment"]}, {"name": "University", "id": "university", "unicode": "f19c", "styles": ["solid"], "search_terms": ["bank", "building", "college", "higher education - students", "institution"]}, {"name": "unlink", "id": "unlink", "unicode": "f127", "styles": ["solid"], "search_terms": ["attachment", "chain", "chain-broken", "remove"]}, {"name": "unlock", "id": "unlock", "unicode": "f09c", "styles": ["solid"], "search_terms": ["admin", "lock", "password", "private", "protect"]}, {"name": "Alternate Unlock", "id": "unlock-alt", "unicode": "f13e", "styles": ["solid"], "search_terms": ["admin", "lock", "password", "private", "protect"]}, {"name": "Untappd", "id": "untappd", "unicode": "f405", "styles": ["brands"], "search_terms": []}, {"name": "Upload", "id": "upload", "unicode": "f093", "styles": ["solid"], "search_terms": ["hard drive", "import", "publish"]}, {"name": "UPS", "id": "ups", "unicode": "f7e0", "styles": ["brands"], "search_terms": ["United Parcel Service", "package", "shipping"]}, {"name": "USB", "id": "usb", "unicode": "f287", "styles": ["brands"], "search_terms": []}, {"name": "User", "id": "user", "unicode": "f007", "styles": ["solid", "regular"], "search_terms": ["account", "avatar", "head", "human", "man", "person", "profile"]}, {"name": "Alternate User", "id": "user-alt", "unicode": "f406", "styles": ["solid"], "search_terms": ["account", "avatar", "head", "human", "man", "person", "profile"]}, {"name": "Alternate User Slash", "id": "user-alt-slash", "unicode": "f4fa", "styles": ["solid"], "search_terms": ["account", "avatar", "head", "human", "man", "person", "profile"]}, {"name": "User Astronaut", "id": "user-astronaut", "unicode": "f4fb", "styles": ["solid"], "search_terms": ["avatar", "clothing", "cosmonaut", "nasa", "space", "suit"]}, {"name": "User Check", "id": "user-check", "unicode": "f4fc", "styles": ["solid"], "search_terms": ["accept", "check", "person", "verified"]}, {"name": "User Circle", "id": "user-circle", "unicode": "f2bd", "styles": ["solid", "regular"], "search_terms": ["account", "avatar", "head", "human", "man", "person", "profile"]}, {"name": "User Clock", "id": "user-clock", "unicode": "f4fd", "styles": ["solid"], "search_terms": ["alert", "person", "remind", "time"]}, {"name": "User Cog", "id": "user-cog", "unicode": "f4fe", "styles": ["solid"], "search_terms": ["admin", "cog", "person", "settings"]}, {"name": "User Edit", "id": "user-edit", "unicode": "f4ff", "styles": ["solid"], "search_terms": ["edit", "pen", "pencil", "person", "update", "write"]}, {"name": "User Friends", "id": "user-friends", "unicode": "f500", "styles": ["solid"], "search_terms": ["group", "people", "person", "team", "users"]}, {"name": "User Graduate", "id": "user-graduate", "unicode": "f501", "styles": ["solid"], "search_terms": ["cap", "clothing", "commencement", "gown", "graduation", "person", "student"]}, {"name": "User Injured", "id": "user-injured", "unicode": "f728", "styles": ["solid"], "search_terms": ["cast", "injury", "ouch", "patient", "person", "sling"]}, {"name": "User Lock", "id": "user-lock", "unicode": "f502", "styles": ["solid"], "search_terms": ["admin", "lock", "person", "private", "unlock"]}, {"name": "Doctor", "id": "user-md", "unicode": "f0f0", "styles": ["solid"], "search_terms": ["job", "medical", "nurse", "occupation", "physician", "profile", "surgeon"]}, {"name": "User <PERSON>us", "id": "user-minus", "unicode": "f503", "styles": ["solid"], "search_terms": ["delete", "negative", "remove"]}, {"name": "User Ninja", "id": "user-ninja", "unicode": "f504", "styles": ["solid"], "search_terms": ["assassin", "avatar", "dangerous", "deadly", "sneaky"]}, {"name": "Nurse", "id": "user-nurse", "unicode": "f82f", "styles": ["solid"], "search_terms": ["doctor", "midwife", "practitioner", "surgeon"]}, {"name": "User Plus", "id": "user-plus", "unicode": "f234", "styles": ["solid"], "search_terms": ["add", "avatar", "positive", "sign up", "signup", "team"]}, {"name": "User Secret", "id": "user-secret", "unicode": "f21b", "styles": ["solid"], "search_terms": ["clothing", "coat", "hat", "incognito", "person", "privacy", "spy", "whisper"]}, {"name": "User Shield", "id": "user-shield", "unicode": "f505", "styles": ["solid"], "search_terms": ["admin", "person", "private", "protect", "safe"]}, {"name": "User Slash", "id": "user-slash", "unicode": "f506", "styles": ["solid"], "search_terms": ["ban", "delete", "remove"]}, {"name": "User Tag", "id": "user-tag", "unicode": "f507", "styles": ["solid"], "search_terms": ["avatar", "discount", "label", "person", "role", "special"]}, {"name": "User <PERSON>ie", "id": "user-tie", "unicode": "f508", "styles": ["solid"], "search_terms": ["avatar", "business", "clothing", "formal", "professional", "suit"]}, {"name": "Remove User", "id": "user-times", "unicode": "f235", "styles": ["solid"], "search_terms": ["archive", "delete", "remove", "x"]}, {"name": "Users", "id": "users", "unicode": "f0c0", "styles": ["solid"], "search_terms": ["friends", "group", "people", "persons", "profiles", "team"]}, {"name": "Users Cog", "id": "users-cog", "unicode": "f509", "styles": ["solid"], "search_terms": ["admin", "cog", "group", "person", "settings", "team"]}, {"name": "United States Postal Service", "id": "usps", "unicode": "f7e1", "styles": ["brands"], "search_terms": ["american", "package", "shipping", "usa"]}, {"name": "us-Sunnah Foundation", "id": "ussunnah", "unicode": "f407", "styles": ["brands"], "search_terms": []}, {"name": "Utensil Spoon", "id": "utensil-spoon", "unicode": "f2e5", "styles": ["solid"], "search_terms": ["cutlery", "dining", "scoop", "silverware", "spoon"]}, {"name": "Utensils", "id": "utensils", "unicode": "f2e7", "styles": ["solid"], "search_terms": ["cutlery", "dining", "dinner", "eat", "food", "fork", "knife", "restaurant"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "vaadin", "unicode": "f408", "styles": ["brands"], "search_terms": []}, {"name": "Vector Square", "id": "vector-square", "unicode": "f5cb", "styles": ["solid"], "search_terms": ["anchors", "lines", "object", "render", "shape"]}, {"name": "Venus", "id": "venus", "unicode": "f221", "styles": ["solid"], "search_terms": ["female"]}, {"name": "Venus Double", "id": "venus-double", "unicode": "f226", "styles": ["solid"], "search_terms": ["female"]}, {"name": "Venus Mars", "id": "venus-mars", "unicode": "f228", "styles": ["solid"], "search_terms": ["Gender"]}, {"name": "Viacoin", "id": "viacoin", "unicode": "f237", "styles": ["brands"], "search_terms": []}, {"name": "Video", "id": "viadeo", "unicode": "f2a9", "styles": ["brands"], "search_terms": []}, {"name": "Video Square", "id": "viadeo-square", "unicode": "f2aa", "styles": ["brands"], "search_terms": []}, {"name": "Vial", "id": "vial", "unicode": "f492", "styles": ["solid"], "search_terms": ["experiment", "lab", "sample", "science", "test", "test tube"]}, {"name": "Vials", "id": "vials", "unicode": "f493", "styles": ["solid"], "search_terms": ["experiment", "lab", "sample", "science", "test", "test tube"]}, {"name": "Viber", "id": "viber", "unicode": "f409", "styles": ["brands"], "search_terms": []}, {"name": "Video", "id": "video", "unicode": "f03d", "styles": ["solid"], "search_terms": ["camera", "film", "movie", "record", "video-camera"]}, {"name": "Video Slash", "id": "video-slash", "unicode": "f4e2", "styles": ["solid"], "search_terms": ["add", "create", "film", "new", "positive", "record", "video"]}, {"name": "<PERSON><PERSON><PERSON>", "id": "vihara", "unicode": "f6a7", "styles": ["solid"], "search_terms": ["buddhism", "buddhist", "building", "monastery"]}, {"name": "Vimeo", "id": "vimeo", "unicode": "f40a", "styles": ["brands"], "search_terms": []}, {"name": "Vimeo Square", "id": "vimeo-square", "unicode": "f194", "styles": ["brands"], "search_terms": []}, {"name": "Vimeo", "id": "vimeo-v", "unicode": "f27d", "styles": ["brands"], "search_terms": ["vimeo"]}, {"name": "Vine", "id": "vine", "unicode": "f1ca", "styles": ["brands"], "search_terms": []}, {"name": "VK", "id": "vk", "unicode": "f189", "styles": ["brands"], "search_terms": []}, {"name": "VNV", "id": "vnv", "unicode": "f40b", "styles": ["brands"], "search_terms": []}, {"name": "Volleyball Ball", "id": "volleyball-ball", "unicode": "f45f", "styles": ["solid"], "search_terms": ["beach", "olympics", "sport"]}, {"name": "Volume Down", "id": "volume-down", "unicode": "f027", "styles": ["solid"], "search_terms": ["audio", "lower", "music", "quieter", "sound", "speaker"]}, {"name": "Volume Mute", "id": "volume-mute", "unicode": "f6a9", "styles": ["solid"], "search_terms": ["audio", "music", "quiet", "sound", "speaker"]}, {"name": "Volume Off", "id": "volume-off", "unicode": "f026", "styles": ["solid"], "search_terms": ["audio", "ban", "music", "mute", "quiet", "silent", "sound"]}, {"name": "Volume Up", "id": "volume-up", "unicode": "f028", "styles": ["solid"], "search_terms": ["audio", "higher", "louder", "music", "sound", "speaker"]}, {"name": "Vote Yea", "id": "vote-yea", "unicode": "f772", "styles": ["solid"], "search_terms": ["accept", "cast", "election", "politics", "positive", "yes"]}, {"name": "Cardboard VR", "id": "vr-cardboard", "unicode": "f729", "styles": ["solid"], "search_terms": ["3d", "augment", "google", "reality", "virtual"]}, {"name": "Vue.js", "id": "v<PERSON><PERSON><PERSON>", "unicode": "f41f", "styles": ["brands"], "search_terms": []}, {"name": "Walking", "id": "walking", "unicode": "f554", "styles": ["solid"], "search_terms": ["exercise", "health", "pedometer", "person", "steps"]}, {"name": "Wallet", "id": "wallet", "unicode": "f555", "styles": ["solid"], "search_terms": ["billfold", "cash", "currency", "money"]}, {"name": "Warehouse", "id": "warehouse", "unicode": "f494", "styles": ["solid"], "search_terms": ["building", "capacity", "garage", "inventory", "storage"]}, {"name": "Water", "id": "water", "unicode": "f773", "styles": ["solid"], "search_terms": ["lake", "liquid", "ocean", "sea", "swim", "wet"]}, {"name": "Square Wave", "id": "wave-square", "unicode": "f83e", "styles": ["solid"], "search_terms": ["frequency", "pulse", "signal"]}, {"name": "Waze", "id": "waze", "unicode": "f83f", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "weebly", "unicode": "f5cc", "styles": ["brands"], "search_terms": []}, {"name": "Weibo", "id": "weibo", "unicode": "f18a", "styles": ["brands"], "search_terms": []}, {"name": "Weight", "id": "weight", "unicode": "f496", "styles": ["solid"], "search_terms": ["health", "measurement", "scale", "weight"]}, {"name": "Hanging Weight", "id": "weight-hanging", "unicode": "f5cd", "styles": ["solid"], "search_terms": ["anvil", "heavy", "measurement"]}, {"name": "<PERSON>xin (WeChat)", "id": "weixin", "unicode": "f1d7", "styles": ["brands"], "search_terms": []}, {"name": "What's App", "id": "whatsapp", "unicode": "f232", "styles": ["brands"], "search_terms": []}, {"name": "What's App Square", "id": "whatsapp-square", "unicode": "f40c", "styles": ["brands"], "search_terms": []}, {"name": "Wheelchair", "id": "wheelchair", "unicode": "f193", "styles": ["solid"], "search_terms": ["accessible", "handicap", "person"]}, {"name": "WHMCS", "id": "whmcs", "unicode": "f40d", "styles": ["brands"], "search_terms": []}, {"name": "WiFi", "id": "wifi", "unicode": "f1eb", "styles": ["solid"], "search_terms": ["connection", "hotspot", "internet", "network", "wireless"]}, {"name": "Wikipedia W", "id": "wikipedia-w", "unicode": "f266", "styles": ["brands"], "search_terms": []}, {"name": "Wind", "id": "wind", "unicode": "f72e", "styles": ["solid"], "search_terms": ["air", "blow", "breeze", "fall", "seasonal", "weather"]}, {"name": "Window Close", "id": "window-close", "unicode": "f410", "styles": ["solid", "regular"], "search_terms": ["browser", "cancel", "computer", "development"]}, {"name": "Window Maximize", "id": "window-maximize", "unicode": "f2d0", "styles": ["solid", "regular"], "search_terms": ["browser", "computer", "development", "expand"]}, {"name": "Window Minimize", "id": "window-minimize", "unicode": "f2d1", "styles": ["solid", "regular"], "search_terms": ["browser", "collapse", "computer", "development"]}, {"name": "Window Restore", "id": "window-restore", "unicode": "f2d2", "styles": ["solid", "regular"], "search_terms": ["browser", "computer", "development"]}, {"name": "Windows", "id": "windows", "unicode": "f17a", "styles": ["brands"], "search_terms": ["microsoft", "operating system", "os"]}, {"name": "Wine Bottle", "id": "wine-bottle", "unicode": "f72f", "styles": ["solid"], "search_terms": ["alcohol", "beverage", "cabernet", "drink", "glass", "grapes", "merlot", "sauvignon"]}, {"name": "Wine Glass", "id": "wine-glass", "unicode": "f4e3", "styles": ["solid"], "search_terms": ["alcohol", "beverage", "cabernet", "drink", "grapes", "merlot", "sauvignon"]}, {"name": "Alternate Wine Glas", "id": "wine-glass-alt", "unicode": "f5ce", "styles": ["solid"], "search_terms": ["alcohol", "beverage", "cabernet", "drink", "grapes", "merlot", "sauvignon"]}, {"name": "Wix", "id": "wix", "unicode": "f5cf", "styles": ["brands"], "search_terms": []}, {"name": "Wizards of the Coast", "id": "wizards-of-the-coast", "unicode": "f730", "styles": ["brands"], "search_terms": ["Dungeons & Dragons", "d&d", "dnd", "fantasy", "game", "gaming", "tabletop"]}, {"name": "Wolf Pack Battalion", "id": "wolf-pack-battalion", "unicode": "f514", "styles": ["brands"], "search_terms": []}, {"name": "Won Sign", "id": "won-sign", "unicode": "f159", "styles": ["solid"], "search_terms": ["currency", "krw", "money"]}, {"name": "WordPress Logo", "id": "wordpress", "unicode": "f19a", "styles": ["brands"], "search_terms": []}, {"name": "Wordpress Simple", "id": "wordpress-simple", "unicode": "f411", "styles": ["brands"], "search_terms": []}, {"name": "WP<PERSON><PERSON><PERSON><PERSON>", "id": "wpbeginner", "unicode": "f297", "styles": ["brands"], "search_terms": []}, {"name": "WPExplorer", "id": "wpexplorer", "unicode": "f2de", "styles": ["brands"], "search_terms": []}, {"name": "WPForms", "id": "wpforms", "unicode": "f298", "styles": ["brands"], "search_terms": []}, {"name": "wpressr", "id": "wpressr", "unicode": "f3e4", "styles": ["brands"], "search_terms": ["rendact"]}, {"name": "<PERSON><PERSON>", "id": "wrench", "unicode": "f0ad", "styles": ["solid"], "search_terms": ["construction", "fix", "mechanic", "plumbing", "settings", "spanner", "tool", "update"]}, {"name": "X-Ray", "id": "x-ray", "unicode": "f497", "styles": ["solid"], "search_terms": ["health", "medical", "radiological images", "radiology", "skeleton"]}, {"name": "Xbox", "id": "xbox", "unicode": "f412", "styles": ["brands"], "search_terms": []}, {"name": "Xi<PERSON>", "id": "xing", "unicode": "f168", "styles": ["brands"], "search_terms": []}, {"name": "Xing Square", "id": "xing-square", "unicode": "f169", "styles": ["brands"], "search_terms": []}, {"name": "Y Combinator", "id": "y-combinator", "unicode": "f23b", "styles": ["brands"], "search_terms": []}, {"name": "Yahoo Logo", "id": "yahoo", "unicode": "f19e", "styles": ["brands"], "search_terms": []}, {"name": "Yammer", "id": "yammer", "unicode": "f840", "styles": ["brands"], "search_terms": []}, {"name": "Yandex", "id": "yandex", "unicode": "f413", "styles": ["brands"], "search_terms": []}, {"name": "Yandex International", "id": "yandex-international", "unicode": "f414", "styles": ["brands"], "search_terms": []}, {"name": "Yarn", "id": "yarn", "unicode": "f7e3", "styles": ["brands"], "search_terms": []}, {"name": "Yelp", "id": "yelp", "unicode": "f1e9", "styles": ["brands"], "search_terms": []}, {"name": "Yen Sign", "id": "yen-sign", "unicode": "f157", "styles": ["solid"], "search_terms": ["currency", "jpy", "money"]}, {"name": "<PERSON>", "id": "yin-yang", "unicode": "f6ad", "styles": ["solid"], "search_terms": ["daoism", "opposites", "taoism"]}, {"name": "Yoast", "id": "yoast", "unicode": "f2b1", "styles": ["brands"], "search_terms": []}, {"name": "YouTube", "id": "youtube", "unicode": "f167", "styles": ["brands"], "search_terms": ["film", "video", "youtube-play", "youtube-square"]}, {"name": "YouTube Square", "id": "youtube-square", "unicode": "f431", "styles": ["brands"], "search_terms": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "zhihu", "unicode": "f63f", "styles": ["brands"], "search_terms": []}]