<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_fact_counter' );
function everest_block_fact_counter() {
Block::make( __( 'Xbees Fact Counter' ) )
	->add_fields( array(
        Field::make( 'text', 'title', __( 'Title' ) ),
        Field::make( 'text', 'subtext', __( 'Subtitle' ) ),
        Field::make( 'text', 'counter_num', __( 'Counter 1 Number' ) ),
        Field::make( 'text', 'counter_title', __( 'Counter 1 Title' ) ),
        Field::make( 'text', 'counter_num2', __( 'Counter 2 Number' ) ),
        Field::make( 'text', 'counter_title2', __( 'Counter 2 Title' ) ),
        Field::make( 'text', 'counter_num3', __( 'Counter 3 Number' ) ),
        Field::make( 'text', 'counter_title3', __( 'Counter 3 Title' ) ),
        Field::make( 'text', 'counter_num4', __( 'Counter 4 Number' ) ),
        Field::make( 'text', 'counter_title4', __( 'Counter 4 Title' ) ),
        ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $title = !empty($fields['title']) ? $fields['title'] : 'Most Interesting Facts';
        $subtext = !empty($fields['subtext']) ? $fields['subtext'] : 'To take a trivial example which of us ever undertakes laborious physical exercise.';

      ?>
<!--Start Fact Counter Area-->
<section class="fact-counter-area">
            <div class="container">
                <div class="sec-title text-center">
                    <h2><?php echo $title; ?></h2>
                    <div class="sub-title">
                        <p><?php echo $subtext; ?></p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-xl-12">
                        <ul class="fact-counter-box">
                            <!--Start Single Fact Counter-->
                            <li class="single-fact-counter">
                                <div class="title-holder">
                                    <div class="count-outer count-box">
                                        <span class="count-text" data-speed="3000" data-stop="<?php echo $fields['counter_num']; ?>">0</span>
                                    </div>
                                    <h3><?php echo $fields['counter_title']; ?></h3>
                                </div>
                                <div class="icon-holder">
                                    <span class="icon-online"><span class="path1"></span><span
                                            class="path2"></span><span class="path3"></span><span
                                            class="path4"></span><span class="path5"></span><span
                                            class="path6"></span><span class="path7"></span><span
                                            class="path8"></span><span class="path9"></span><span
                                            class="path10"></span><span class="path11"></span><span
                                            class="path12"></span><span class="path13"></span><span
                                            class="path14"></span><span class="path15"></span><span
                                            class="path16"></span><span class="path17"></span></span>
                                </div>
                            </li>
                            <!--End Single Fact Counter-->
                            <!--Start Single Fact Counter-->
                            <li class="single-fact-counter single-fact-counter--style2">
                                <div class="title-holder">
                                    <div class="count-outer count-box">
                                        <span class="count-text" data-speed="3000" data-stop="<?php echo $fields['counter_num2']; ?>">0</span>
                                    </div>
                                    <h3><?php echo $fields['counter_title2']; ?></h3>
                                </div>
                                <div class="icon-holder">
                                    <span class="icon-office-building"><span class="path1"></span><span
                                            class="path2"></span><span class="path3"></span><span
                                            class="path4"></span><span class="path5"></span><span
                                            class="path6"></span><span class="path7"></span><span
                                            class="path8"></span><span class="path9"></span><span
                                            class="path10"></span><span class="path11"></span><span
                                            class="path12"></span><span class="path13"></span><span
                                            class="path14"></span><span class="path15"></span><span
                                            class="path16"></span><span class="path17"></span><span
                                            class="path18"></span><span class="path19"></span><span
                                            class="path20"></span><span class="path21"></span><span
                                            class="path22"></span><span class="path23"></span><span
                                            class="path24"></span><span class="path25"></span><span
                                            class="path26"></span><span class="path27"></span><span
                                            class="path28"></span><span class="path29"></span><span
                                            class="path30"></span><span class="path31"></span><span
                                            class="path32"></span><span class="path33"></span><span
                                            class="path34"></span><span class="path35"></span><span
                                            class="path36"></span><span class="path37"></span><span
                                            class="path38"></span><span class="path39"></span><span
                                            class="path40"></span><span class="path41"></span><span
                                            class="path42"></span><span class="path43"></span><span
                                            class="path44"></span><span class="path45"></span><span
                                            class="path46"></span><span class="path47"></span><span
                                            class="path48"></span><span class="path49"></span><span
                                            class="path50"></span><span class="path51"></span><span
                                            class="path52"></span><span class="path53"></span><span
                                            class="path54"></span><span class="path55"></span><span
                                            class="path56"></span><span class="path57"></span><span
                                            class="path58"></span><span class="path59"></span><span
                                            class="path60"></span><span class="path61"></span><span
                                            class="path62"></span><span class="path63"></span></span>
                                </div>
                            </li>
                            <!--End Single Fact Counter-->
                        </ul>

                        <ul class="fact-counter-box bottom">
                            <!--Start Single Fact Counter-->
                            <li class="single-fact-counter pdt50 pdb0">
                                <div class="title-holder">
                                    <div class="count-outer count-box">
                                        <span class="count-text" data-speed="3000" data-stop="<?php echo $fields['counter_num3']; ?>">0</span>
                                    </div>
                                    <h3><?php echo $fields['counter_title3']; ?></h3>
                                </div>
                                <div class="icon-holder">
                                    <span class="icon-book"><span class="path1"></span><span class="path2"></span><span
                                            class="path3"></span><span class="path4"></span><span
                                            class="path5"></span><span class="path6"></span><span
                                            class="path7"></span><span class="path8"></span><span
                                            class="path9"></span><span class="path10"></span><span
                                            class="path11"></span><span class="path12"></span><span
                                            class="path13"></span><span class="path14"></span><span
                                            class="path15"></span><span class="path16"></span><span
                                            class="path17"></span><span class="path18"></span><span
                                            class="path19"></span><span class="path20"></span><span
                                            class="path21"></span><span class="path22"></span><span
                                            class="path23"></span><span class="path24"></span><span
                                            class="path25"></span><span class="path26"></span><span
                                            class="path27"></span><span class="path28"></span><span
                                            class="path29"></span><span class="path30"></span><span
                                            class="path31"></span><span class="path32"></span><span
                                            class="path33"></span><span class="path34"></span></span>
                                </div>
                            </li>
                            <!--End Single Fact Counter-->
                            <!--Start Single Fact Counter-->
                            <li class="single-fact-counter single-fact-counter--style2 pdt50 pdb0">
                                <div class="title-holder">
                                    <div class="count-outer count-box">
                                        <span class="count-text" data-speed="3000" data-stop="<?php echo $fields['counter_num4']; ?>">0</span>
                                    </div>
                                    <h3><?php echo $fields['counter_title4']; ?></h3>
                                </div>
                                <div class="icon-holder">
                                    <span class="icon-browser"><span class="path1"></span><span
                                            class="path2"></span><span class="path3"></span><span
                                            class="path4"></span><span class="path5"></span><span
                                            class="path6"></span><span class="path7"></span><span
                                            class="path8"></span><span class="path9"></span><span
                                            class="path10"></span><span class="path11"></span><span
                                            class="path12"></span><span class="path13"></span><span
                                            class="path14"></span><span class="path15"></span><span
                                            class="path16"></span><span class="path17"></span><span
                                            class="path18"></span><span class="path19"></span><span
                                            class="path20"></span><span class="path21"></span><span
                                            class="path22"></span><span class="path23"></span></span>
                                </div>
                            </li>
                            <!--End Single Fact Counter-->
                        </ul>

                    </div>
                </div>
            </div>
        </section>
        <!--End Fact Counter Area-->
<?php
	} );
}