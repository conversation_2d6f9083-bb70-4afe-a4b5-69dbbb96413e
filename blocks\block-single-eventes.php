<?php 

use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_single_evente' );
function everest_single_evente() {
Block::make( __( 'Xbees Single Evente' ) )
	->add_fields( array() )
    // ->set_mode( 'preview' ) 
	// ->set_inner_blocks( true )
    // ->set_inner_blocks_template( array(
	// 	array( 'carbon-fields/col' ),
	// 	array( 'carbon-fields/col' ),
	// ) )
	->set_render_callback( function ($field, $atts, $inner_blocks_content, $id) {
        if( get_post_type($id) === 'everest-eventes' ) { 
            $date = carbon_get_post_meta($id, 'date');
            $start_time = carbon_get_post_meta($id, 'start_time');
            $end_time = carbon_get_post_meta($id, 'end_time');
            $location = carbon_get_post_meta($id, 'location');
            $organizer = !empty(carbon_get_post_meta($id, 'organizer')) ? carbon_get_post_meta($id, 'organizer') : 'Everest';
            $phone = !empty(carbon_get_post_meta($id, 'phone')) ? carbon_get_post_meta($id, 'phone') : '+201200433432 +201019111669';
            $email = !empty(carbon_get_post_meta($id, 'email')) ? carbon_get_post_meta($id, 'email') : '<EMAIL>';
        
        ?>
        <!--Start Events Details Page-->
        <section class="events-details-page">
            <div class="container">
                <div class="row">
                    <div class="col-xl-8">
                        <div class="events-details-content">
                            <div class="events-details-content__text-box">
                                <?php echo get_the_content($id); ?>
                            </div>
                        </div>
                    </div>

                    <!--Start Single Event Three-->
                    <div class="col-xl-4 col-lg-6 col-md-6">
                        <div class="events-details-info-box">
                            <div class="inner-title">
                                <div class="dot-box"></div>
                                <h3>Event Details</h3>
                            </div>
                            <ul class="events-details-info-box__items">
                                <li>
                                    Date <span><?php echo date("j F, Y", strtotime($date)); ?></span>
                                </li>
                                <li>
                                    Time <span><?php echo $start_time; ?> - <?php echo $end_time; ?></span>
                                </li>
                                <?php if(!empty($location)) : ?>
                                <li>
                                    Location <span><?php echo $location; ?></span>
                                </li>
                                <?php endif; ?>
                                <li>
                                    Organizer <span><?php echo $organizer; ?></span>
                                </li>
                                <li>
                                    Phone <span><a href="#"><?php echo $phone; ?></a></span>
                                </li>
                                <li>
                                    Email <span><a href="mailto:<?php echo $email; ?>"><?php echo $email; ?></a></span>
                                </li>
                            </ul>
                            <div class="button-one">
                                <a href="#">Add to Google Calendar</a>
                            </div>
                            <div class="btns-box">
                                <a class="btn-one btn-one--style2" href="#">
                                    <span class="txt">Book Now</span>
                                </a>
                            </div>
                            <ul class="events-details-info-box__social-links">
                                <li><a href="#"><i class="icon-twitter-1"></i></a></li>
                                <li><a href="#"><i class="icon-facebook-app-symbol"></i></a></li>
                                <li><a href="#"><i class="icon-linkedin"></i></a></li>
                            </ul>
                        </div>
                    </div>
                    <!--End Single Event Three-->

                </div>
            </div>
        </section>
        <!--End Events Details Page-->
<?php }
	} );

}