<?php 
$terms_one = get_categories([
    'taxonomy'    => 'courses_category',
    'hide_empty'  => 0,
    'number'      => 1
    ] );

$terms_two = get_categories([
    'taxonomy'    => 'courses_category',
    'hide_empty'  => 0,
    'number'      => 2,
    'offset'      => 1
    ] );    
?>   
       
       <!--Start Learning Categories Area-->
        <section class="learning-categories-area">
            <div class="container">
                <div class="row">

                    <div class="col-xl-6">
                        <div class="learning-categories-content-box">
                            <div class="sec-title-style8">
                                <h2><?php echo $title; ?></h2>
                                <p><?php echo $subtext; ?></p>

                            </div>
                            <?php 
                                if( $terms_one ) {
                                    foreach ( $terms_one as $key => $term ) { 
                                    $description = !empty(get_term_field( 'description', $term->term_id )) ? get_term_field( 'description', $term->term_id ) : 'Enjoy a pleasure that has no annoying or one who avoids a pain that produces no.' ;           

                             ?>
                            <!--Start Single learning categories Item -->
                            <div class="single-learning-categories-item">
                                <div class="couning-box">01</div>
                                <div class="single-learning-categories-item__inner">
                                    <div class="icon" data-aos="fade-up">
                                        <span class="flaticon-memory"></span>
                                    </div>
                                    <div class="text">
                                        <h3><?php echo $term->name; ?></h3>
                                        <p>T<?php echo $description; ?></p>
                                        <a class="readmore" href="<?php echo get_term_link( $term->term_id, 'courses_category'); ?>"><span class="icon-right-arrow-1"></span></a>
                                    </div>
                                </div>
                            </div>
                            <!--End Single learning categories Item -->
                            <?php } } ?>
                        </div>
                    </div>

                    <div class="col-xl-6">
                        <div class="learning-categories-content-box-two">
                            <?php 
                             if( $terms_two ) {
                                     foreach ( $terms_two as $key => $term ) {  
                                        $description = !empty(get_term_field( 'description', $term->term_id )) ? get_term_field( 'description', $term->term_id ) : 'Enjoy a pleasure that has no annoying or one who avoids a pain that produces no.' ;           
                             ?>
                            <!--Start Single learning categories Item -->
                            <div class="single-learning-categories-item">
                                <div class="couning-box">02</div>
                                <div class="single-learning-categories-item__inner">
                                    <div class="icon" data-aos="fade-up">
                                        <span class="flaticon-atom"></span>
                                    </div>
                                    <div class="text">
                                        <h3><?php echo $term->name; ?></h3>
                                        <p><?php echo $description; ?></p>
                                        <a class="readmore" href="<?php echo get_term_link( $term->term_id, 'courses_category'); ?>"><span class="icon-right-arrow-1"></span></a>
                                    </div>

                                </div>
                            </div>
                            <!--End Single learning categories Item -->
                            <?php } } ?>
                        </div>
                    </div>

                </div>
            </div>
        </section>
        <!--End Learning Categories Area-->