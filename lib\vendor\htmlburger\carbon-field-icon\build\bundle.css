/* ------------------------------------------------------------ *\
	Field - icon
\* ------------------------------------------------------------ */
.cf-icon-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap; }

.cf-icon-preview {
  border: 1px dashed #ccc;
  margin-right: 10px;
  flex: 0 0 150px; }
  .cf-container-widget .cf-icon-preview {
    flex: 0 0 100px; }
  @media screen and (max-width: 768px) {
    .cf-icon-preview {
      flex: 1 0 auto;
      margin-right: 0;
      margin-bottom: 10px; } }

.cf-icon-preview__canvas {
  display: flex;
  border: 0;
  background: 0;
  padding: 15px;
  margin: auto;
  min-height: 100px;
  justify-content: center;
  align-items: center;
  text-align: center; }
  .cf-container-widget .cf-icon-preview__canvas {
    line-height: 20px;
    min-height: 60px; }

.cf-icon-preview__canvas i {
  font-size: 64px;
  line-height: 100px; }
  .cf-container-widget .cf-icon-preview__canvas i {
    line-height: 60px; }
  .cf-icon-preview__canvas i.dashicons-before:before {
    font-size: 64px;
    line-height: 100px;
    width: auto;
    height: auto; }
    .cf-container-widget .cf-icon-preview__canvas i.dashicons-before:before {
      line-height: 80px; }

.cf-icon-preview__canvas img {
  width: 64px; }

.cf-icon-preview__label {
  width: 100%;
  margin: 0; }

.cf-icon-switcher {
  flex: 1;
  position: relative; }

.cf-icon-switcher__options {
  display: none;
  position: absolute;
  left: 0;
  top: 35px;
  z-index: 10;
  width: 100%;
  max-height: 250px;
  overflow-y: auto;
  background: #fff;
  border-width: 0 1px 1px;
  border-style: solid;
  border-color: #e5e5e5;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.15); }
  .cf-icon-switcher__options--opened {
    display: block; }
  @media screen and (max-width: 768px) {
    .cf-icon-switcher__options {
      top: 42px; } }

.cf-icon-switcher__options-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 0; }

.cf-icon-switcher__options-list__item {
  display: block;
  border: 1px solid #eee;
  margin: 0;
  flex: 0 0 12.5%;
  word-break: break-word;
  box-sizing: border-box; }
  .cf-icon-switcher__options-list__item--no-results {
    padding: 8px;
    flex: 0 0 100%;
    text-align: center; }
  .cf-icon-switcher__options-list__item:hover, .cf-icon-switcher__options-list__item--selected {
    background: #eee; }
  .cf-container-user-meta .cf-icon-switcher__options-list__item,
  .cf-container-term-meta .cf-icon-switcher__options-list__item {
    flex: 0 0 25%; }
  .cf-container-widget .cf-icon-switcher__options-list__item {
    flex: 0 0 33.33333%; }
  @media screen and (max-width: 1400px) {
    .cf-icon-switcher__options-list__item {
      flex: 0 0 25%; } }

.cf-icon-switcher__options-list button {
  border: 0;
  background: 0;
  width: 100%;
  height: 100%;
  padding: 10px 0;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; }

.cf-icon-switcher__options-list i,
.cf-icon-switcher__options-list img {
  width: 24px;
  margin-bottom: 10px; }

.cf-icon-switcher__options-list i {
  color: #73777c; }
  .cf-icon-switcher__options-list i:before {
    font-size: 20px; }

.cf-icon-search {
  position: relative;
  width: 100%;
  border: 0;
  border-spacing: 0;
  margin: 0;
  z-index: 1;
  padding: 5px 0;
  border: 1px solid #ddd;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
  display: flex;
  flex-direction: row; }

.cf-icon-search:before {
  position: absolute;
  left: 8px;
  top: 50%;
  margin-top: -10px;
  color: #73777c;
  pointer-events: none; }

.cf-icon-search__input {
  flex: 1 0 auto;
  margin: 0;
  margin-left: 32px;
  font-size: 13px;
  line-height: 18px;
  border-radius: 0;
  border: 0 !important;
  box-shadow: none !important;
  width: auto !important; }

.cf-icon-search__clear {
  margin-right: 5px !important;
  margin-bottom: 0 !important; }

