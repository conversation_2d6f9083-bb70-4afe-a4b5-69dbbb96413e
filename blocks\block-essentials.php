<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_essentials' );
function everest_block_essentials() {
Block::make( __( 'Xbees Essentials' ) )
	->add_fields( array(
        // Field::make( 'image', 'boxbg', __( 'Box 1 Image' ) )
        // ->set_value_type( 'url' ),
        // Field::make( 'text', 'box_title', __( 'Box 1 Title' ) ),
        // Field::make( 'text', 'box_text', __( 'Box 1 desc' ) ),

        // Field::make( 'image', 'boxbg2', __( 'Box 2 Image' ) )
        // ->set_value_type( 'url' ),
        // Field::make( 'text', 'box2_title', __( 'Box 2 Title' ) ),
        // Field::make( 'text', 'box2_text', __( 'Box 2 desc' ) ),

        Field::make( 'text', 'services_title', __( 'Services 1 Title' ) ),
        Field::make( 'text', 'services_icon', __( 'Services 1 Icon' ) ),
        Field::make( 'text', 'services_url', __( 'Services 1 URL' ) ),

        Field::make( 'text', 'services_title2', __( 'Services 2 Title' ) ),
        Field::make( 'text', 'services_icon2', __( 'Services 2 Icon' ) ),
        Field::make( 'text', 'services_url2', __( 'Services 2 URL' ) ),

        Field::make( 'text', 'services_title3', __( 'Services 3 Title' ) ),
        Field::make( 'text', 'services_icon3', __( 'Services 3 Icon' ) ),
        Field::make( 'text', 'services_url3', __( 'Services 3 URL' ) ),

        Field::make( 'text', 'services_title4', __( 'Services 4 Title' ) ),
        Field::make( 'text', 'services_icon4', __( 'Services 4 Icon' ) ),
        Field::make( 'text', 'services_url4', __( 'Services 4 URL' ) ),

        Field::make( 'text', 'services_title5', __( 'Services 5 Title' ) ),
        Field::make( 'text', 'services_icon5', __( 'Services 5 Icon' ) ),
        Field::make( 'text', 'services_url5', __( 'Services 5 URL' ) ),


        ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $box_title = !empty($fields['box_title']) ? $fields['box_title'] : 'Our Vision';
        $box_text = !empty($fields['box_text']) ? $fields['box_text'] : 'Everest Business Consulting envisions becoming a global leader in education';
        $box2_title = !empty($fields['box2_title']) ? $fields['box2_title'] : 'Our Mission';
        $box2_text = !empty($fields['box2_text']) ? $fields['box2_text'] : 'To be the catalyst for professional and organizational growth by offering tailored';
        $boxbg = !empty($fields['boxbg']) ? $fields['boxbg'] : get_stylesheet_directory_uri() . '/assets/img/logo.png';
        $boxbg2 = !empty($fields['boxbg2']) ? $fields['boxbg2'] : get_stylesheet_directory_uri() . '/assets/img/8-3.png';
		
        $services_title = !empty($fields['services_title']) ? $fields['services_title'] : 'Customized Corporate Training';
		$services_icon = !empty($fields['services_icon']) ? $fields['services_icon'] : 'fa fa-user-plus';
        $services_url = !empty($fields['services_url']) ? $fields['services_url'] : '#';
       
        $services_title2 = !empty($fields['services_title2']) ? $fields['services_title2'] : 'Start-ups Journey Workshops';
		$services_icon2 = !empty($fields['services_icon2']) ? $fields['services_icon2'] : 'fa fa-line-chart';
        $services_url2 = !empty($fields['services_url2']) ? $fields['services_url2'] : '#';

        $services_title3 = !empty($fields['services_title3']) ? $fields['services_title3'] : 'SMEs Resilience Workshops';
		$services_icon3 = !empty($fields['services_icon3']) ? $fields['services_icon3'] : 'fa fa-university';
        $services_url3 = !empty($fields['services_url3']) ? $fields['services_url3'] : '#';

        $services_title4 = !empty($fields['services_title4']) ? $fields['services_title4'] : 'Executive Programs';
		$services_icon4 = !empty($fields['services_icon4']) ? $fields['services_icon4'] : 'fa fa-tachometer';
        $services_url4 = !empty($fields['services_url4']) ? $fields['services_url4'] : '#';

        $services_title5 = !empty($fields['services_title5']) ? $fields['services_title5'] : 'Business Planning and Mentorship';
		$services_icon5 = !empty($fields['services_icon5']) ? $fields['services_icon5'] : 'fa fa-ticket';
        $services_url5 = !empty($fields['services_url5']) ? $fields['services_url5'] : '#';
        
       ?>
<!--Start Essentials Area-->
<section class="essentials-area">
    <div class="auto-container">
        <div class="row">
            <div class="col-xl-12">
                <div class="essentials-content-box">
                    <ul>
                        <li>
                            <div class="single-essentials-box marginbottom text-center wow fadeInRight"
                            data-wow-duration="1s" data-wow-delay="0.1s">
                                <a href="<?php echo $services_url; ?>">
                                    <div class="icon">
                                        <span class="<?php echo $services_icon; ?>"></span>
                                        <div class="round-box"></div>
                                    </div>
                                    <h3> <?php echo $services_title; ?></h3>
                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="single-essentials-box marginbottom text-center wow fadeInRight"
                            data-wow-duration="1s" data-wow-delay="0.1s">
                            <a href="<?php echo $services_url2; ?>">
                                <div class="icon">
                                    <span class="<?php echo $services_icon2; ?>"></span>
                                    <div class="round-box"></div>
                                </div>
                                <h3><?php echo $services_title2; ?></h3>
                            </a>
                            </div>
                        </li>
                        <li>
                            <div class="single-essentials-box marginbottom text-center wow fadeInRight"
                            data-wow-duration="1s" data-wow-delay="0.1s">
                            <a href="<?php echo $services_url3; ?>">
                                <div class="icon">
                                    <span class="<?php echo $services_icon3; ?>"></span>
                                    <div class="round-box"></div>
                                </div>
                                <h3><?php echo $services_title3; ?></h3>
                            </a>
                            </div>
                        </li>
                        <li>
                            <div class="single-essentials-box marginbottom text-center wow fadeInRight"
                            data-wow-duration="1s" data-wow-delay="0.1s">
                            <a href="<?php echo $services_url4; ?>">
                                <div class="icon">
                                    <span class="<?php echo $services_icon4; ?>"></span>
                                    <div class="round-box"></div>
                                </div>
                                <h3><?php echo $services_title4; ?></h3>
                            </a>
                            </div>
                        </li>
                        <li>
                            <div class="single-essentials-box marginbottom text-center wow fadeInRight"
                            data-wow-duration="1s" data-wow-delay="0.1s">
                            <a href="<?php echo $services_url5; ?>">
                                <div class="icon">
                                    <span class="<?php echo $services_icon5; ?>"></span>
                                    <div class="round-box"></div>
                                </div>
                                <h3><?php echo $services_title5; ?></h3>
                            </a>    
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

        </div>
    </div>
</section>
<!--End Essentials Area-->
<?php
	} );
}