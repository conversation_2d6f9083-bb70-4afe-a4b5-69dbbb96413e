/* ==========================================================================
   Container
   ========================================================================== */

.carbon-box {
	&.hide-if-js:not([hidden]) {
		display: block;
	}
}

#poststuff .carbon-box .inside,
.carbon-box .inside {
	padding: 0;
	margin: 0;
}

.cf-container {
	&--plain {
		display: block;
	}

	&--tabbed {
		display: flex;

		&-horizontal {
			flex-direction: column;
		}

		&-vertical {
			flex-direction: row;
		}
	}
}

.cf-container__fields {
	display: flex;
	flex-wrap: wrap;
	flex: 1;
	margin: 0 -1px 0 0;
	background-color: $color-white;

	&[hidden] {
		display: none;
	}

	.block-editor & {
		border-left: 1px solid $wp-color-gray-light-500;
	}

	.cf-container-term-meta &,
	.cf-container-user-meta & {
		border-width: 0 0 1px 1px;
		border-style: solid;
		border-color: $wp-color-gray-light-500;
		margin: 0;
	}
}

.cf-container__tabs {
	position: relative;
	z-index: 1;
	background-color: $wp-color-gray-light-100;

	&-list {
		display: flex;
		flex-wrap: wrap;
		margin: 0 0 -1px;

		.cf-container__tabs-item {
			display: flex;
			align-items: center;
			border: 1px solid $wp-color-gray-light-500;
			margin: 0 ($size-base * 2) 0 0;
			background-color: $wp-color-gray-light-100;
			font-size: 13px;
			cursor: pointer;
			transition: background-color $transition-base, border-color $transition-base;

			button {
				background: 0;
				border: 0;
				padding: ($size-base * 2.5) ($size-base * 3);
				margin: 0;
				flex: 1;
				cursor: pointer;
				display: flex;
				align-items: center;

				span {
					margin-right: 5px;
				}
			}

			&:hover {
				background-color: $color-white;
			}

			&--current {
				background-color: $color-white;
				border-bottom-color: $color-white;
			}
		}

	}

	&--tabbed-horizontal {
		padding: ($size-base * 3) ($size-base * 3) 0;
		border-bottom: 1px solid $wp-color-gray-light-500;

		.cf-container__tabs-list {
			display: flex;
			flex-direction: row;
		}
	}

	&--tabbed-vertical {
		width: 300px;
		border-right: 1px solid $wp-color-gray-light-500;

		.cf-container__tabs-list {
			display: flex;
			flex-direction: column;

			.cf-container__tabs-item {
				margin: 0;
				justify-content: flex-start;
				border: 0;
				border-top: 1px solid $gb-light-gray-500;
				border-bottom: 1px solid $gb-light-gray-500;

				&:first-of-type {
					border-top: 0;
				}

				button {
					text-align: left;
					font-weight: 500;
				}
			}

			.cf-container__tabs-item + .cf-container__tabs-item {
				border-top: 0;
			}
		}
	}
}
