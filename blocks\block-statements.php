<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_statements' );
function everest_block_statements() {
Block::make( __( 'Xbees Statements' ) )
	->add_fields( array(
        // Field::make( 'text', 'title', __( 'Title' ) ),
        Field::make( 'checkbox', 'dis_order', 'Disable Order on mobile' )
                ->set_option_value( 'yes' ),
            Field::make( 'complex', 'about', __( 'Statements Options' ) )
            ->add_fields( array(
                Field::make( 'text', 'title', __( 'Title' ) ),
                Field::make( 'text', 'subtitle', __( 'Subtitle' ) ),
                Field::make( 'rich_text', 'subtext', __( 'Content' ) ),
                Field::make( 'select', 'image_pos', __( 'Image Style' ) )
                    ->set_options( array(
                        'left' => __( 'Image Left' ),
                        'right' => __( 'Image Right' ),
                    ) )
                    ->set_default_value( 'left' ),
                Field::make( 'image', 'image', __( 'Brand Image' ) )
                    ->set_value_type( 'url' ),
                Field::make( 'text', 'btn_title', __( 'Button Title' ) ),
                Field::make( 'text', 'btn_url', __( 'Button Url' ) ),
                Field::make( 'text', 'btn_title_2', __( 'Button Title 2' ) ),
                Field::make( 'text', 'btn_url_2', __( 'Button Url 2' ) ),
                Field::make( 'text', 'btn_width', __( 'Button Width (px)' ) )
                    ->set_attribute( 'min', '0' )
                    ->set_attribute( 'type', 'number' ),
                Field::make( 'text', 'btn_font', __( 'Button Font Size (px)' ) )
                    ->set_attribute( 'min', '0' )
                    ->set_attribute( 'type', 'number' ),        
                Field::make( 'select', 'btn_align', __( 'Button Alignment' ) )
                    ->set_options( array(
                        'left' => __( 'Left' ),
                        'center' => __( 'Center' ),
                        'right' => __( 'Right' ),
                    ) )
                    ->set_default_value( 'left' ),
                
            ) )
    ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
        $order_1 = ' order-xl-1 order-sm-2';
        $order_2 = ' order-xl-2 order-sm-1';
        if( isset($fields['dis_order']) &&  $fields['dis_order'] ) {
            $order_1 = $order_2 = '';
        }
      ?>
      <section class="who-we-are-style6-area">
            <div class="container">
                <?php if( isset($fields['about']) ) : ?>
                    <?php foreach ( $fields['about'] as $key => $value) { 
                        $img_pos = isset($value['image_pos']) ? $value['image_pos'] : 'left';
                        $btn_title = isset($value['btn_title']) ? $value['btn_title'] : '';
                        $btn_url = isset($value['btn_url']) ? $value['btn_url'] : '#';
                        $btn_title_2 = $value['btn_title_2'] ?? '';
                        $btn_url_2 = $value['btn_url_2'] ?? '#';
                        $btn_width = isset($value['btn_width']) ? $value['btn_width'] . 'px' : 'auto';
                        $btn_font = isset($value['btn_font']) ? $value['btn_font'] . 'px' : '17px';
                        $btn_align = $value['btn_align'] ?? 'left';
                        
                        ?>
                    <div class="row align-items-center mb-5 pb-5">
                        <?php if( $img_pos === 'left' ) { ?>
                            <div class="col-xl-6<?php echo $order_1; ?>">
                                <div class="who-we-are-style6-img-box">
                                    <div class="inner">
                                        <img src="<?php echo $value['image']; ?>" alt="">
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-6<?php echo $order_2; ?>">
                                <div class="who-we-are-style6-content-box">
                                    <div class="sec-title-style6">
                                        <div class="sub-title">
                                            <p><?php echo $value['subtitle']; ?></p>
                                        </div>
                                        <h2><?php echo $value['title']; ?></h2>
                                    </div>
                                    <div class="top-text pb-4">
                                        <?php echo apply_filters( 'the_content', $value['subtext'] ); ?>
                                    </div>
                                    <div class="btn--group" style="text-align:<?php echo $btn_align; ?>;">
                                        <?php if( !empty( $btn_title ) ) : ?>
                                        <a class="btn-one" href="<?php echo $btn_url; ?>" style="width:<?php echo $btn_width; ?>;font-size:<?php echo $btn_font; ?>;">
                                            <span class="txt"><?php echo $btn_title; ?></span>
                                        </a>
                                        <?php endif; ?>
                                        <?php if( !empty( $btn_title_2 ) ) : ?>
                                        <a class="btn-one" href="<?php echo $btn_url_2; ?>" style="width:<?php echo $btn_width; ?>;font-size:<?php echo $btn_font; ?>;">
                                            <span class="txt"><?php echo $btn_title_2; ?></span>
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php } else { ?>
                            <div class="col-xl-6">
                                <div class="who-we-are-style6-content-box">
                                    <div class="sec-title-style6">
                                        <div class="sub-title">
                                            <p><?php echo $value['subtitle']; ?></p>
                                        </div>
                                        <h2><?php echo $value['title']; ?></h2>
                                    </div>
                                    <div class="top-text pb-4">
                                        <?php echo apply_filters( 'the_content', $value['subtext'] ); ?>
                                    </div>
                                    <div class="btn--group" style="text-align:<?php echo $btn_align; ?>;">
                                        <?php if( !empty( $btn_title ) ) : ?>
                                        <a class="btn-one" href="<?php echo $btn_url; ?>" style="width:<?php echo $btn_width; ?>;font-size:<?php echo $btn_font; ?>;">
                                            <span class="txt"><?php echo $btn_title; ?></span>
                                        </a>
                                        <?php endif; ?>
                                        <?php if( !empty( $btn_title_2 ) ) : ?>
                                        <a class="btn-one" href="<?php echo $btn_url_2; ?>" style="width:<?php echo $btn_width; ?>;font-size:<?php echo $btn_font; ?>;">
                                            <span class="txt"><?php echo $btn_title_2; ?></span>
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="who-we-are-style6-img-box">
                                    <div class="inner">
                                        <img src="<?php echo $value['image']; ?>" alt="">
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                    <?php } ?>
                    <?php endif; ?>
            </div>
     </section>
<?php
	} );
}