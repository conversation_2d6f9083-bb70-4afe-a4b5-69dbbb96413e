<?php 
use Carbon_Fields\Block;
use Carbon_Fields\Field;


add_action( 'carbon_fields_register_fields', 'everest_block_why_us' );
function everest_block_why_us() {
Block::make( __( 'Xbees Why Us' ) )
	->add_tab( 'Left Side', array(
        Field::make( 'text', 'title', __( 'box 1 Title' ) ),
        Field::make( 'text', 'subtext', __( 'box 1 Subtitle' ) ),
        Field::make( 'image', 'image', __( 'box 1 Background Image' ) )
        ->set_value_type( 'url' ),

        Field::make( 'text', 'title2', __( 'box 2 Title' ) ),
        Field::make( 'text', 'subtext2', __( 'box 2 Subtitle' ) ),
        Field::make( 'image', 'image2', __( 'box 2 Background Image' ) )
        ->set_value_type( 'url' ),
    ) )
    ->add_tab( 'Right Side', array(
        Field::make( 'text', 'r_title', __( 'Title' ) ),
        Field::make( 'text', 'r_subtext', __( 'Subtitle' ) ),
   
        Field::make( 'text', 'r_title1', __( 'List 1 Title' ) ),
        Field::make( 'text', 'r_subtext1', __( 'List 1 subTitle' ) ),

        Field::make( 'text', 'r_title2', __( 'List 2 Title' ) ),
        Field::make( 'text', 'r_subtext2', __( 'List 2 subTitle' ) ),

        Field::make( 'text', 'r_title3', __( 'List 3 Title' ) ),
        Field::make( 'text', 'r_subtext3', __( 'List 3 subTitle' ) ),

    ) )
    ->set_category( 'xbees-category', __( 'Xbees Category' ), 'xbees' )
    ->set_icon( 'no' )
    ->set_mode( 'preview' )  
    // ->set_inner_blocks( true )
	// ->set_inner_blocks_position( 'below' )
	->set_render_callback( function ( $fields, $attributes, $inner_blocks, $post_id, $id ) {
          
        $title = !empty($fields['title']) ? $fields['title'] : 'Users Around the World';
        $subtext = !empty($fields['subtext']) ? $fields['subtext'] : '26';
        $image = !empty($fields['image']) ? $fields['image'] : 'https://st.ourhtmldemo.com/new/educamb/assets/images/quiz-learning/quiz-learning-choose-img-1.jpg';

       
        $title2 = !empty($fields['title2']) ? $fields['title2'] : 'Engagement Platform';
        $subtext2 = !empty($fields['subtext2']) ? $fields['subtext2'] : '100';
        $image2 = !empty($fields['image2']) ? $fields['image2'] : 'https://st.ourhtmldemo.com/new/educamb/assets/images/quiz-learning/quiz-learning-choose-img-2.jpg';

        $r_title = !empty($fields['r_title']) ? $fields['r_title'] : 'The Reasons for <span>Why We</span>';
        $r_subtext = !empty($fields['r_subtext']) ? $fields['r_subtext'] : 'Denouncing pleasure and praising pain was born you a complete.';
     
        $r_title1 = !empty($fields['r_title1']) ? $fields['r_title1'] : 'Certified Educators';
        $r_subtext1 = !empty($fields['r_subtext1']) ? $fields['r_subtext1'] : 'Beguiled and demoralized by all charms of pleasure to the moments so to be welcomed.';
     
        $r_title2 = !empty($fields['r_title2']) ? $fields['r_title2'] : 'Affordable Price';
        $r_subtext2 = !empty($fields['r_subtext2']) ? $fields['r_subtext2'] : 'Owings to the claims of duty the obligations off business it will frequently advantage.';
     
        $r_title3 = !empty($fields['r_title3']) ? $fields['r_title3'] : 'Lifetime Support';
        $r_subtext3 = !empty($fields['r_subtext3']) ? $fields['r_subtext3'] : 'The wise man therefore always holds in these matters to this principle of right to find fault.';
     ?>
<!--Start Quiz Learning Choose Area-->
<section class="quiz-learning-choose-area">
    <div class="quiz-learning-choose-area-shape1 wow slideInLeft" data-wow-delay="100ms" data-wow-duration="2500ms">
        <img src="https://st.ourhtmldemo.com/new/educamb/assets/images/quiz-learning/shape/quiz-learning-choose-area-shape-1.png" alt="">
    </div>
    <div class="container">
        <div class="row">

            <div class="col-xl-6">
                <div class="quiz-learning-choose-img-box">
                    <div class="quiz-learning-choose-area-shape2">
                        <img class="rotate-me"
                            src="https://st.ourhtmldemo.com/new/educamb/assets/images/quiz-learning/shape/quiz-learning-choose-area-shape-2.png" alt="">
                    </div>
                    <div class="quiz-learning-choose-area-shape3">
                        <img class="float-bob-x"
                            src="https://st.ourhtmldemo.com/new/educamb/assets/images/quiz-learning/shape/quiz-learning-choose-area-shape-3.png" alt="">
                    </div>
                    <div class="row">
                        <!--Start Single Quiz Learning Choose Box -->
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <div class="single-quiz-learning-choose-box">
                                <div class="img-box">
                                    <img src="<?php echo $image; ?>" alt="">
                                </div>
                                <div class="text-box pdt60 text-right">
                                    <div class="icon">
                                        <span class="flaticon-student-1"></span>
                                    </div>
                                    <div class="count-outer count-box">
                                        <span class="count-text" data-speed="3000" data-stop="<?php echo $subtext; ?>">0</span>k
                                    </div>
                                    <h3><?php echo $title; ?></h3>
                                </div>
                            </div>
                        </div>
                        <!--End Single Quiz Learning Choose Box -->
                        <!--Start Single Quiz Learning Choose Box -->
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <div class="single-quiz-learning-choose-box style2">
                                <div class="text-box pdb60 text-left">
                                    <div class="icon">
                                        <span class="flaticon-exam"></span>
                                    </div>
                                    <div class="count-outer count-box">
                                        <span class="count-text" data-speed="3000" data-stop="<?php echo $subtext2; ?>">0</span>%
                                    </div>
                                    <h3><?php echo $title2; ?></h3>
                                </div>
                                <div class="img-box">
                                    <img src="<?php echo $image2; ?>" alt="">
                                </div>
                            </div>
                        </div>
                        <!--End Single Quiz Learning Choose Box -->
                    </div>
                </div>
            </div>

            <div class="col-xl-6">
                <div class="quiz-learning-choose-content-box">
                    <div class="sec-title-style8">
                        <h2><?php echo $r_title; ?></h2>
                        <p><?php echo $r_subtext; ?></p>
                    </div>
                    <ul class="quiz-learning-choose-content-box-items">
                        <li class="single-quiz-learning-choose-content-box-item">
                            <div class="icon">
                                <span class="flaticon-contract"></span>
                            </div>
                            <div class="text">
                                <h2>01.</h2>
                                <h3><?php echo $r_title1; ?></h3>
                                <p><?php echo $r_subtext1; ?></p>
                            </div>
                        </li>
                        <li class="single-quiz-learning-choose-content-box-item">
                            <div class="icon">
                                <span class="flaticon-dollar"></span>
                            </div>
                            <div class="text">
                                <h2>02.</h2>
                                <h3><?php echo $r_title2; ?></h3>
                                <p><?php echo $r_subtext2; ?></p>
                            </div>
                        </li>
                        <li class="single-quiz-learning-choose-content-box-item">
                            <div class="icon">
                                <span class="flaticon-infinity"></span>
                            </div>
                            <div class="text">
                                <h2>03.</h2>
                                <h3><?php echo $r_title3; ?></h3>
                                <p><?php echo $r_subtext3; ?></p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

        </div>
    </div>
</section>
<!--End Quiz Learning Choose Area-->
<?php
	} );
}