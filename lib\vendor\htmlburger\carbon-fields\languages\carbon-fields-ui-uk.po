msgid ""
msgstr ""
"Content-Type: text/plain; charset=UTF-8\n"
"X-Generator: Poedit 3.0\n"
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Transfer-Encoding: 8bit\n"
"Last-Translator: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Language: uk\n"

#: packages/blocks/components/block-edit/index.js:214
msgid "Show preview"
msgstr "Показати превью"

#: packages/blocks/components/block-edit/index.js:215
msgid "Hide preview"
msgstr "Приховати превью"

#: packages/blocks/components/block-edit/index.js:286
msgid "Fields"
msgstr "Поля"

#: packages/blocks/components/not-supported-field/index.js:14
msgid "Field of type '%s' is not supported in Gutenberg."
msgstr "Поле типу '%s' не підтримується в Gutenberg."

#: packages/blocks/components/server-side-render/index.js:129
msgid "Error loading block: %s"
msgstr "Помилка завантаження блоку: "

#: packages/blocks/components/server-side-render/index.js:135
msgid "No results found."
msgstr "Нічого не знайдено."

#: packages/blocks/fields/datetime/index.js:59
#: packages/core/fields/date/index.js:15
#: packages/metaboxes/fields/datetime/index.js:9
msgid "Select Date"
msgstr "Оберіть дату"

#: packages/blocks/fields/file/index.js:16
#: packages/metaboxes/fields/file/index.js:11
msgid "Use File"
msgstr "Використовувати файл"

#: packages/blocks/fields/file/index.js:17
#: packages/metaboxes/fields/file/index.js:12
msgid "Select File"
msgstr "Виберіть файл"

#: packages/blocks/fields/image/index.js:11
#: packages/metaboxes/fields/image/index.js:11
msgid "Use Image"
msgstr "Використовувати зображення"

#: packages/blocks/fields/image/index.js:12
#: packages/metaboxes/fields/image/index.js:12
msgid "Select Image"
msgstr "Виберіть зображення"

#: packages/blocks/fields/media-gallery/index.js:16
#: packages/metaboxes/fields/media-gallery/index.js:16
msgid "Use Attachments"
msgstr "Використовувати вкладені файли"

#: packages/blocks/fields/media-gallery/index.js:17
#: packages/metaboxes/fields/media-gallery/index.js:17
msgid "Select Attachments"
msgstr "Виберіть вкладені файли"

#: packages/core/components/no-options/index.js:14
msgid "No options."
msgstr "Нема опцій."

#: packages/core/components/search-input/index.js:30
msgid "Search..."
msgstr "Пошук..."

#: packages/core/fields/association/index.js:169
msgid "Maximum number of items reached (%s items)"
msgstr "Досягнуто максимальної кількості елементів (%s елементів)"

#: packages/core/fields/association/index.js:269
msgid "Showing %1$d of %2$d results"
msgstr "Показано %1$d з %2$d результатів"

#: packages/core/fields/association/index.js:283
msgid "Thumbnail"
msgstr "Мініатюра"

#: packages/core/fields/association/index.js:305
msgid "Edit"
msgstr "Редагувати"

#: packages/core/fields/association/index.js:313
msgid "Add"
msgstr "Додати"

#: packages/core/fields/association/index.js:365
#: packages/core/fields/complex/group.js:163
msgid "Remove"
msgstr "Видалити"

#: packages/core/fields/association/index.js:466
msgid "An error occurred while trying to fetch association options."
msgstr "Під час спроби отримати параметри асоціації сталася помилка."

#: packages/core/fields/association/index.js:519
#: packages/core/fields/complex/index.js:428
#: packages/core/hocs/with-validation/required.js:20
msgid "This field is required."
msgstr "Це поле є обов'язковим."

#: packages/core/fields/association/index.js:523
msgid "Minimum number of items not reached (%s items)"
msgstr "Мінімальна кількість елементів не досягнута (%s елементів)"

#: packages/core/fields/color/index.js:106
msgid "Clear"
msgstr "Очистити"

#: packages/core/fields/color/index.js:92
msgid "Select a color"
msgstr "Виберіть колір"

#: packages/core/fields/complex/group.js:154
msgid "Duplicate"
msgstr "Дублювати"

#: packages/core/fields/complex/group.js:172
msgid "Collapse"
msgstr "Згорнути"

#: packages/core/fields/complex/index.js:146
msgid "Couldn't create the label of group - %s"
msgstr "Не вдалося створити мітку групи - %s"

#: packages/core/fields/complex/index.js:344
msgid "There are no entries yet."
msgstr "Записів ще немає."

#: packages/core/fields/complex/index.js:401
msgid "Expand All"
msgstr "Розгорнути все"

#: packages/core/fields/complex/index.js:401
msgid "Collapse All"
msgstr "Згорнути все"

#: packages/core/fields/complex/index.js:435
msgid "Minimum number of rows not reached (%1$d %2$s)"
msgstr "Не досягнуто мінімальної кількості рядків (%1$d %2$s)"

#: packages/core/fields/complex/index.js:82
msgid "Add %s"
msgstr "Додати %s"

#: packages/core/fields/map/index.js:165
msgid "The address could not be found."
msgstr "Адресу не вдалося знайти."

#: packages/core/fields/map/index.js:167
msgid "Geocode was not successful for the following reason: "
msgstr "Геокодування не вдалося з наступної причини:"

#: packages/core/fields/map/index.js:185
msgid "Error alert"
msgstr "Сповіщення про помилку"

#: packages/core/fields/oembed/index.js:187
msgid "An error occurred while trying to fetch oembed preview."
msgstr "Під час спроби отримати попередній перегляд oembed сталася помилка."

#: packages/core/fields/oembed/index.js:202
msgid "Not Found"
msgstr "Не знайдено"

#: packages/core/fields/rich-text/index.js:123
msgid "Visual"
msgstr "Візуальний"

#: packages/core/fields/rich-text/index.js:127
msgid "Text"
msgstr "Текст"

#: packages/core/fields/sidebar/index.js:110
msgid "Please enter the name of the new sidebar:"
msgstr "Будь ласка, введіть назву нової бічної панелі:"

#: packages/core/fields/sidebar/index.js:128
msgid "An error occurred while trying to create the sidebar."
msgstr "Під час спроби створити бічну панель сталася помилка."

#: packages/core/fields/sidebar/index.js:58
msgid "Please choose"
msgstr "Будь-ласка оберіть"

#: packages/core/fields/time/index.js:16
msgid "Select Time"
msgstr "Виберіть час"

#: packages/core/hocs/with-conditional-logic/index.js:69
msgid "An unknown field is used in condition - \"%s\""
msgstr "В умові використовується невідоме поле - '%s'"

#: packages/core/registry/index.js:40
msgid "%1$s type must be a string."
msgstr "Тип %1$s має бути рядком."

#: packages/core/registry/index.js:46
msgid "%1$s %2$s is already registered."
msgstr "%1$s %2$s уже зареєстровано."

#: packages/core/registry/index.js:54
msgid "The \"component\" param must be a function."
msgstr "Параметр \"component\" має бути функцією."

#: packages/core/registry/index.js:79
msgid "The provided context isn't a valid one. Must be one of - %s ."
msgstr "Наданий контекст не є дійсним. Має бути один із - %s ."

#: packages/core/registry/index.js:89
msgid "%s %s isn't registered."
msgstr "%s %s не зареєстровано."

#: packages/core/utils/api-fetch.js:19
msgid "An error occured."
msgstr "Сталася помилка."

#: packages/core/utils/fetch-attachments-data.js:24
msgid "An error occurred while trying to fetch files data."
msgstr "Під час спроби отримати дані файлів сталася помилка."

#: packages/metaboxes/containers/index.js:55
msgid "Could not find DOM element for container \"%1$s\"."
msgstr "Не вдалося знайти елемент DOM для контейнера \"%1$s\"."

#: packages/metaboxes/monitors/conditional-display/conditions/base.js:52
msgid "Unsupported container condition comparison operator used - \"%1$s\"."
msgstr "Використано непідтримуваний оператор порівняння умов контейнера - \"%1$s\"."

#: packages/metaboxes/monitors/conditional-display/handler/index.js:55
msgid "Unsupported container condition - \"%1$s\"."
msgstr "Непідтримуваний стан контейнера - \"%1$s\"."

#: packages/metaboxes/monitors/conditional-display/handler/index.js:73
msgid "Unsupported container condition relation used - \"%1$s\"."
msgstr "Використано непідтримуване відношення умов контейнера - \"%1$s\"."
