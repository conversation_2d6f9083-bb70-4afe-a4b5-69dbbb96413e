<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => NULL,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => NULL,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'htmlburger/carbon-field-icon' => array(
            'pretty_version' => 'v3.1.0',
            'version' => '3.1.0.0',
            'reference' => 'fe0341462e8c8681bb7ff20380974022578e15b0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../htmlburger/carbon-field-icon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'htmlburger/carbon-fields' => array(
            'pretty_version' => 'v3.6.3',
            'version' => '3.6.3.0',
            'reference' => 'd913a5148cb9dc61ed239719c747f4ebb513003f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../htmlburger/carbon-fields',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
