msgid ""
msgstr ""
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: bg_BG\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.4.1\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: packages/blocks/components/block-edit/index.js:214
msgid "Show preview"
msgstr "Покажи прегледа"

#: packages/blocks/components/block-edit/index.js:215
msgid "Hide preview"
msgstr "Скрий прегледа"

#: packages/blocks/components/block-edit/index.js:286
msgid "Fields"
msgstr "Полета"

#: packages/blocks/components/not-supported-field/index.js:14
msgid "Field of type '%s' is not supported in Gutenberg."
msgstr "Поле от тип ‘%s’ не се поддържа в Gutenberg."

#: packages/blocks/components/server-side-render/index.js:129
msgid "Error loading block: %s"
msgstr ""

#: packages/blocks/components/server-side-render/index.js:135
#, fuzzy
#| msgid "Not Found"
msgid "No results found."
msgstr "Няма резултат"

#: packages/blocks/fields/datetime/index.js:59
#: packages/core/fields/date/index.js:15
#: packages/metaboxes/fields/datetime/index.js:9
msgid "Select Date"
msgstr "Избери дата"

#: packages/blocks/fields/file/index.js:16
#: packages/metaboxes/fields/file/index.js:11
msgid "Use File"
msgstr "Използвай файла"

#: packages/blocks/fields/file/index.js:17
#: packages/metaboxes/fields/file/index.js:12
msgid "Select File"
msgstr "Избери файл"

#: packages/blocks/fields/image/index.js:11
#: packages/metaboxes/fields/image/index.js:11
msgid "Use Image"
msgstr "Използвай изображението"

#: packages/blocks/fields/image/index.js:12
#: packages/metaboxes/fields/image/index.js:12
msgid "Select Image"
msgstr "Избери изображение"

#: packages/blocks/fields/media-gallery/index.js:16
#: packages/metaboxes/fields/media-gallery/index.js:16
msgid "Use Attachments"
msgstr "Използвай файловете"

#: packages/blocks/fields/media-gallery/index.js:17
#: packages/metaboxes/fields/media-gallery/index.js:17
msgid "Select Attachments"
msgstr "Избери файлове"

#: packages/core/components/no-options/index.js:14
msgid "No options."
msgstr "Не са дефинирани опции."

#: packages/core/components/search-input/index.js:30
msgid "Search..."
msgstr "Търси…"

#: packages/core/fields/association/index.js:168
msgid "Maximum number of items reached (%s items)"
msgstr "Максималният брой на избрани елементи е достигнат (%s)"

#: packages/core/fields/association/index.js:266
msgid "Showing %1$d of %2$d results"
msgstr "Показани %1$d от %2$d резултата"

#: packages/core/fields/association/index.js:458
msgid "An error occurred while trying to fetch association options."
msgstr "Възникна грешка при опита да се получат елементите."

#: packages/core/fields/association/index.js:515
#: packages/core/fields/complex/index.js:428
#: packages/core/hocs/with-validation/required.js:20
msgid "This field is required."
msgstr "Това поле е задължително."

#: packages/core/fields/association/index.js:519
msgid "Minimum number of items not reached (%s items)"
msgstr "Минималния брой на избрани елементи не е достигнат (%s)"

#: packages/core/fields/color/index.js:92
msgid "Select a color"
msgstr "Изберете цвят"

#: packages/core/fields/complex/group.js:154
msgid "Duplicate"
msgstr "Дублирай"

#: packages/core/fields/complex/group.js:163
msgid "Remove"
msgstr "Премахни"

#: packages/core/fields/complex/group.js:172
msgid "Collapse"
msgstr "Свий"

#: packages/core/fields/complex/index.js:146
msgid "Couldn't create the label of group - %s"
msgstr ""

#: packages/core/fields/complex/index.js:344
msgid "There are no entries yet."
msgstr ""

#: packages/core/fields/complex/index.js:401
msgid "Expand All"
msgstr "Разгъни всички"

#: packages/core/fields/complex/index.js:401
msgid "Collapse All"
msgstr "Свий всички"

#: packages/core/fields/complex/index.js:435
msgid "Minimum number of rows not reached (%1$d %2$s)"
msgstr "Минималния брой не е достигнат (%1$d %2$s)"

#: packages/core/fields/complex/index.js:82
msgid "Add %s"
msgstr "Добави %s"

#: packages/core/fields/map/index.js:165
msgid "The address could not be found."
msgstr "Адресът не може да бъде открит."

#: packages/core/fields/map/index.js:167
msgid "Geocode was not successful for the following reason: "
msgstr "Геолокацията не беше успешна:"

#: packages/core/fields/map/index.js:185
msgid "Error alert"
msgstr ""

#: packages/core/fields/oembed/index.js:188
msgid "An error occurred while trying to fetch oembed preview."
msgstr "Възникна грешка при опита да се получи прегледа."

#: packages/core/fields/oembed/index.js:203
msgid "Not Found"
msgstr "Няма резултат"

#: packages/core/fields/rich-text/index.js:123
msgid "Visual"
msgstr "Визуален"

#: packages/core/fields/rich-text/index.js:127
msgid "Text"
msgstr "Текст"

#: packages/core/fields/sidebar/index.js:110
msgid "Please enter the name of the new sidebar:"
msgstr "Моля въведете име за страничната лента:"

#: packages/core/fields/sidebar/index.js:128
msgid "An error occurred while trying to create the sidebar."
msgstr "Възникна грешка при създаването на страничната лента."

#: packages/core/fields/sidebar/index.js:58
msgid "Please choose"
msgstr "Избери"

#: packages/core/fields/time/index.js:16
msgid "Select Time"
msgstr "Избери време"

#: packages/core/hocs/with-conditional-logic/index.js:69
msgid "An unknown field is used in condition - \"%s\""
msgstr ""

#: packages/core/registry/index.js:40
msgid "%1$s type must be a string."
msgstr ""

#: packages/core/registry/index.js:46
msgid "%1$s %2$s is already registered."
msgstr ""

#: packages/core/registry/index.js:54
msgid "The \"component\" param must be a function."
msgstr ""

#: packages/core/registry/index.js:79
msgid "The provided context isn't a valid one. Must be one of - %s ."
msgstr ""

#: packages/core/registry/index.js:89
msgid "%s %s isn't registered."
msgstr ""

#: packages/core/utils/api-fetch.js:19
msgid "An error occured."
msgstr "Възникна грешка."

#: packages/core/utils/fetch-attachments-data.js:24
msgid "An error occurred while trying to fetch files data."
msgstr "Възникна грешка при получаването на информацията за файла."

#: packages/metaboxes/containers/index.js:55
msgid "Could not find DOM element for container \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/conditions/base.js:52
msgid "Unsupported container condition comparison operator used - \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/handler/index.js:55
msgid "Unsupported container condition - \"%1$s\"."
msgstr ""

#: packages/metaboxes/monitors/conditional-display/handler/index.js:73
msgid "Unsupported container condition relation used - \"%1$s\"."
msgstr ""
